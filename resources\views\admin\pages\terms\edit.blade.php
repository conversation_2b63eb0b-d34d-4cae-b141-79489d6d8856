@extends('admin.layouts.master')
@section('title', 'Edit Terms and Conditions')
@section('main-section')
    <link rel="stylesheet" href="{{ asset('flatepicker/flatpickr.min.css') }}">
    <script src="{{ asset('js/flatpickr.min.js') }}"></script>

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>

                        <a href="{{ route('terms.index') }}" class="clr hover:underline">Terms and Conditions</a>
                    </li>
                    <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
                        <span>Edit</span>
                    </li>
                </ul>

            </div>
            <div class="mt-3" x-data="form">


                <div class="mb-5">

                    <div class="panel">

                        <div class="mb-5">
                            <form action="{{ route('terms.update', $terms->id) }}" method="POST"
                                class="space-y-5" enctype="multipart/form-data">
                                @csrf
                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <div>
                                        <label class="required-field" for="browserFname">Title</label>
                                        <input id="browserFname" type="text" name="title"
                                            value="{{ old('title', $terms->title) }}" placeholder="Enter title"
                                            class="form-input" />
                                        @error('title')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>











                                </div>
                                <div class="form-group">
                                    <label class="required-field" for="editor"> Description </label>
                                    <textarea id="editor" class="tinymce" name="description">{{ old('description', $terms->description) }}</textarea>
                                </div>
                                <span id="editor-error" class="text-danger"></span>
                                @error('description')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                                <div class="flex justify-end mt-4">
                                    <a href="{{ route('terms.index') }}" class="ifl-cancel btn !mt-6">Cancel</a>

                                    <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>

                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    @endsection

    @section('extra-script')
        <!-- Select2 CSS -->
        <link href="{{ asset('js/select2/select2.min.css') }}" rel="stylesheet" />
        <!-- jQuery (Select2 requires jQuery) -->
        <script src="{{ asset('js/jquery/jquery.min.js') }}"></script>
        <script>
            $(document).ready(function() {
                $('.dropdown').select2();
            });
        </script>

        <script>
            function previewImage(event) {
                const file = event.target.files[0];
                const preview = document.getElementById('imagePreview');

                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('hidden');
                    }

                    reader.readAsDataURL(file);
                } else {
                    preview.src = '';
                    preview.classList.add('hidden');
                }
            }
        </script>




       <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        if (typeof tinymce !== 'undefined') {
            tinymce.init({
                selector: 'textarea.tinymce',
                height: 300,
                menubar: false,
                plugins: 'link lists paste',
                toolbar: 'undo redo | bold italic | blocks | bullist numlist | link',
                block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6',

                // ✅ Keep formatting (not plain text paste)
                paste_as_text: false,

                // ✅ Allow only safe elements
                valid_elements: 'p,h2,h3,strong,em,ul,ol,li,a[href],b,i,u',
                branding: false,
                forced_root_block: 'p',

                // ✅ Clean inline styles but keep HTML tags
                paste_preprocess: function (plugin, args) {
                    args.content = args.content.replace(/ style="[^"]*"/g, '');
                },

                setup: function (editor) {
                    // ✅ Enforce max 2 links from toolbar
                    editor.on('BeforeExecCommand', function (e) {
                        if (e.command === 'mceInsertLink') {
                            const linkCount = editor.getBody().querySelectorAll('a').length;
                            if (linkCount >= 2) {
                                e.preventDefault();
                                alert("Only 2 links are allowed.");
                            }
                        }
                    });

                    // ✅ Prevent pasting too many links
                    editor.on('PastePreProcess', function (e) {
                        const pasted = e.content;
                        const existingLinks = editor.getBody().querySelectorAll('a').length;
                        const newLinks = (pasted.match(/<a\s+(.*?)>/g) || []).length;

                        if (existingLinks + newLinks > 2) {
                            e.preventDefault();
                            alert("You can't paste content that adds more than 2 links.");
                        }
                    });

                    // ✅ Remove extra links if user manages to add manually
                    editor.on('NodeChange', function () {
                        const links = editor.getBody().querySelectorAll('a');
                        if (links.length > 2) {
                            alert('Only 2 links are allowed. Extra links will be removed.');
                            for (let i = 2; i < links.length; i++) {
                                links[i].remove();
                            }
                        }
                    });
                }
            });

            // ✅ Ensure textarea syncs on form submit
            document.querySelector('form').addEventListener('submit', function () {
                tinymce.triggerSave();
            });
        }
    });
</script>

        <script>
            const compressImage = async (file, {
                quality = 1,
                type = file.type
            }) => {
                const imageBitmap = await createImageBitmap(file);

                const canvas = document.createElement('canvas');
                canvas.width = imageBitmap.width;
                canvas.height = imageBitmap.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(imageBitmap, 0, 0);

                const blob = await new Promise((resolve) =>
                    canvas.toBlob(resolve, type, quality)
                );

                return new File([blob], file.name, {
                    type: blob.type,
                });
            };

            const input = document.querySelector('.my-image-field');
            input.addEventListener('change', async (e) => {
                const {
                    files
                } = e.target;

                if (!files.length) return;

                const dataTransfer = new DataTransfer();

                for (const file of files) {
                    if (!file.type.startsWith('image')) {
                        dataTransfer.items.add(file);
                        continue;
                    }

                    const compressedFile = await compressImage(file, {
                        quality: 0.5,
                        type: 'image/jpeg',
                    });

                    dataTransfer.items.add(compressedFile);
                }

                e.target.files = dataTransfer.files;
            });
        </script>

    @endsection
