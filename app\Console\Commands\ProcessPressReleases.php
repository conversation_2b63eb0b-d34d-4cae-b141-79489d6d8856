<?php

namespace App\Console\Commands;

use App\Models\PressRelease;
use App\Mail\StatusChangedMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use OpenAI; // If you're using OpenAI PHP SDK

class ProcessPressReleases extends Command
{
    protected $signature = 'pressrelease:process';
    protected $description = 'Process 2 pending press releases every 10 minutes';

    public function handle()
    {
        $releases = PressRelease::where('status', 'pending')->limit(2)->get();

        foreach ($releases as $release) {
            $prompt = $this->buildPrompt($release);

            $response = Http::withOptions(['verify' => false])->withHeaders([
            'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', [
            'model' => 'gpt-4o',
            'messages' => [
                ['role' => 'system', 'content' => 'You are a press release validator.'],
                ['role' => 'user', 'content' => $prompt],
            ],
        ]);

        // Handle API failure gracefully
        if (!$response->ok()) {
            \Log::error('OpenAI API error', ['response' => $response->body()]);
            continue; // skip to next release
        }

            $reply = strtolower($response['choices'][0]['message']['content']);

            if (str_contains($reply, 'reject') || str_contains($reply, 'violation')) {
                $release->status = 'rejected';
                $release->reject_reason = $reply;
                $release->save();

                // Mail::to($release->user->email)->send(new \App\Mail\PRRejected($release));
                Mail::to($release->email)->send(new StatusChangedMail($release, $release->status));
            } else {
                $release->status = 'approved';
                $release->save();
                Mail::to($release->email)->send(new StatusChangedMail($release, $release->status));
            }
        }
    }

  protected function buildPrompt($release)
{
    return <<<PROMPT
Please validate the following press release against the submission guidelines below.

--- Start Press Release ---

Title: {$release->press_release_title}

Description:
{$release->press_release_description}

Submitted By: {$release->name}
Company: {$release->company_name}
PR Agency: {$release->pr_agency}
Category: {$release->category}

--- End Press Release ---

Now, validate it according to these rules:

Press Release Submission Guidelines

Content Requirements
* Must read as newsworthy and objective.
* Should neither sound overly promotional nor negative.
* May not contain “reviews.”
* Must not allege illegal or unethical conduct, breach of contract, or threaten litigation.

Prohibited Topics
* Drugs & Substances: Recreational or psychotropic compounds (e.g., cannabis, kratom, THC).
* Finance & Trading Schemes: Unverified or unregulated offerings (crypto, NFTs, DeFi, FOREX, cloud mining, get-rich-quick).
* Weapons & Violence: Explosives, firearms, weapons tutorials, graphic violence or gore.
* Gambling & Betting
* Adult Content: Nudity, sexual or erotic material, content involving minors.
* Hate & Extremism: Hate speech, extremist ideology, or targeted discrimination.
* Defamation & Privacy Violations
* Self-Harm & Suicide
* Medical & Legal Advice
* Scams & Fraud: Payday loans, bail bonds, pyramid schemes, identity theft, credit-repair services.
* Affiliate/Spam Links: Affiliate marketing “hoplinks,” social-media like-buying services, malware-flagged URLs.
* Cosmetic & Body Modification: Cosmetic procedures, ED treatments, breast enhancement, massage parlors.
* Pseudo-Religious & Occult: Amulets, charms, magic rituals.
* Software Piracy & Cybercrime: Cracking/keygens, hacking guides, reverse-engineering, malware/ransomware instructions, phishing or social-engineering tutorials.
* Inauthentic social-media services: Paid likes, followers, views, engagements, or “pods.”
* Copyright Infringement & Pirated Content

Please clearly state whether this press release should be:
- ✅ Accepted (with a short reason)
- ❌ Rejected (with a clear reason and which guideline is violated)

Use professional tone and be direct in your response.
PROMPT;
}

}
