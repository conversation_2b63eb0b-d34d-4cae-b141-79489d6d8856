<?php

namespace App\Http\Controllers\Admin\press;

use Carbon\Carbon;
use App\Models\PressRelease;
use Illuminate\Http\Request;
use App\Mail\StatusChangedMail;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;

class PressReleaseController extends Controller
{
    public function index()
    {
        if (auth()->check() && auth()->user()->role == 'user') {
            $pressReleases = PressRelease::where('user_id', Auth::id())->latest()->get();
        } else {
            $pressReleases = PressRelease::latest()->get();
        }


        return view('admin.pages.press.index', compact('pressReleases'));
    }
    public function create()
    {
        return view('admin.pages.press.create');
    }
    public function store(Request $request)
    {
        $alreadyExists = PressRelease::where('user_id', Auth::id())
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->exists();

        if ($alreadyExists) {
            Session::flash('error-toast', 'You have already submitted a press release this month.');
            return redirect()->back();
        }

        // dd($request->all());
          $userEmailDomain = substr(strrchr(Auth::user()->email, "@"), 1);
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
            'required',
            'email',
            'max:255',
            function ($attribute, $value, $fail) use ($userEmailDomain) {
                $submittedEmailDomain = substr(strrchr($value, "@"), 1);
                if ($submittedEmailDomain !== $userEmailDomain) {
                    $fail("The email must use the same domain as your registered email (@$userEmailDomain).");
                }
            },
        ],
            'pr_agency' => 'nullable|string|max:255',
            'telephone' => 'nullable|string|max:20',
            'company_name' => 'required|string|max:255',
            // Restrict category to dropdown values
            'category' => 'required|in:News,Events,Announcements',
            'press_release_title' => 'required|string|max:255',
            'press_release_description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('press_releases', 'public');
        }

        $data['user_id'] = Auth::id();
        $data['status'] = 'pending';

        PressRelease::create($data);

        Session::flash('success-toast', 'Press release submitted for review.');
        return redirect()->route('press-releases.index');
    }
    public function edit($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.press.edit', compact('pressRelease'));
    }


    public function update(Request $request, $id)
    {
         $user = Auth::user();
    $userEmailDomain = substr(strrchr($user->email, "@"), 1);
        // dd($request->all());
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
            'required',
            'email',
            'max:255',
            function ($attribute, $value, $fail) use ($userEmailDomain) {
                if (substr(strrchr($value, "@"), 1) !== $userEmailDomain) {
                    $fail("The $attribute must be a {$userEmailDomain} email address.");
                }
            }
        ],
            'pr_agency' => 'nullable|string|max:255',
            'telephone' => 'nullable|string|max:20',
            'company_name' => 'required|string|max:255',
            // Restrict category to dropdown values
            'category' => 'required|in:News,Events,Announcements',
            'press_release_title' => 'required|string|max:255',
            'press_release_description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $pressRelease = PressRelease::findOrFail($id);
        // dd($pressRelease);

        // if ($pressRelease->user_id !== Auth::id()) {
        //     abort(403, 'Unauthorized action.');
        // }

        if ($request->hasFile('image')) {
            if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
                unlink(storage_path('app/public/' . $pressRelease->image));
            }
            $data['image'] = $request->file('image')->store('press_releases', 'public');
        } else {
            $data['image'] = $pressRelease->image;
        }

        if (in_array($pressRelease->status, ['approved', 'rejected'])) {
            $data['status'] = 'pending';
            $data['approved_by'] = null;
            $data['approved_at'] = null;
            $data['reject_reason'] = null;
        }

        $pressRelease->update($data);

        Session::flash('success-toast', 'Press release updated successfully.');
        return redirect()->route('press-releases.index');
    }

    public function destroy($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        $pressRelease->delete();

        if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
            unlink(storage_path('app/public/' . $pressRelease->image));
        }
        Session::flash('success-error', 'Press release deleted.');
        return redirect()->route('press-releases.index');
    }
    public function show($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.press.view', compact('pressRelease'));
    }

    // fetch all pending press releases

    public function pending()
    {
        $user = Auth::user();
        if ($user->role == 'user') {
            $pressReleases = PressRelease::where('user_id', Auth::id())->where('status', 'pending')->get();
        } else {
            $pressReleases = PressRelease::where('status', 'pending')->get();
        }
        return view('admin.pages.pending.index', compact('pressReleases'));
    }
    // edit pending press release
    public function editPending($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.pending.edit', compact('pressRelease'));
    }
    // update pending press release
    public function updatePending(Request $request, $id)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'pr_agency' => 'nullable|string|max:255',
            'telephone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'press_release_title' => 'required|string|max:255',
            'press_release_description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        $pressRelease = PressRelease::findOrFail($id);
        if ($pressRelease->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }
        if ($request->hasFile('image')) {
            if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
                unlink(storage_path('app/public/' . $pressRelease->image));
            }
            $data['image'] = $request->file('image')->store('press_releases', 'public');
        } else {
            $data['image'] = $pressRelease->image;
        }
        if (in_array($pressRelease->status, ['approved', 'rejected'])) {
            $data['status'] = 'pending';
            $data['approved_by'] = null;
            $data['approved_at'] = null;
            $data['reject_reason'] = null;
        }
        $pressRelease->update($data);
        Session::flash('success-toast', 'Press release updated successfully.');
        return redirect()->route('press.pending');
    }

    // Delete pending press release
    public function deletePending($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        $pressRelease->delete();

        if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
            unlink(storage_path('app/public/' . $pressRelease->image));
        }
        Session::flash('success-error', 'Press release deleted.');
        return redirect()->route('press.pending');
    }

    // show pending press release
    public function showPending($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.pending.show', compact('pressRelease'));
    }

    // fetch approved press releases
    public function approved()
    {
        $user = Auth::user();
        if ($user->role == 'user') {
            $pressReleases = PressRelease::where('user_id', Auth::id())->where('status', 'approved')->get();
        } else {
            $pressReleases = PressRelease::where('status', 'approved')->get();
        }
        return view('admin.pages.approved.index', compact('pressReleases'));
    }
    // edit approved press release
    public function editApproved($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.approved.edit', compact('pressRelease'));
    }
    public function updateApproved(Request $request, $id)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'pr_agency' => 'nullable|string|max:255',
            'telephone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'press_release_title' => 'required|string|max:255',
            'press_release_description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        $pressRelease = PressRelease::findOrFail($id);
        if ($pressRelease->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }
        if ($request->hasFile('image')) {
            if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
                unlink(storage_path('app/public/' . $pressRelease->image));
            }
            $data['image'] = $request->file('image')->store('press_releases', 'public');
        } else {
            $data['image'] = $pressRelease->image;
        }
        if (in_array($pressRelease->status, ['approved', 'rejected'])) {
            $data['status'] = 'pending';
            $data['approved_by'] = null;
            $data['approved_at'] = null;
            $data['reject_reason'] = null;
        }
        $pressRelease->update($data);
        Session::flash('success-toast', 'Press release  updated successfully.');
        return redirect()->route('presses.approved');
    }
    // Delete approved press release
    public function deleteApproved($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        $pressRelease->delete();
        if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
            unlink(storage_path('app/public/' . $pressRelease->image));
        }
        Session::flash('success-error', 'Press release deleted.');
        return redirect()->route('presses.pending');
    }
    public function showApproved($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.approved.show', compact('pressRelease'));
    }
    // fetch rejected press releases
    public function rejected()
    {
        $user = Auth::user();
        if ($user->role == 'user') {
            $pressReleases = PressRelease::where('user_id', Auth::id())->where('status', 'rejected')->get();
        } else {
            $pressReleases = PressRelease::where('status', 'rejected')->get();
        }
        return view('admin.pages.rejected.index', compact('pressReleases'));
    }
    // edit rejected press release
    public function editRejected($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.rejected.edit', compact('pressRelease'));
    }
    public function updateRejected(Request $request, $id)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'pr_agency' => 'nullable|string|max:255',
            'telephone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'press_release_title' => 'required|string|max:255',
            'press_release_description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        $pressRelease = PressRelease::findOrFail($id);
        if ($pressRelease->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }
        if ($request->hasFile('image')) {
            if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
                unlink(storage_path('app/public/' . $pressRelease->image));
            }
            $data['image'] = $request->file('image')->store('press_releases', 'public');
        } else {
            $data['image'] = $pressRelease->image;
        }
        if (in_array($pressRelease->status, ['approved', 'rejected'])) {
            $data['status'] = 'pending';
            $data['approved_by'] = null;
            $data['approved_at'] = null;
            $data['reject_reason'] = null;
        }
        $pressRelease->update($data);
        Session::flash('success-toast', 'Press release updated successfully.');
        return redirect()->route('pressing.rejected');
    }
    public function deleteRejected($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        $pressRelease->delete();

        if ($pressRelease->image && file_exists(storage_path('app/public/' . $pressRelease->image))) {
            unlink(storage_path('app/public/' . $pressRelease->image));
        }
        Session::flash('success-error', 'Press release deleted.');
        return redirect()->route('pressing.rejected');
    }
    public function showRejected($id)
    {
        $pressRelease = PressRelease::findOrFail($id);
        return view('admin.pages.rejected.show', compact('pressRelease'));
    }

    // PressReleaseController.php

    public function updateStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:press_releases,id',
            'status' => 'required|in:pending,approved,rejected',
            'reason' => 'required_if:status,rejected|string|nullable',
        ]);

        $pressRelease = PressRelease::find($request->id);
        $pressRelease->status = $request->status;

        if ($request->status === 'rejected') {
            $pressRelease->reject_reason = $request->reason;
        } else {
            $pressRelease->reject_reason = null;
        }
        $pressRelease->approved_by = Auth::id();
        $pressRelease->save();
        Mail::to($pressRelease->email)->send(new StatusChangedMail($pressRelease, $request->status));
        //   if (!empty($pressRelease->email)) {
        //             Mail::to($pressRelease->email)->send(new StatusChangedMail($pressRelease));
        //         }
        return response()->json(['message' => 'Status updated successfully']);
    }
}
