<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class StatusChangedMail extends Mailable
{
    use Queueable, SerializesModels;
     public $pressRelease;
     public $status;
     public $reason;

    /**
     * Create a new message instance.
     */
    public function __construct($pressRelease,$status)
    {
        $this->pressRelease = $pressRelease;
        $this->status = $status;
         $this->reason = $release->reject_reason ?? null;

    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Status Changed Mail',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
       return new Content(
        view: 'email.status_changed',
        with: [
            'status' => $this->pressRelease->status,
            'title' => $this->pressRelease->press_release_title,
            'reason' => $this->reason,
        ]
    );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
