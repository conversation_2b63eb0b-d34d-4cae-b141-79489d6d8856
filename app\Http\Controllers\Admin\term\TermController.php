<?php

namespace App\Http\Controllers\Admin\term;

use App\Models\Terms;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;


class TermController extends Controller
{
    public function index()
    {
       $terms = Terms::latest()->get();
        return view('admin.pages.terms.index', compact('terms'));
    }
    public function create()
    {
        $terms = Terms::first();
        return view('admin.pages.terms.create',compact('terms'));
    }
    // public function store(Request $request)
    // {
    //     // dd($request->all());
    //     $request->validate([
    //         'title' => 'required|string|max:255',
    //         'description' => 'required|string',
    //         'is_active' => 'boolean',
    //     ]);

    //     Terms::create($request->all());

    //    Session::flash('success-toast', 'Terms created successfully.');
    //     return redirect()->route('terms.index');
    // }
  public function store(Request $request)
{
    $request->validate([
        'title' => 'required|string|max:255',
        'description' => 'required|string',
        'is_active' => 'boolean',
    ]);

    // Check if first Terms record exists
    $terms = Terms::first();

    if ($terms) {
        // Update the first record
        $terms->update([
            'title' => $request->title,
            'description' => $request->description,
            'is_active' => $request->is_active ?? false,
        ]);
    } else {
        // Create new record if none exists
        Terms::create([
            'title' => $request->title,
            'description' => $request->description,
            'is_active' => $request->is_active ?? false,
        ]);
    }

    Session::flash('success-toast', 'Terms saved successfully.');
    return redirect()->back();
}


    public function edit($id)
    {
        $terms = Terms::findOrFail($id);
        return view('admin.pages.terms.edit', compact('terms'));
    }
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'is_active' => 'boolean',
        ]);

        $term = Terms::findOrFail($id);
        $term->update($request->all());

        Session::flash('success-toast', 'Terms updated successfully.');
        return redirect()->route('terms.index');
    }
    public function destroy($id)
    {
        $term = Terms::findOrFail($id);
        $term->delete();

        Session::flash('success-toast', 'Terms deleted successfully.');
        return redirect()->route('terms.index');
    }
    public function show()
    {
        $term = Terms::first();
        return view('admin.pages.terms.show', compact('term'));
    }
}
