<?php

namespace App\Http\Controllers\Admin\contact;

use App\Models\contact;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class ContactController extends Controller
{
    public function index()
    {
        $contacts = contact::latest()->get();
        return view('admin.pages.contact.index', compact('contacts'));
    }
    public function create()
    {
        $contacts = Contact::first();
        return view('admin.pages.contact.create',compact('contacts'));
    }
    // public function store(Request $request)
    // {
    //    $request->validate([
    //         'name' => 'required',
    //         'email' => 'required|email',
    //         'subject' => 'required',
    //         'message' => 'required',
    //     ]);

    //     Contact::create($request->all());
    //     Session::flash('success-toast', 'Contact message created successfully.');
    //     return redirect()->route('contact.index');
    // }
    public function store(Request $request)
{
    $request->validate([
        'name' => 'required',
        'email' => 'required|email',
        'address'=> 'required|string',
        'phone' => 'required|string',
        'whatsapp'=> 'required|url',
        'facebook'=> 'required|url',
        'instagram'=>'required|url',
        'linkedin'=> 'required|url'

    ]);

    $contact = Contact::first();

    if ($contact) {
        $contact->update($request->all());
        Session::flash('success-toast', 'Contact message updated successfully.');
    } else {
        Contact::create($request->all());
        Session::flash('success-toast', 'Contact message created successfully.');
    }

    return redirect()->back();
}

    public function edit($id)
    {
        $contact = contact::findOrFail($id);
        return view('admin.pages.contact.edit', compact('contact'));
    }
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $contact = contact::findOrFail($id);
        $contact->update($request->all());
        Session::flash('success-toast', 'Contact message updated successfully.');
        return redirect()->route('contact.index');

    }
    public function destroy($id)
    {
        $contact = contact::findOrFail($id);
        $contact->delete();

        Session::flash('success-toast', 'Contact message deleted successfully.');
        return redirect()->route('contact.index');
    }
    public function show()
    {
        $contact = contact::first();
        return view('admin.pages.contact.show', compact('contact'));
    }
}
