{{-- <x-guest-layout>
    <div class="mb-4 text-sm text-gray-600">
        {{ __('Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form method="POST" action="{{ route('password.email') }}">
        @csrf

        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email')" />
            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autofocus />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <div class="flex items-center justify-end mt-4">
            <x-primary-button>
                {{ __('Email Password Reset Link') }}
            </x-primary-button>
        </div>
    </form>
</x-guest-layout> --}}


<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Forgot your password? - SubmitPressRelease.org</title>
    <meta name="description" content="Lost your password? Quickly reset it and get back into our platform to manage and distribute your press releases without delay." />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.png" />
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" media="screen" href="{{ asset('admin/assets/css/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('css/toastr.min.css') }}">
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
    <style>
        .btn-gradient:hover {
            background: #1968b3;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.22);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .txt-red {
            color: #dc3545;
            font-weight: bold;
        }

        .login-card {
            width: 650px !important;
        }
    </style>
</head>
<body class="relative overflow-x-hidden font-nunito text-sm font-normal antialiased">

<div class="main-container min-h-screen text-black dark:text-white-dark">
    <div class="absolute inset-0" style="background-color: #1968B3;"></div>

    <div class="relative flex min-h-screen items-center justify-center bg-cover bg-center bg-no-repeat px-6 py-10 dark:bg-[#060818] sm:px-16">
        <div class="login-card relative w-full max-w-[870px] rounded-md bg-white p-2 dark:bg-black/30">
            <div class="relative flex flex-col justify-center rounded-md backdrop-blur-lg dark:bg-black/50 py-8 mx-auto glass-effect">
                <div class="flex items-center justify-center space-x-4 mb-6">
                    <img src="{{ asset('admin/assets/images/faq/submit press release logo.png') }}" alt="Logo" width="300px" height="300px" />
                </div>

                <div class="mx-auto w-full max-w-[440px]">
                    <h1 class="text-xl  md:text-4xl lg:text-5xl mb-4" style="color: #1968b3;">
                        Forgot Password
                    </h1>

                    <p class="text-base font-bold leading-normal mb-6" style="color: #003b75;">
                        Forgot your password? No problem. Just let us know your email address and we will email you a password reset link.
                    </p>

                    <!-- Session Status -->
                    @if (session('status'))
                        <div class="text-green-600 font-semibold mb-4">
                            {{ session('status') }}
                        </div>
                    @endif

                    <!-- Forgot Password Form -->
                    <form method="POST" action="{{ route('password.email') }}" class="space-y-5 dark:text-white">
                        @csrf
                        <!-- Email -->
                        <div>
                            <label for="email" class="block font-semibold mb-1">Email</label>
                            <input id="email" type="email" name="email" placeholder="Enter your email"
                                   value="{{ old('email') }}"
                                   class="form-input w-full placeholder:text-white-dark ps-4" required autofocus>
                            @error('email')
                                <span class="txt-red">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mt-6">
                            <button type="submit"
                                    class="btn btn-gradient w-full border-0 uppercase shadow-[0_10px_20px_-10px_rgba(67,97,238,0.44)]"
                                    style="background: linear-gradient(90deg, #1968b3 0%, #1968b3 100%);"
                                    onmouseover="this.style.background='linear-gradient(90deg, #539bd8 0%, #539bd8 100%)'"
                                    onmouseout="this.style.background='linear-gradient(90deg, #1968b3 0%, #1968b3 100%)'">
                                Email Password Reset Link
                            </button>
                        </div>

                        <div class="mt-4 text-center">
                            <a href="{{ route('login') }}" class="underline text-sm text-gray-600 hover:text-gray-900">
                                Back to Login
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ asset('js/jquery/jquery-3.6.0.min.js') }}"></script>
<script src="{{ asset('js/toastr.min.js') }}"></script>
<script>
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "timeOut": "5000"
    };

    $(document).ready(function () {
        @if (Session::has('success-toast'))
            toastr.success("{{ Session::get('success-toast') }}");
        @endif

        @if (Session::has('error-toast'))
            toastr.error("{{ Session::get('error-toast') }}");
        @endif
    });
</script>
</body>
</html>

