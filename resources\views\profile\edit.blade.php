@extends('admin.layouts.master')

@section('title', 'Profile Settings')

@section('extra-css')
    <link href="/css/toastr.min.css" rel="stylesheet">
    <style>
        .hidden {
            display: none;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }

        .modal-content {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
        }
    </style>
@endsection

@section('main-section')
    <ul class="flex space-x-2 rtl:space-x-reverse">
        <li>
            <a href="{{ route('dashboard') }}" class="clr hover:underline">Dashboard</a>
        </li>
        <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
            <span>Profile Settings</span>
        </li>
    </ul>

    <div class="pt-5">
        <div x-data="{ tab: 'home' }">
            <template x-if="tab === 'home'">
                <div>
                    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('patch')
                        <h6 class="mb-5 text-lg font-bold">Admin Information</h6>
                        <div class="flex flex-col sm:flex-row">

                            <div class="grid flex-1 grid-cols-1 gap-5 sm:grid-cols-2">
                                <div>
                                    <label for="name">Full Name</label>
                                    <input id="name" name="name" type="text" value="{{ old('name', $user->name) }}" placeholder="Enter name"
                                        class="form-input" />
                                </div>
                                <div>
                                    <label for="email">Email</label>
                                    <input id="email" type="email" name="email" value="{{ old('email', $user->email) }}"
                                        class="form-input" placeholder="Enter email" />
                                    @error('email')
                                        <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div>

                                <div class="mt-4">
                                    <button type="submit" class="ifl btn !mt-6">Save</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </template>
        </div>
    </div>




@endsection


