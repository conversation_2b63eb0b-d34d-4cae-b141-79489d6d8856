@extends('admin.layouts.master')
@section('title', 'Change Password')
@section('main-section')

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>
                        <a href="" class="clr hover:underline">Change Password</a>
                    </li>
                </ul>
            </div>

            <div class="mt-3" x-data="form">
                <div class="mb-5">
                    <div class="panel">
                        <div class="mb-5">
                            <form method="POST" action="{{ route('passwords.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <!-- Old Password Field -->
                                    <div class="form-group">
                                        <label for="old_password">Old Password</label>
                                        <input type="password" id="old_password" name="old_password" class="form-input"
                                            placeholder="Enter old password" required>
                                        @error('old_password')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <!-- New Password Field -->
                                    <div class="form-group">
                                        <label for="new_password">New Password</label>
                                        <input type="password" id="new_password" name="new_password" class="form-input"
                                            placeholder="Enter new password" required>
                                        @error('new_password')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <!-- Confirm Password Field -->
                                    <div class="form-group">
                                        <label for="new_password_confirmation">Confirm Password</label>
                                        <input type="password" id="new_password_confirmation"
                                            name="new_password_confirmation" class="form-input"
                                            placeholder="Confirm new password" required>
                                    </div>
                                </div>

                                <div class="flex justify-end mt-4">
                                    <a href="{{ route('dashboard') }}" class="ifl-cancel btn !mt-6">Cancel</a>
                                    <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>
                                </div>
                            </form>


                        </div>
                    </div>
                </div>
            </div>

        </div>
    @endsection
