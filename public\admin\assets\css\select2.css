.nice-select {
    float: none !important;
}

.rtl .nice-select {
    text-align: right !important;
    padding-left: 30px;
    padding-right: 18px;
}

.rtl .nice-select::after {
    right: auto;
    left: 12px;
}

.rtl .nice-select .option {
    text-align: right;
}

.dark .nice-select,
.dark .nice-select.open {
    background-color: #1b2e4b;
    border-color: #253b5c !important;
}

.dark .nice-select .nice-select-dropdown {
    background-color: #1b2e4b;
}

.dark .nice-select .option:hover,
.dark .nice-select .option.focus,
.dark .nice-select .option.selected.focus,
.dark .nice-select .nice-select-search {
    background-color: #132136;
    border-color: #253b5c !important;
    color:#888ea8;
}

.nice-select .nice-select-search {
    margin: 0px !important;
}

.nice-select .nice-select-dropdown {
    width: 100%;
    transform: translateY(5px) !important;
    transition: all .2s cubic-bezier(0.4, 0.35, 0.38, 0.39), opacity 0.2s ease-out !important;
}

.nice-select.open .nice-select-dropdown {
    transform: scale(1) translateY(0) !important;
}

.nice-select.open {
    border-color: #e0e6ed !important;
}