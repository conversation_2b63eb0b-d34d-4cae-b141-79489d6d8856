(()=>{function b(i){i.directive("mask",(e,{value:t,expression:u},{effect:s,evaluateLater:f})=>{let n=()=>u,o="";if(["function","dynamic"].includes(t)){let r=f(u);s(()=>{n=a=>{let c;return i.dontAutoEvaluateFunctions(()=>{r(d=>{c=typeof d=="function"?d(a):d},{scope:{$input:a,$money:I.bind({el:e})}})}),c},l(e)})}else l(e);e.addEventListener("input",()=>l(e)),e.addEventListener("blur",()=>l(e,!1));function l(r,a=!0){let c=r.value,d=n(c);if(o.length-r.value.length==1)return o=r.value;let h=()=>{o=r.value=p(c,d)};a?k(r,d,()=>{h()}):h()}function p(r,a){if(r==="")return"";let c=g(a,r);return v(a,c)}})}function k(i,e,t){let u=i.selectionStart,s=i.value;t();let f=s.slice(0,u),n=v(e,g(e,f)).length;i.setSelectionRange(n,n)}function g(i,e){let t=e,u="",s={"9":/[0-9]/,a:/[a-zA-Z]/,"*":/[a-zA-Z0-9]/},f="";for(let n=0;n<i.length;n++){if(["9","a","*"].includes(i[n])){f+=i[n];continue}for(let o=0;o<t.length;o++)if(t[o]===i[n]){t=t.slice(0,o)+t.slice(o+1);break}}for(let n=0;n<f.length;n++){let o=!1;for(let l=0;l<t.length;l++)if(s[f[n]].test(t[l])){u+=t[l],t=t.slice(0,l)+t.slice(l+1),o=!0;break}if(!o)break}return u}function v(i,e){let t=Array.from(e),u="";for(let s=0;s<i.length;s++){if(!["9","a","*"].includes(i[s])){u+=i[s];continue}if(t.length===0)break;u+=t.shift()}return u}function I(i,e=".",t){t=e===","&&t===void 0?".":",";let u=(n,o)=>{let l="",p=0;for(let r=n.length-1;r>=0;r--)n[r]!==o&&(p===3?(l=n[r]+o+l,p=0):l=n[r]+l,p++);return l},s=i.replaceAll(t,""),f=Array.from({length:s.split(e)[0].length}).fill("9").join("");return f=u(f,t),i.includes(e)&&(f+=`${e}99`),queueMicrotask(()=>{this.el.value.endsWith(e)||this.el.value[this.el.selectionStart-1]===e&&this.el.setSelectionRange(this.el.selectionStart-1,this.el.selectionStart-1)}),f}document.addEventListener("alpine:init",()=>{window.Alpine.plugin(b)});})();
