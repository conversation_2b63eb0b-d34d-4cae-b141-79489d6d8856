@extends('admin.layouts.master')

@section('title', 'View Press Release')

@section('main-section')
<link href="{{asset('css/tailwind/tailwind.min.css')}}" rel="stylesheet">

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <header class="mb-3">
            <a href="#" class="burger-btn d-block d-xl-none">
                <i class="bi bi-justify text-3xl"></i>
            </a>
        </header>

        <div class="page-heading">
            <div class="panel flex items-center justify-between p-3">
                <!-- Survey and Polls Text -->
                <span class="font-bold ltr:mr-3 rtl:ml-3">View Press Release</span>

                <!-- Back Button -->
                <a href="{{ route('press-releases.index') }}" class="ifl btn">Back</a>
            </div>


            <section class="section mt-6">
                <div class="panel bg-white shadow-md rounded-lg p-6 space-y-6">
                    @if ($pressRelease->image != null)
                        <div class="relative cursor-pointer"
                            onclick="showImageModal('{{ asset('storage/' . $pressRelease->image) }}')">
                            <img src="{{ asset('storage/' . $pressRelease->image) }}" alt="Press Release Image"
                                class="object-cover object-center rounded shadow-md w-40 h-40">
                        </div>
                    @else
                        <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}" alt="Default Image"
                            class="rounded shadow-md w-40 h-40">
                    @endif

                    <!-- Fullscreen Image Modal -->
                    <div id="imageModal"
                        class="fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex items-center justify-center transition-all duration-300">
                        <div class="absolute top-4 right-6">
                            <button onclick="closeImageModal()"
                                class="text-white text-4xl font-bold hover:text-gray-300">&times;</button>
                        </div>
                        <img id="modalImage" src=""
                            class="max-w-4xl max-h-[90vh] rounded-lg shadow-xl border border-white" alt="Full Image">
                    </div>



                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="text-lg font-semibold">Name</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->name }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">Email</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->email }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">PR Agency</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->pr_agency }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">Telephone</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->telephone }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">Company Name</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->company_name }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">Category</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->category }}</p>
                        </div>
                         <div>
                            <label class="text-lg font-semibold">Press Release Title</label>
                            <p class="block text-sm font-medium text-gray-700">{{ $pressRelease->press_release_title }}</p>
                        </div>


                        <div class="col-span-2">
                            <label class="text-lg font-semibold">Press Release Detail</label>
                            <p class="block text-sm font-medium text-gray-700"> {!! $pressRelease->press_release_description !!}</p>
                        </div>




                    </div>

                </div>
            </section>
        </div>
    </div>
@endsection
