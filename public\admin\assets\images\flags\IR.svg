<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>IR</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#38BB56" offset="0%"></stop>
            <stop stop-color="#2B9F45" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#DE1B27" offset="0%"></stop>
            <stop stop-color="#D80915" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="IR">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="5"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="0" y="10" width="21" height="5"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-1)" x="0" y="5" width="21" height="5"></rect>
            <path d="M1,4.25234222 C1,4.11297746 1.10701752,4 1.25234222,4 L1.74765778,4 C1.88702254,4 2,4.10701752 2,4.25234222 L2,4.74765778 C2,4.88702254 1.89298248,5 1.74765778,5 L1.25234222,5 C1.11297746,5 1,4.89298248 1,4.74765778 L1,4.25234222 Z M3,4.25234222 C3,4.11297746 3.10701752,4 3.25234222,4 L3.74765778,4 C3.88702254,4 4,4.10701752 4,4.25234222 L4,4.74765778 C4,4.88702254 3.89298248,5 3.74765778,5 L3.25234222,5 C3.11297746,5 3,4.89298248 3,4.74765778 L3,4.25234222 Z M5,4.25234222 C5,4.11297746 5.10701752,4 5.25234222,4 L5.74765778,4 C5.88702254,4 6,4.10701752 6,4.25234222 L6,4.74765778 C6,4.88702254 5.89298248,5 5.74765778,5 L5.25234222,5 C5.11297746,5 5,4.89298248 5,4.74765778 L5,4.25234222 Z M7,4.25234222 C7,4.11297746 7.10701752,4 7.25234222,4 L7.74765778,4 C7.88702254,4 8,4.10701752 8,4.25234222 L8,4.74765778 C8,4.88702254 7.89298248,5 7.74765778,5 L7.25234222,5 C7.11297746,5 7,4.89298248 7,4.74765778 L7,4.25234222 Z M9,4.25234222 C9,4.11297746 9.10701752,4 9.25234222,4 L9.74765778,4 C9.88702254,4 10,4.10701752 10,4.25234222 L10,4.74765778 C10,4.88702254 9.89298248,5 9.74765778,5 L9.25234222,5 C9.11297746,5 9,4.89298248 9,4.74765778 L9,4.25234222 Z M11,4.25234222 C11,4.11297746 11.1070175,4 11.2523422,4 L11.7476578,4 C11.8870225,4 12,4.10701752 12,4.25234222 L12,4.74765778 C12,4.88702254 11.8929825,5 11.7476578,5 L11.2523422,5 C11.1129775,5 11,4.89298248 11,4.74765778 L11,4.25234222 Z M13,4.25234222 C13,4.11297746 13.1070175,4 13.2523422,4 L13.7476578,4 C13.8870225,4 14,4.10701752 14,4.25234222 L14,4.74765778 C14,4.88702254 13.8929825,5 13.7476578,5 L13.2523422,5 C13.1129775,5 13,4.89298248 13,4.74765778 L13,4.25234222 Z M15,4.25234222 C15,4.11297746 15.1070175,4 15.2523422,4 L15.7476578,4 C15.8870225,4 16,4.10701752 16,4.25234222 L16,4.74765778 C16,4.88702254 15.8929825,5 15.7476578,5 L15.2523422,5 C15.1129775,5 15,4.89298248 15,4.74765778 L15,4.25234222 Z M17,4.25234222 C17,4.11297746 17.1070175,4 17.2523422,4 L17.7476578,4 C17.8870225,4 18,4.10701752 18,4.25234222 L18,4.74765778 C18,4.88702254 17.8929825,5 17.7476578,5 L17.2523422,5 C17.1129775,5 17,4.89298248 17,4.74765778 L17,4.25234222 Z M19,4.25234222 C19,4.11297746 19.1070175,4 19.2523422,4 L19.7476578,4 C19.8870225,4 20,4.10701752 20,4.25234222 L20,4.74765778 C20,4.88702254 19.8929825,5 19.7476578,5 L19.2523422,5 C19.1129775,5 19,4.89298248 19,4.74765778 L19,4.25234222 Z M1,10.2523422 C1,10.1129775 1.10701752,10 1.25234222,10 L1.74765778,10 C1.88702254,10 2,10.1070175 2,10.2523422 L2,10.7476578 C2,10.8870225 1.89298248,11 1.74765778,11 L1.25234222,11 C1.11297746,11 1,10.8929825 1,10.7476578 L1,10.2523422 Z M3,10.2523422 C3,10.1129775 3.10701752,10 3.25234222,10 L3.74765778,10 C3.88702254,10 4,10.1070175 4,10.2523422 L4,10.7476578 C4,10.8870225 3.89298248,11 3.74765778,11 L3.25234222,11 C3.11297746,11 3,10.8929825 3,10.7476578 L3,10.2523422 Z M5,10.2523422 C5,10.1129775 5.10701752,10 5.25234222,10 L5.74765778,10 C5.88702254,10 6,10.1070175 6,10.2523422 L6,10.7476578 C6,10.8870225 5.89298248,11 5.74765778,11 L5.25234222,11 C5.11297746,11 5,10.8929825 5,10.7476578 L5,10.2523422 Z M7,10.2523422 C7,10.1129775 7.10701752,10 7.25234222,10 L7.74765778,10 C7.88702254,10 8,10.1070175 8,10.2523422 L8,10.7476578 C8,10.8870225 7.89298248,11 7.74765778,11 L7.25234222,11 C7.11297746,11 7,10.8929825 7,10.7476578 L7,10.2523422 Z M9,10.2523422 C9,10.1129775 9.10701752,10 9.25234222,10 L9.74765778,10 C9.88702254,10 10,10.1070175 10,10.2523422 L10,10.7476578 C10,10.8870225 9.89298248,11 9.74765778,11 L9.25234222,11 C9.11297746,11 9,10.8929825 9,10.7476578 L9,10.2523422 Z M11,10.2523422 C11,10.1129775 11.1070175,10 11.2523422,10 L11.7476578,10 C11.8870225,10 12,10.1070175 12,10.2523422 L12,10.7476578 C12,10.8870225 11.8929825,11 11.7476578,11 L11.2523422,11 C11.1129775,11 11,10.8929825 11,10.7476578 L11,10.2523422 Z M13,10.2523422 C13,10.1129775 13.1070175,10 13.2523422,10 L13.7476578,10 C13.8870225,10 14,10.1070175 14,10.2523422 L14,10.7476578 C14,10.8870225 13.8929825,11 13.7476578,11 L13.2523422,11 C13.1129775,11 13,10.8929825 13,10.7476578 L13,10.2523422 Z M15,10.2523422 C15,10.1129775 15.1070175,10 15.2523422,10 L15.7476578,10 C15.8870225,10 16,10.1070175 16,10.2523422 L16,10.7476578 C16,10.8870225 15.8929825,11 15.7476578,11 L15.2523422,11 C15.1129775,11 15,10.8929825 15,10.7476578 L15,10.2523422 Z M17,10.2523422 C17,10.1129775 17.1070175,10 17.2523422,10 L17.7476578,10 C17.8870225,10 18,10.1070175 18,10.2523422 L18,10.7476578 C18,10.8870225 17.8929825,11 17.7476578,11 L17.2523422,11 C17.1129775,11 17,10.8929825 17,10.7476578 L17,10.2523422 Z M19,10.2523422 C19,10.1129775 19.1070175,10 19.2523422,10 L19.7476578,10 C19.8870225,10 20,10.1070175 20,10.2523422 L20,10.7476578 C20,10.8870225 19.8929825,11 19.7476578,11 L19.2523422,11 C19.1129775,11 19,10.8929825 19,10.7476578 L19,10.2523422 Z" id="Rectangle-223" fill-opacity="0.5" fill="#FFFFFF"></path>
            <path d="M9.59127226,6.64164167 C9.37408527,6.87130071 9.25,7.17543058 9.25,7.5 C9.25,8.19035594 9.80964406,8.75 10.5,8.75 C11.1903559,8.75 11.75,8.19035594 11.75,7.5 C11.75,7.18215258 11.6310412,6.88383389 11.4218262,6.65571799 L9.59127226,6.64164167 Z" id="Oval-1" stroke="#D80915" stroke-width="0.5"></path>
            <rect id="Rectangle-243" fill="#D80915" x="10" y="7" width="1" height="2" rx="0.5"></rect>
        </g>
    </g>
</svg>