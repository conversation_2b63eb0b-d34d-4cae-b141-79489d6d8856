<link rel="icon" type="image/x-icon" href="favicon.png" />
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
{{-- <link href="css/fonts.css" rel="stylesheet" /> --}}

<link rel="stylesheet" href="{{asset('css/toastr.min.css')}}">

    <link rel="stylesheet" href="{{ asset('fontawesome/css/all.min.css') }}">

<link rel="stylesheet" type="text/css" media="screen"
    href="{{ asset('admin/assets/css/perfect-scrollbar.min.css') }}" />
<link rel="stylesheet" type="text/css" media="screen" href="{{ asset('admin/assets/css/style.css') }}" />
<link rel="stylesheet" href="{{ asset('admin/assets/css/highlight.min.css') }}" />
<link defer rel="stylesheet" type="text/css" media="screen" href="{{ asset('admin/assets/css/animate.css') }}" />
<script src="{{ asset('admin/assets/js/perfect-scrollbar.min.js') }}"></script>
<script defer src="{{ asset('admin/assets/js/popper.min.js') }}"></script>
<script defer src="{{ asset('admin/assets/js/tippy-bundle.umd.min.js') }}"></script>
<script defer src="{{ asset('admin/assets/js/sweetalert.min.js') }}"></script>
<link rel="stylesheet" type="text/css" href="{{ asset('admin/assets/css/quill.snow.css') }}" />

<script src="{{asset('js/jquery/jquery-3.6.0.min.js')}}"></script>


<style>
    .ifl {
        color: white !important;
        background-color: #1968b3 !important;
    }
    .ifl-cancel{
        padding: 12px;
        color: white !important;
        background-color: #ff4d4d !important;
    }

    .clr {
        color: black !important;
    }

    .clr:hover {
        color: #1968b3 !important;
    }

    .required-field::after {
        content: " *";
        color: red;
    }

    .customBlue {
        color: #1968b3 !important;
    }

    .customBlue:hover {
        color: #1968b3 !important;
    }

    .news-title {
        word-wrap: break-word !important;
        white-space: normal !important;
    }

    .required-field::after {
        content: " *";
        color: red;
    }
    .add{
            margin-left: 619px  !important;
        }


        .dataTable-pagination .pagination-button {
    background-color: #1968b3;
    color: white;
    border: 1px solid #1968b3;
    border-radius: 4px;
    padding: 5px 10px;
    margin: 0 5px;
    cursor: pointer;
}

.dataTable-pagination .active {
    color: white;
    font-weight: bold;
}

.dataTable-pagination .pagination-button:hover {
    background-color: #1968b3;
    color: #ffffff;
}


</style>
