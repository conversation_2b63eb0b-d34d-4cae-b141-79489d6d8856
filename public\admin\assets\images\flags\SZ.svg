<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>SZ</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#486BCA" offset="0%"></stop>
            <stop stop-color="#3E5FBA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFDF20" offset="0%"></stop>
            <stop stop-color="#FFDA00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#CF1615" offset="0%"></stop>
            <stop stop-color="#B20D0C" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="4.5" cy="2.5" rx="4.5" ry="2.5"></ellipse>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="SZ">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="3"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="12" width="21" height="3"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="0" y="3" width="21" height="9"></rect>
            <rect id="Rectangle-2-Copy-4" fill="url(#linearGradient-4)" x="0" y="4" width="21" height="7"></rect>
            <g id="Oval-190" transform="translate(6.000000, 5.000000)">
                <mask id="mask-6" fill="white">
                    <use xlink:href="#path-5"></use>
                </mask>
                <use id="Mask" fill="url(#linearGradient-1)" xlink:href="#path-5"></use>
                <polygon id="Rectangle-1180" fill="url(#linearGradient-7)" mask="url(#mask-6)" transform="translate(2.500000, 2.500000) scale(-1, 1) translate(-2.500000, -2.500000) " points="0 0 5 0 5 5 1 5"></polygon>
                <circle id="Oval-191" fill="#1A1A1A" mask="url(#mask-6)" cx="5.5" cy="2.5" r="1"></circle>
                <circle id="Oval-191" fill="#F6F6F6" mask="url(#mask-6)" cx="3.5" cy="2.5" r="1"></circle>
            </g>
        </g>
    </g>
</svg>