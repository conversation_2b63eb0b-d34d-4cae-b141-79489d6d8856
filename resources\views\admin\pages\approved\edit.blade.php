@extends('admin.layouts.master')
@section('title', 'Edit Press Release')
@section('main-section')
    <link rel="stylesheet" href="{{ asset('flatepicker/flatpickr.min.css') }}">
    <script src="{{ asset('js/flatpickr.min.js') }}"></script>

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>
                        <a href="{{ route('presses.approved') }}" class="clr hover:underline">Press Release</a>
                    </li>
                    <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
                        <span>Edit</span>
                    </li>
                </ul>

            </div>
            <div class="mt-3" x-data="form">


                <div class="mb-5">

                    <div class="panel">

                        <div class="mb-5">
                            <form action="{{ route('presses.updateApproved', $pressRelease->id) }}" method="POST"
                                class="space-y-5" enctype="multipart/form-data">
                                @csrf
                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <div>
                                        <label class="required-field" for="browserFname">Name</label>
                                        <input id="browserFname" type="text" name="name"
                                            value="{{ old('name', $pressRelease->name) }}" placeholder="Enter name"
                                            class="form-input" />
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">Email</label>
                                        <input id="browserFname" type="email" name="email"
                                            value="{{ old('email', $pressRelease->email) }}" placeholder="Enter email"
                                            class="form-input" />
                                        @error('email')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">PR Agency</label>
                                        <input id="browserFname" type="text" name="pr_agency"
                                            value="{{ old('pr_agency', $pressRelease->pr_agency) }}"
                                            placeholder="Enter PR Agency" class="form-input" />
                                        @error('pr_agency')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">Telephone</label>
                                        <input id="browserFname" type="text" name="telephone"
                                            value="{{ old('telephone', $pressRelease->telephone) }}"
                                            placeholder="Enter Telephone" class="form-input" />
                                        @error('telephone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">Company Name</label>
                                        <input id="browserFname" type="text" name="company_name"
                                            value="{{ old('company_name', $pressRelease->company_name) }}"
                                            placeholder="Enter Company Name" class="form-input" />
                                        @error('company_name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>


                                    <div>
                                        <label class="required-field" for="category">Category</label>
                                        <input id="category" type="text"
                                            value="{{ old('category', $pressRelease->category) }}" name="category"
                                            placeholder="Enter category" class="form-input" />
                                        @error('category')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field" for="browserFname">Press Release Title </label>
                                        <input id="browserFname" type="text" name="press_release_title"
                                            value="{{ old('press_release_title', $pressRelease->press_release_title) }}"
                                            placeholder="Enter Press Release Title" class="form-input" />
                                        @error('press_release_title')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field block text-sm font-medium "
                                            for="image">Image</label>

                                        <input type="file" name="image" id="image" accept="image/*"
                                            class="rtl:file-ml-5 form-input p-0 file:border-0 file:/90 file:py-2 file:px-4 file:font-semibold file: file:hover: ltr:file:mr-5 form-input my-image-field @error('image') border-red-500 @enderror"
                                            onchange="previewImage(event)">


                                        @error('image')
                                            <span class="text-red-500 text-sm">{{ $message }}</span>
                                        @enderror

                                        <!-- Image Preview -->
                                        <div class="mt-2">
                                            @if (isset($pressRelease) && $pressRelease->image)
                                                <img id="imagePreview"
                                                    src="{{ asset('storage/' . $pressRelease->image) }}"
                                                    alt="Current Image" class="w-32 h-32 object-cover">
                                            @else
                                                <img id="imagePreview" src="" alt="Image Preview"
                                                    class="hidden w-32 h-32 object-cover">
                                            @endif
                                        </div>

                                    </div>

                                </div>
                                <div class="form-group">
                                    <label class="required-field" for="editor">Press Release Description </label>
                                    <textarea id="editor" class="tinymce" name="press_release_description">{{ old('press_release_description', $pressRelease->press_release_description) }}</textarea>
                                </div>
                                <span id="editor-error" class="text-danger"></span>
                                @error('press_release_description')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                                <div class="flex justify-end mt-4">
                                    <a href="{{ route('press-releases.index') }}" class="ifl-cancel btn !mt-6">Cancel</a>

                                    <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>

                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    @endsection

    @section('extra-script')
        <!-- Select2 CSS -->
        <link href="{{ asset('js/select2/select2.min.css') }}" rel="stylesheet" />
        <!-- jQuery (Select2 requires jQuery) -->
        <script src="{{ asset('js/jquery/jquery.min.js') }}"></script>
        <script>
            $(document).ready(function() {
                $('.dropdown').select2();
            });
        </script>

        <script>
            function previewImage(event) {
                const file = event.target.files[0];
                const preview = document.getElementById('imagePreview');

                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('hidden');
                    }

                    reader.readAsDataURL(file);
                } else {
                    preview.src = '';
                    preview.classList.add('hidden');
                }
            }
        </script>




        <script type="text/javascript">
            tinymce.init({
                selector: 'textarea.tinymce',
                height: 300,
                menubar: false,
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat |',
                content_css: '//www.tiny.cloud/css/codepen.min.css'
            });
        </script>
        <script>
            const compressImage = async (file, {
                quality = 1,
                type = file.type
            }) => {
                const imageBitmap = await createImageBitmap(file);

                const canvas = document.createElement('canvas');
                canvas.width = imageBitmap.width;
                canvas.height = imageBitmap.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(imageBitmap, 0, 0);

                const blob = await new Promise((resolve) =>
                    canvas.toBlob(resolve, type, quality)
                );

                return new File([blob], file.name, {
                    type: blob.type,
                });
            };

            const input = document.querySelector('.my-image-field');
            input.addEventListener('change', async (e) => {
                const {
                    files
                } = e.target;

                if (!files.length) return;

                const dataTransfer = new DataTransfer();

                for (const file of files) {
                    if (!file.type.startsWith('image')) {
                        dataTransfer.items.add(file);
                        continue;
                    }

                    const compressedFile = await compressImage(file, {
                        quality: 0.5,
                        type: 'image/jpeg',
                    });

                    dataTransfer.items.add(compressedFile);
                }

                e.target.files = dataTransfer.files;
            });
        </script>

    @endsection
