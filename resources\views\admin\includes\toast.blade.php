<script src="{{asset('js/jquery/jquery-3.6.0.min.js')}}"></script>
<script src="{{asset('js/toastr.min.js')}}"></script>
<script>
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
</script>
<script>
    $(document).ready(function() {
        @if(Session::has('success-toast'))
            toastr.success("{{ Session::get('success-toast') }}");
        @endif

        @if(Session::has('error-toast'))
            toastr.error("{{ Session::get('error-toast') }}");
        @endif

        @if(Session::has('info-toast'))
            toastr.info("{{ Session::get('info-toast') }}");
        @endif

        @if(Session::has('warning-toast'))
            toastr.warning("{{ Session::get('warning-toast') }}");
        @endif
    });
</script>
