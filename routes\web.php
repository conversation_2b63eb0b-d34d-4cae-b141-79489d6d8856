<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\term\TermController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Admin\contact\ContactController;
use App\Http\Controllers\Admin\press\PressReleaseController;
use Illuminate\Foundation\Exceptions\RegisterErrorViewPaths;
use App\Http\Controllers\Admin\Dashboard\DashboardController;

Route::get('/', function () {
    return view('auth.register');
});
// Auth::routes(['verify' => true]);


// Route::get('/dashboard', function () {
//     return view('admin.pages.dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'dashboard'])->name('dashboard');
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/logout', [ProfileController::class, 'logout'])->name('logout');

    // Press Releases route start here
    Route::get('/press-releases', [PressReleaseController::class, 'index'])->name('press-releases.index');
    Route::get('/press-releases/create', [PressReleaseController::class, 'create'])->name('press-releases.create');
    Route::post('/press-releases', [PressReleaseController::class, 'store'])->name('press-releases.store');
    Route::get('/press-releases/{id}/edit', [PressReleaseController::class, 'edit'])->name('press-releases.edit');
    Route::post('/press-releases/{id}', [PressReleaseController::class, 'update'])->name('press-releases.update');
    Route::delete('/press-releases/{id}', [PressReleaseController::class, 'destroy'])->name('press-releases.destroy');
    Route::get('/press-releases/{id}', [PressReleaseController::class, 'show'])->name('press-releases.show');
    // web.php
Route::post('update-status', [PressReleaseController::class, 'updateStatus'])->name('updateStatus');


    // pending PR route strat here
    Route::get('/press/pending', [PressReleaseController::class, 'pending'])->name('press.pending');
    Route::get('edit-pending/{id}', [PressReleaseController::class, 'editPending'])->name('press.editPending');
    Route::post('/update-pending/{id}', [PressReleaseController::class, 'updatePending'])->name('press.updatePending');
    Route::post('/delete-pending/{id}', [PressReleaseController::class, 'deletePending'])->name('press.deletePending');
    Route::get('/show-pending/{id}', [PressReleaseController::class, 'showPending'])->name('press.showPending');

    // approved PR route start here
    Route::get('/press/approved', [PressReleaseController::class, 'approved'])->name('presses.approved');
    Route::get('/edit-approved/{id}', [PressReleaseController::class, 'editApproved'])->name('presses.editApproved');
    Route::post('/update-approved/{id}', [PressReleaseController::class, 'updateApproved'])->name('presses.updateApproved');
    Route::post('/delete-approved/{id}', [PressReleaseController::class, 'deleteApproved'])->name('presses.deleteApproved');
    Route::get('/show-approved/{id}', [PressReleaseController::class, 'showApproved'])->name('presses.showApproved');

    // rejected PR route start here
    Route::get('/press/rejected', [PressReleaseController::class, 'rejected'])->name('pressing.rejected');
    Route::get('/edit-rejected/{id}', [PressReleaseController::class, 'editRejected'])->name('pressing.editRejected');
    Route::post('/update-rejected/{id}', [PressReleaseController::class, 'updateRejected'])->name('pressing.updateRejected');
    Route::post('/delete-rejected/{id}', [PressReleaseController::class, 'deleteRejected'])->name('pressing.deleteRejected');
    Route::get('/show-rejected/{id}', [PressReleaseController::class, 'showRejected'])->name('pressing.showRejected');

    // term and policy route start here
  // List all terms
    Route::get('/terms', [TermController::class, 'index'])->name('terms.index');
    Route::get('/terms/create', [TermController::class, 'create'])->name('terms.create');
    Route::post('/terms', [TermController::class, 'store'])->name('terms.store');
    Route::get('/terms/{term}/edit', [TermController::class, 'edit'])->name('terms.edit');
    Route::post('/terms/{term}', [TermController::class, 'update'])->name('terms.update');
    Route::delete('/terms/{term}', [TermController::class, 'destroy'])->name('terms.destroy');
    Route::get('/terms', [TermController::class, 'show'])->name('terms.show');


    // Contact us page route start here
    Route::get('/contact',[ContactController::class, 'index'])->name('contact.index');
    Route::get('/contact/create', [ContactController::class, 'create'])->name('contact.create');
    Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
    Route::get('/contact/{id}/edit', [ContactController::class, 'edit'])->name('contact.edit');
    Route::post('/contact/{id}', [ContactController::class, 'update'])->name('contact.update');
    Route::delete('/contact/{id}', [ContactController::class, 'destroy'])->name('contact.destroy');
    Route::get('/contact', [ContactController::class, 'show'])->name('contact.show');


    Route::get('/chnage/password',[DashboardController::class, 'changePassword'])->name('password.change.form');

    // chnage password
// For logged-in user
Route::put('/change-password', [DashboardController::class, 'updatePassword'])->name('passwords.update');


});




Route::get('verify/custom/email/{id}/{hash}', [RegisteredUserController::class, 'verify'])
    ->name('custom.verify.email');


require __DIR__.'/auth.php';
