    <!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Press Release Pro - Professional Press Release Distribution</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <style>
            .gradient-bg {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            .gradient-text {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            .hero-pattern {
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
            }
            .card-hover {
                transition: all 0.3s ease;
            }
            .card-hover:hover {
                transform: translateY(-8px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            .blob {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                animation: blob 7s ease-in-out infinite;
            }
            @keyframes blob {
                0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
                25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
                50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
                75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
                100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
            }
        </style>
    </head>
    <body class="font-inter antialiased bg-white text-gray-900">
        <!-- Navigation -->
        <nav class="absolute top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold gradient-text">Press Release Pro</h1>
                        </div>
                    </div>
                    @if (Route::has('login'))
                        <div class="flex items-center space-x-4">
                            @auth
                                <a href="{{ url('/dashboard') }}" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-full hover:from-indigo-700 hover:to-purple-700 transition duration-300 font-semibold">Dashboard</a>
                            @else
                                <a href="{{ route('login') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">Sign In</a>
                                @if (Route::has('register'))
                                    <a href="{{ route('register') }}" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-full hover:from-indigo-700 hover:to-purple-700 transition duration-300 font-semibold">Get Started</a>
                                @endif
                            @endauth
                        </div>
                    @endif
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="relative pt-20 pb-16 overflow-hidden">
            <div class="gradient-bg hero-pattern">
                <div class="absolute inset-0">
                    <div class="blob absolute top-20 left-10 w-72 h-72 bg-white/10"></div>
                    <div class="blob absolute bottom-20 right-10 w-96 h-96 bg-white/5" style="animation-delay: 2s;"></div>
                </div>
                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                    <div class="grid lg:grid-cols-2 gap-12 items-center">
                        <div class="text-white">
                            <h1 class="text-5xl lg:text-7xl font-bold font-playfair leading-tight mb-6">
                                Share Your <span class="text-yellow-300">Story</span> with the World
                            </h1>
                            <p class="text-xl text-white/90 mb-8 leading-relaxed">
                                Professional press release distribution platform that gets your news in front of journalists, media outlets, and your target audience.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="{{ route('register') }}" class="bg-white text-indigo-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition duration-300 text-center">
                                    Start Publishing
                                </a>
                                <a href="#features" class="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-indigo-600 transition duration-300 text-center">
                                    Learn More
                                </a>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition duration-500">
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                                    <div class="h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-lg"></div>
                                    <div class="h-4 bg-gray-200 rounded w-full"></div>
                                    <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-playfair">Why Choose Our Platform?</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to create, distribute, and track your press releases with professional results.</p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-rocket text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Instant Distribution</h3>
                        <p class="text-gray-600 leading-relaxed">Get your press release distributed to thousands of media outlets and journalists within minutes of publication.</p>
                    </div>

                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-chart-line text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Analytics & Tracking</h3>
                        <p class="text-gray-600 leading-relaxed">Monitor your press release performance with detailed analytics and real-time tracking data.</p>
                    </div>

                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Media Network</h3>
                        <p class="text-gray-600 leading-relaxed">Access our extensive network of journalists, bloggers, and media professionals worldwide.</p>
                    </div>

                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-edit text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Easy Editor</h3>
                        <p class="text-gray-600 leading-relaxed">Create professional press releases with our intuitive editor and pre-built templates.</p>
                    </div>

                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Secure & Reliable</h3>
                        <p class="text-gray-600 leading-relaxed">Enterprise-grade security ensures your content is protected and delivered reliably.</p>
                    </div>

                    <div class="card-hover bg-white p-8 rounded-2xl shadow-lg">
                        <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-green-600 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-headset text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">24/7 Support</h3>
                        <p class="text-gray-600 leading-relaxed">Get help whenever you need it with our dedicated customer support team.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 gradient-bg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6 font-playfair">Ready to Get Started?</h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">Join thousands of businesses and professionals who trust our platform for their press release needs.</p>
                <a href="{{ route('register') }}" class="inline-block bg-white text-indigo-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition duration-300 text-lg">
                    Create Your First Press Release
                </a>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="col-span-2">
                        <h3 class="text-2xl font-bold mb-4 gradient-text">Press Release Pro</h3>
                        <p class="text-gray-300 mb-4">Professional press release distribution platform for businesses and organizations worldwide.</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li><a href="#" class="hover:text-white transition duration-300">Features</a></li>
                            <li><a href="#" class="hover:text-white transition duration-300">Pricing</a></li>
                            <li><a href="#" class="hover:text-white transition duration-300">About</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Support</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li><a href="#" class="hover:text-white transition duration-300">Help Center</a></li>
                            <li><a href="#" class="hover:text-white transition duration-300">Contact</a></li>
                            <li><a href="#" class="hover:text-white transition duration-300">Privacy</a></li>
                        </ul>
                    </div>
                </div>
                <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                    <p>&copy; 2025 Press Release Pro. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </body>
</html>
