/**
 * easymde v2.16.1
 * Copyright <PERSON><PERSON><PERSON>
 * @link https://github.com/ionaru/easy-markdown-editor
 * @license MIT
 */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EasyMDE=e()}}((function(){return function e(t,n,r){function i(a,l){if(!n[a]){if(!t[a]){var s="function"==typeof require&&require;if(!l&&s)return s(a,!0);if(o)return o(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var c=n[a]={exports:{}};t[a][0].call(c.exports,(function(e){return i(t[a][1][e]||e)}),c,c.exports,e,t,n,r)}return n[a].exports}for(var o="function"==typeof require&&require,a=0;a<r.length;a++)i(r[a]);return i}({1:[function(e,t,n){},{}],2:[function(e,t,n){"use strict";var r=e("typo-js");function i(e){"function"==typeof(e=e||{}).codeMirrorInstance&&"function"==typeof e.codeMirrorInstance.defineMode?(String.prototype.includes||(String.prototype.includes=function(){return-1!==String.prototype.indexOf.apply(this,arguments)}),e.codeMirrorInstance.defineMode("spell-checker",(function(t){if(!i.aff_loading){i.aff_loading=!0;var n=new XMLHttpRequest;n.open("GET","https://cdn.jsdelivr.net/codemirror.spell-checker/latest/en_US.aff",!0),n.onload=function(){4===n.readyState&&200===n.status&&(i.aff_data=n.responseText,i.num_loaded++,2==i.num_loaded&&(i.typo=new r("en_US",i.aff_data,i.dic_data,{platform:"any"})))},n.send(null)}if(!i.dic_loading){i.dic_loading=!0;var o=new XMLHttpRequest;o.open("GET","https://cdn.jsdelivr.net/codemirror.spell-checker/latest/en_US.dic",!0),o.onload=function(){4===o.readyState&&200===o.status&&(i.dic_data=o.responseText,i.num_loaded++,2==i.num_loaded&&(i.typo=new r("en_US",i.aff_data,i.dic_data,{platform:"any"})))},o.send(null)}var a='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~ ',l={token:function(e){var t=e.peek(),n="";if(a.includes(t))return e.next(),null;for(;null!=(t=e.peek())&&!a.includes(t);)n+=t,e.next();return i.typo&&!i.typo.check(n)?"spell-error":null}},s=e.codeMirrorInstance.getMode(t,t.backdrop||"text/plain");return e.codeMirrorInstance.overlayMode(s,l,!0)}))):console.log("CodeMirror Spell Checker: You must provide an instance of CodeMirror via the option `codeMirrorInstance`")}i.num_loaded=0,i.aff_loading=!1,i.dic_loading=!1,i.aff_data="",i.dic_data="",i.typo,t.exports=i},{"typo-js":16}],3:[function(e,t,n){(function(e){"use strict";function t(t,n){clearTimeout(n.timeout),e.off(window,"mouseup",n.hurry),e.off(window,"keyup",n.hurry)}e.defineOption("autoRefresh",!1,(function(n,r){n.state.autoRefresh&&(t(0,n.state.autoRefresh),n.state.autoRefresh=null),r&&0==n.display.wrapper.offsetHeight&&function(n,r){function i(){n.display.wrapper.offsetHeight?(t(0,r),n.display.lastWrapHeight!=n.display.wrapper.clientHeight&&n.refresh()):r.timeout=setTimeout(i,r.delay)}r.timeout=setTimeout(i,r.delay),r.hurry=function(){clearTimeout(r.timeout),r.timeout=setTimeout(i,50)},e.on(window,"mouseup",r.hurry),e.on(window,"keyup",r.hurry)}(n,n.state.autoRefresh={delay:r.delay||250})}))})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],4:[function(e,t,n){(function(e){"use strict";e.defineOption("fullScreen",!1,(function(t,n,r){r==e.Init&&(r=!1),!r!=!n&&(n?function(e){var t=e.getWrapperElement();e.state.fullScreenRestore={scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,width:t.style.width,height:t.style.height},t.style.width="",t.style.height="auto",t.className+=" CodeMirror-fullscreen",document.documentElement.style.overflow="hidden",e.refresh()}(t):function(e){var t=e.getWrapperElement();t.className=t.className.replace(/\s*CodeMirror-fullscreen\b/,""),document.documentElement.style.overflow="";var n=e.state.fullScreenRestore;t.style.width=n.width,t.style.height=n.height,window.scrollTo(n.scrollLeft,n.scrollTop),e.refresh()}(t))}))})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],5:[function(e,t,n){(function(e){function t(e){e.state.placeholder&&(e.state.placeholder.parentNode.removeChild(e.state.placeholder),e.state.placeholder=null)}function n(e){t(e);var n=e.state.placeholder=document.createElement("pre");n.style.cssText="height: 0; overflow: visible",n.style.direction=e.getOption("direction"),n.className="CodeMirror-placeholder CodeMirror-line-like";var r=e.getOption("placeholder");"string"==typeof r&&(r=document.createTextNode(r)),n.appendChild(r),e.display.lineSpace.insertBefore(n,e.display.lineSpace.firstChild)}function r(e){o(e)&&n(e)}function i(e){var r=e.getWrapperElement(),i=o(e);r.className=r.className.replace(" CodeMirror-empty","")+(i?" CodeMirror-empty":""),i?n(e):t(e)}function o(e){return 1===e.lineCount()&&""===e.getLine(0)}e.defineOption("placeholder","",(function(o,a,l){var s=l&&l!=e.Init;if(a&&!s)o.on("blur",r),o.on("change",i),o.on("swapDoc",i),e.on(o.getInputField(),"compositionupdate",o.state.placeholderCompose=function(){!function(e){setTimeout((function(){var r=!1;if(1==e.lineCount()){var i=e.getInputField();r="TEXTAREA"==i.nodeName?!e.getLine(0).length:!/[^\u200b]/.test(i.querySelector(".CodeMirror-line").textContent)}r?n(e):t(e)}),20)}(o)}),i(o);else if(!a&&s){o.off("blur",r),o.off("change",i),o.off("swapDoc",i),e.off(o.getInputField(),"compositionupdate",o.state.placeholderCompose),t(o);var u=o.getWrapperElement();u.className=u.className.replace(" CodeMirror-empty","")}a&&!o.hasFocus()&&r(o)}))})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],6:[function(e,t,n){(function(e){"use strict";var t=/^(\s*)(>[> ]*|[*+-] \[[x ]\]\s|[*+-]\s|(\d+)([.)]))(\s*)/,n=/^(\s*)(>[> ]*|[*+-] \[[x ]\]|[*+-]|(\d+)[.)])(\s*)$/,r=/[*+-]\s/;function i(e,n){var r=n.line,i=0,o=0,a=t.exec(e.getLine(r)),l=a[1];do{var s=r+(i+=1),u=e.getLine(s),c=t.exec(u);if(c){var d=c[1],h=parseInt(a[3],10)+i-o,f=parseInt(c[3],10),p=f;if(l!==d||isNaN(f)){if(l.length>d.length)return;if(l.length<d.length&&1===i)return;o+=1}else h===f&&(p=f+1),h>f&&(p=h+1),e.replaceRange(u.replace(t,d+p+c[4]+c[5]),{line:s,ch:0},{line:s,ch:u.length})}}while(c)}e.commands.newlineAndIndentContinueMarkdownList=function(o){if(o.getOption("disableInput"))return e.Pass;for(var a=o.listSelections(),l=[],s=0;s<a.length;s++){var u=a[s].head,c=o.getStateAfter(u.line),d=e.innerMode(o.getMode(),c);if("markdown"!==d.mode.name)return void o.execCommand("newlineAndIndent");var h=!1!==(c=d.state).list,f=0!==c.quote,p=o.getLine(u.line),m=t.exec(p),g=/^\s*$/.test(p.slice(0,u.ch));if(!a[s].empty()||!h&&!f||!m||g)return void o.execCommand("newlineAndIndent");if(n.test(p)){var v=f&&/>\s*$/.test(p),x=!/>\s*$/.test(p);(v||x)&&o.replaceRange("",{line:u.line,ch:0},{line:u.line,ch:u.ch+1}),l[s]="\n"}else{var y=m[1],b=m[5],D=!(r.test(m[2])||m[2].indexOf(">")>=0),C=D?parseInt(m[3],10)+1+m[4]:m[2].replace("x"," ");l[s]="\n"+y+C+b,D&&i(o,u)}}o.replaceSelections(l)}})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],7:[function(e,t,n){(function(e){"use strict";e.overlayMode=function(t,n,r){return{startState:function(){return{base:e.startState(t),overlay:e.startState(n),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(r){return{base:e.copyState(t,r.base),overlay:e.copyState(n,r.overlay),basePos:r.basePos,baseCur:null,overlayPos:r.overlayPos,overlayCur:null}},token:function(e,i){return(e!=i.streamSeen||Math.min(i.basePos,i.overlayPos)<e.start)&&(i.streamSeen=e,i.basePos=i.overlayPos=e.start),e.start==i.basePos&&(i.baseCur=t.token(e,i.base),i.basePos=e.pos),e.start==i.overlayPos&&(e.pos=e.start,i.overlayCur=n.token(e,i.overlay),i.overlayPos=e.pos),e.pos=Math.min(i.basePos,i.overlayPos),null==i.overlayCur?i.baseCur:null!=i.baseCur&&i.overlay.combineTokens||r&&null==i.overlay.combineTokens?i.baseCur+" "+i.overlayCur:i.overlayCur},indent:t.indent&&function(e,n,r){return t.indent(e.base,n,r)},electricChars:t.electricChars,innerMode:function(e){return{state:e.base,mode:t}},blankLine:function(e){var i,o;return t.blankLine&&(i=t.blankLine(e.base)),n.blankLine&&(o=n.blankLine(e.overlay)),null==o?i:r&&null!=i?i+" "+o:o}}}})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],8:[function(e,t,n){(function(e){"use strict";var t,n,r=e.Pos;function i(e,t){for(var n=function(e){var t=e.flags;return null!=t?t:(e.ignoreCase?"i":"")+(e.global?"g":"")+(e.multiline?"m":"")}(e),r=n,i=0;i<t.length;i++)-1==r.indexOf(t.charAt(i))&&(r+=t.charAt(i));return n==r?e:new RegExp(e.source,r)}function o(e){return/\\s|\\n|\n|\\W|\\D|\[\^/.test(e.source)}function a(e,t,n){t=i(t,"g");for(var o=n.line,a=n.ch,l=e.lastLine();o<=l;o++,a=0){t.lastIndex=a;var s=e.getLine(o),u=t.exec(s);if(u)return{from:r(o,u.index),to:r(o,u.index+u[0].length),match:u}}}function l(e,t,n){if(!o(t))return a(e,t,n);t=i(t,"gm");for(var l,s=1,u=n.line,c=e.lastLine();u<=c;){for(var d=0;d<s&&!(u>c);d++){var h=e.getLine(u++);l=null==l?h:l+"\n"+h}s*=2,t.lastIndex=n.ch;var f=t.exec(l);if(f){var p=l.slice(0,f.index).split("\n"),m=f[0].split("\n"),g=n.line+p.length-1,v=p[p.length-1].length;return{from:r(g,v),to:r(g+m.length-1,1==m.length?v+m[0].length:m[m.length-1].length),match:f}}}}function s(e,t,n){for(var r,i=0;i<=e.length;){t.lastIndex=i;var o=t.exec(e);if(!o)break;var a=o.index+o[0].length;if(a>e.length-n)break;(!r||a>r.index+r[0].length)&&(r=o),i=o.index+1}return r}function u(e,t,n){t=i(t,"g");for(var o=n.line,a=n.ch,l=e.firstLine();o>=l;o--,a=-1){var u=e.getLine(o),c=s(u,t,a<0?0:u.length-a);if(c)return{from:r(o,c.index),to:r(o,c.index+c[0].length),match:c}}}function c(e,t,n){if(!o(t))return u(e,t,n);t=i(t,"gm");for(var a,l=1,c=e.getLine(n.line).length-n.ch,d=n.line,h=e.firstLine();d>=h;){for(var f=0;f<l&&d>=h;f++){var p=e.getLine(d--);a=null==a?p:p+"\n"+a}l*=2;var m=s(a,t,c);if(m){var g=a.slice(0,m.index).split("\n"),v=m[0].split("\n"),x=d+g.length,y=g[g.length-1].length;return{from:r(x,y),to:r(x+v.length-1,1==v.length?y+v[0].length:v[v.length-1].length),match:m}}}}function d(e,t,n,r){if(e.length==t.length)return n;for(var i=0,o=n+Math.max(0,e.length-t.length);;){if(i==o)return i;var a=i+o>>1,l=r(e.slice(0,a)).length;if(l==n)return a;l>n?o=a:i=a+1}}function h(e,i,o,a){if(!i.length)return null;var l=a?t:n,s=l(i).split(/\r|\n\r?/);e:for(var u=o.line,c=o.ch,h=e.lastLine()+1-s.length;u<=h;u++,c=0){var f=e.getLine(u).slice(c),p=l(f);if(1==s.length){var m=p.indexOf(s[0]);if(-1==m)continue e;return o=d(f,p,m,l)+c,{from:r(u,d(f,p,m,l)+c),to:r(u,d(f,p,m+s[0].length,l)+c)}}var g=p.length-s[0].length;if(p.slice(g)==s[0]){for(var v=1;v<s.length-1;v++)if(l(e.getLine(u+v))!=s[v])continue e;var x=e.getLine(u+s.length-1),y=l(x),b=s[s.length-1];if(y.slice(0,b.length)==b)return{from:r(u,d(f,p,g,l)+c),to:r(u+s.length-1,d(x,y,b.length,l))}}}}function f(e,i,o,a){if(!i.length)return null;var l=a?t:n,s=l(i).split(/\r|\n\r?/);e:for(var u=o.line,c=o.ch,h=e.firstLine()-1+s.length;u>=h;u--,c=-1){var f=e.getLine(u);c>-1&&(f=f.slice(0,c));var p=l(f);if(1==s.length){var m=p.lastIndexOf(s[0]);if(-1==m)continue e;return{from:r(u,d(f,p,m,l)),to:r(u,d(f,p,m+s[0].length,l))}}var g=s[s.length-1];if(p.slice(0,g.length)==g){var v=1;for(o=u-s.length+1;v<s.length-1;v++)if(l(e.getLine(o+v))!=s[v])continue e;var x=e.getLine(u+1-s.length),y=l(x);if(y.slice(y.length-s[0].length)==s[0])return{from:r(u+1-s.length,d(x,y,x.length-s[0].length,l)),to:r(u,d(f,p,g.length,l))}}}}function p(e,t,n,o){var s;this.atOccurrence=!1,this.afterEmptyMatch=!1,this.doc=e,n=n?e.clipPos(n):r(0,0),this.pos={from:n,to:n},"object"==typeof o?s=o.caseFold:(s=o,o=null),"string"==typeof t?(null==s&&(s=!1),this.matches=function(n,r){return(n?f:h)(e,t,r,s)}):(t=i(t,"gm"),o&&!1===o.multiline?this.matches=function(n,r){return(n?u:a)(e,t,r)}:this.matches=function(n,r){return(n?c:l)(e,t,r)})}String.prototype.normalize?(t=function(e){return e.normalize("NFD").toLowerCase()},n=function(e){return e.normalize("NFD")}):(t=function(e){return e.toLowerCase()},n=function(e){return e}),p.prototype={findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(t){var n=this.doc.clipPos(t?this.pos.from:this.pos.to);if(this.afterEmptyMatch&&this.atOccurrence&&(n=r(n.line,n.ch),t?(n.ch--,n.ch<0&&(n.line--,n.ch=(this.doc.getLine(n.line)||"").length)):(n.ch++,n.ch>(this.doc.getLine(n.line)||"").length&&(n.ch=0,n.line++)),0!=e.cmpPos(n,this.doc.clipPos(n))))return this.atOccurrence=!1;var i=this.matches(t,n);if(this.afterEmptyMatch=i&&0==e.cmpPos(i.from,i.to),i)return this.pos=i,this.atOccurrence=!0,this.pos.match||!0;var o=r(t?this.doc.firstLine():this.doc.lastLine()+1,0);return this.pos={from:o,to:o},this.atOccurrence=!1},from:function(){if(this.atOccurrence)return this.pos.from},to:function(){if(this.atOccurrence)return this.pos.to},replace:function(t,n){if(this.atOccurrence){var i=e.splitLines(t);this.doc.replaceRange(i,this.pos.from,this.pos.to,n),this.pos.to=r(this.pos.from.line+i.length-1,i[i.length-1].length+(1==i.length?this.pos.from.ch:0))}}},e.defineExtension("getSearchCursor",(function(e,t,n){return new p(this.doc,e,t,n)})),e.defineDocExtension("getSearchCursor",(function(e,t,n){return new p(this,e,t,n)})),e.defineExtension("selectMatches",(function(t,n){for(var r=[],i=this.getSearchCursor(t,this.getCursor("from"),n);i.findNext()&&!(e.cmpPos(i.to(),this.getCursor("to"))>0);)r.push({anchor:i.from(),head:i.to()});r.length&&this.setSelections(r,0)}))})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],9:[function(e,t,n){(function(e){"use strict";function t(e){e.state.markedSelection&&e.operation((function(){!function(e){if(!e.somethingSelected())return a(e);if(e.listSelections().length>1)return l(e);var t=e.getCursor("start"),n=e.getCursor("end"),r=e.state.markedSelection;if(!r.length)return o(e,t,n);var s=r[0].find(),u=r[r.length-1].find();if(!s||!u||n.line-t.line<=8||i(t,u.to)>=0||i(n,s.from)<=0)return l(e);for(;i(t,s.from)>0;)r.shift().clear(),s=r[0].find();for(i(t,s.from)<0&&(s.to.line-t.line<8?(r.shift().clear(),o(e,t,s.to,0)):o(e,t,s.from,0));i(n,u.to)<0;)r.pop().clear(),u=r[r.length-1].find();i(n,u.to)>0&&(n.line-u.from.line<8?(r.pop().clear(),o(e,u.from,n)):o(e,u.to,n))}(e)}))}function n(e){e.state.markedSelection&&e.state.markedSelection.length&&e.operation((function(){a(e)}))}e.defineOption("styleSelectedText",!1,(function(r,i,o){var s=o&&o!=e.Init;i&&!s?(r.state.markedSelection=[],r.state.markedSelectionStyle="string"==typeof i?i:"CodeMirror-selectedtext",l(r),r.on("cursorActivity",t),r.on("change",n)):!i&&s&&(r.off("cursorActivity",t),r.off("change",n),a(r),r.state.markedSelection=r.state.markedSelectionStyle=null)}));var r=e.Pos,i=e.cmpPos;function o(e,t,n,o){if(0!=i(t,n))for(var a=e.state.markedSelection,l=e.state.markedSelectionStyle,s=t.line;;){var u=s==t.line?t:r(s,0),c=s+8,d=c>=n.line,h=d?n:r(c,0),f=e.markText(u,h,{className:l});if(null==o?a.push(f):a.splice(o++,0,f),d)break;s=c}}function a(e){for(var t=e.state.markedSelection,n=0;n<t.length;++n)t[n].clear();t.length=0}function l(e){a(e);for(var t=e.listSelections(),n=0;n<t.length;n++)o(e,t[n].from(),t[n].to())}})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],10:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?t.exports=r():(e=e||self).CodeMirror=r()}(this,(function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=r||i||o,l=a&&(r?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),u=s&&/Qt\/\d+\.\d+/.test(e),c=!o&&/Chrome\//.test(e),d=/Opera\//.test(e),h=/Apple Computer/.test(navigator.vendor),f=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),p=/PhantomJS/.test(e),m=h&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),g=/Android/.test(e),v=m||g||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),x=m||/Mac/.test(t),y=/\bCrOS\b/.test(e),b=/win/i.test(t),D=d&&e.match(/Version\/(\d*\.\d*)/);D&&(D=Number(D[1])),D&&D>=15&&(d=!1,s=!0);var C=x&&(u||d&&(null==D||D<12.11)),w=n||a&&l>=9;function k(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var S,F=function(e,t){var n=e.className,r=k(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function A(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function E(e,t){return A(e).appendChild(t)}function T(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function L(e,t,n,r){var i=T(e,t,n,r);return i.setAttribute("role","presentation"),i}function M(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function B(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function N(e,t){var n=e.className;k(t).test(n)||(e.className+=(n?" ":"")+t)}function O(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!k(n[r]).test(t)&&(t+=" "+n[r]);return t}S=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var I=function(e){e.select()};function z(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function H(e,t,n){for(var r in t||(t={}),e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function R(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=n-a%n,o=l+1}}m?I=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(I=function(e){try{e.select()}catch(e){}});var P=function(){this.id=null,this.f=null,this.time=0,this.handler=z(this.onTimeout,this)};function _(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}P.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},P.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};var W={toString:function(){return"CodeMirror.Pass"}},j={scroll:!1},q={origin:"*mouse"},U={origin:"+move"};function $(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var a=o-r;if(o==e.length||i+a>=t)return r+Math.min(a,t-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=t)return r}}var G=[""];function V(e){for(;G.length<=e;)G.push(X(G)+" ");return G[e]}function X(e){return e[e.length-1]}function K(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function Z(){}function Y(e,t){var n;return Object.create?n=Object.create(e):(Z.prototype=e,n=new Z),t&&H(t,n),n}var Q=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function J(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Q.test(e))}function ee(e,t){return t?!!(t.source.indexOf("\\w")>-1&&J(e))||t.test(e):J(e)}function te(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ne=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function re(e){return e.charCodeAt(0)>=768&&ne.test(e)}function ie(e,t,n){for(;(n<0?t>0:t<e.length)&&re(e.charAt(t));)t+=n;return t}function oe(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}var ae=null;function le(e,t,n){var r;ae=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:ae=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:ae=i)}return null!=r?r:ae}var se=function(){var e=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,t=/[stwN]/,n=/[LRr]/,r=/[Lb1n]/,i=/[1n]/;function o(e,t,n){this.level=e,this.from=t,this.to=n}return function(a,l){var s="ltr"==l?"L":"R";if(0==a.length||"ltr"==l&&!e.test(a))return!1;for(var u,c=a.length,d=[],h=0;h<c;++h)d.push((u=a.charCodeAt(h))<=247?"bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN".charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?"nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111".charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":8204==u?"b":"L");for(var f=0,p=s;f<c;++f){var m=d[f];"m"==m?d[f]=p:p=m}for(var g=0,v=s;g<c;++g){var x=d[g];"1"==x&&"r"==v?d[g]="n":n.test(x)&&(v=x,"r"==x&&(d[g]="R"))}for(var y=1,b=d[0];y<c-1;++y){var D=d[y];"+"==D&&"1"==b&&"1"==d[y+1]?d[y]="1":","!=D||b!=d[y+1]||"1"!=b&&"n"!=b||(d[y]=b),b=D}for(var C=0;C<c;++C){var w=d[C];if(","==w)d[C]="N";else if("%"==w){var k=void 0;for(k=C+1;k<c&&"%"==d[k];++k);for(var S=C&&"!"==d[C-1]||k<c&&"1"==d[k]?"1":"N",F=C;F<k;++F)d[F]=S;C=k-1}}for(var A=0,E=s;A<c;++A){var T=d[A];"L"==E&&"1"==T?d[A]="L":n.test(T)&&(E=T)}for(var L=0;L<c;++L)if(t.test(d[L])){var M=void 0;for(M=L+1;M<c&&t.test(d[M]);++M);for(var B="L"==(L?d[L-1]:s),N=B==("L"==(M<c?d[M]:s))?B?"L":"R":s,O=L;O<M;++O)d[O]=N;L=M-1}for(var I,z=[],H=0;H<c;)if(r.test(d[H])){var R=H;for(++H;H<c&&r.test(d[H]);++H);z.push(new o(0,R,H))}else{var P=H,_=z.length,W="rtl"==l?1:0;for(++H;H<c&&"L"!=d[H];++H);for(var j=P;j<H;)if(i.test(d[j])){P<j&&(z.splice(_,0,new o(1,P,j)),_+=W);var q=j;for(++j;j<H&&i.test(d[j]);++j);z.splice(_,0,new o(2,q,j)),_+=W,P=j}else++j;P<H&&z.splice(_,0,new o(1,P,H))}return"ltr"==l&&(1==z[0].level&&(I=a.match(/^\s+/))&&(z[0].from=I[0].length,z.unshift(new o(0,0,I[0].length))),1==X(z).level&&(I=a.match(/\s+$/))&&(X(z).to-=I[0].length,z.push(new o(0,c-I[0].length,c)))),"rtl"==l?z.reverse():z}}();function ue(e,t){var n=e.order;return null==n&&(n=e.order=se(e.text,t)),n}var ce=[],de=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||ce).concat(n)}};function he(e,t){return e._handlers&&e._handlers[t]||ce}function fe(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=_(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function pe(e,t){var n=he(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function me(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),pe(e,n||t.type,e,t),De(t)||t.codemirrorIgnore}function ge(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==_(n,t[r])&&n.push(t[r])}function ve(e,t){return he(e,t).length>0}function xe(e){e.prototype.on=function(e,t){de(this,e,t)},e.prototype.off=function(e,t){fe(this,e,t)}}function ye(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function be(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function De(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Ce(e){ye(e),be(e)}function we(e){return e.target||e.srcElement}function ke(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),x&&e.ctrlKey&&1==t&&(t=3),t}var Se,Fe,Ae=function(){if(a&&l<9)return!1;var e=T("div");return"draggable"in e||"dragDrop"in e}();function Ee(e){if(null==Se){var t=T("span","​");E(e,T("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Se=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&l<8))}var n=Se?T("span","​"):T("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function Te(e){if(null!=Fe)return Fe;var t=E(e,document.createTextNode("AخA")),n=S(t,0,1).getBoundingClientRect(),r=S(t,1,2).getBoundingClientRect();return A(e),!(!n||n.left==n.right)&&(Fe=r.right-n.right<3)}var Le,Me=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(n.push(o.slice(0,a)),t+=a+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},Be=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ne="oncopy"in(Le=T("div"))||(Le.setAttribute("oncopy","return;"),"function"==typeof Le.oncopy),Oe=null;var Ie={},ze={};function He(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ie[e]=t}function Re(e){if("string"==typeof e&&ze.hasOwnProperty(e))e=ze[e];else if(e&&"string"==typeof e.name&&ze.hasOwnProperty(e.name)){var t=ze[e.name];"string"==typeof t&&(t={name:t}),(e=Y(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Re("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Re("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Pe(e,t){t=Re(t);var n=Ie[t.name];if(!n)return Pe(e,"text/plain");var r=n(e,t);if(_e.hasOwnProperty(t.name)){var i=_e[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)r[a]=t.modeProps[a];return r}var _e={};function We(e,t){H(t,_e.hasOwnProperty(e)?_e[e]:_e[e]={})}function je(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function qe(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Ue(e,t,n){return!e.startState||e.startState(t,n)}var $e=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function Ge(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function Ve(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,(function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i})),r}function Xe(e,t,n){var r=[];return e.iter(t,n,(function(e){r.push(e.text)})),r}function Ke(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function Ze(e){if(null==e.parent)return null;for(var t=e.parent,n=_(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function Ye(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return n+a}function Qe(e,t){return t>=e.first&&t<e.first+e.size}function Je(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function et(e,t,n){if(void 0===n&&(n=null),!(this instanceof et))return new et(e,t,n);this.line=e,this.ch=t,this.sticky=n}function tt(e,t){return e.line-t.line||e.ch-t.ch}function nt(e,t){return e.sticky==t.sticky&&0==tt(e,t)}function rt(e){return et(e.line,e.ch)}function it(e,t){return tt(e,t)<0?t:e}function ot(e,t){return tt(e,t)<0?e:t}function at(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function lt(e,t){if(t.line<e.first)return et(e.first,0);var n=e.first+e.size-1;return t.line>n?et(n,Ge(e,n).text.length):function(e,t){var n=e.ch;return null==n||n>t?et(e.line,t):n<0?et(e.line,0):e}(t,Ge(e,t.line).text.length)}function st(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=lt(e,t[r]);return n}$e.prototype.eol=function(){return this.pos>=this.string.length},$e.prototype.sol=function(){return this.pos==this.lineStart},$e.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},$e.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},$e.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},$e.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},$e.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},$e.prototype.skipToEnd=function(){this.pos=this.string.length},$e.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},$e.prototype.backUp=function(e){this.pos-=e},$e.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},$e.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},$e.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},$e.prototype.current=function(){return this.string.slice(this.start,this.pos)},$e.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},$e.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},$e.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var ut=function(e,t){this.state=e,this.lookAhead=t},ct=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function dt(e,t,n,r){var i=[e.state.modeGen],o={};bt(e,t.text,e.doc.mode,n,(function(e,t){return i.push(e,t)}),o,r);for(var a=n.state,l=function(r){n.baseTokens=i;var l=e.state.overlays[r],s=1,u=0;n.state=!0,bt(e,t.text,l.mode,n,(function(e,t){for(var n=s;u<e;){var r=i[s];r>e&&i.splice(s,1,e,i[s+1],r),s+=2,u=Math.min(e,r)}if(t)if(l.opaque)i.splice(n,s-n,e,"overlay "+t),s=n+2;else for(;n<s;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+t}}),o),n.state=a,n.baseTokens=null,n.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)l(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function ht(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=ft(e,Ze(t)),i=t.text.length>e.options.maxHighlightLength&&je(e.doc.mode,r.state),o=dt(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function ft(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new ct(r,!0,t);var o=function(e,t,n){for(var r,i,o=e.doc,a=n?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=Ge(o,l-1),u=s.stateAfter;if(u&&(!n||l+(u instanceof ut?u.lookAhead:0)<=o.modeFrontier))return l;var c=R(s.text,null,e.options.tabSize);(null==i||r>c)&&(i=l-1,r=c)}return i}(e,t,n),a=o>r.first&&Ge(r,o-1).stateAfter,l=a?ct.fromSaved(r,a,o):new ct(r,Ue(r.mode),o);return r.iter(o,t,(function(n){pt(e,n.text,l);var r=l.line;n.stateAfter=r==t-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?l.save():null,l.nextLine()})),n&&(r.modeFrontier=l.line),l}function pt(e,t,n,r){var i=e.doc.mode,o=new $e(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&mt(i,n.state);!o.eol();)gt(i,o,n.state),o.start=o.pos}function mt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=qe(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function gt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=qe(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}ct.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},ct.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},ct.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},ct.fromSaved=function(e,t,n){return t instanceof ut?new ct(e,je(e.mode,t.state),n,t.lookAhead):new ct(e,je(e.mode,t),n)},ct.prototype.save=function(e){var t=!1!==e?je(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new ut(t,this.maxLookAhead):t};var vt=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function xt(e,t,n,r){var i,o,a=e.doc,l=a.mode,s=Ge(a,(t=lt(a,t)).line),u=ft(e,t.line,n),c=new $e(s.text,e.options.tabSize,u);for(r&&(o=[]);(r||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=gt(l,c,u.state),r&&o.push(new vt(c,i,je(a.mode,u.state)));return r?o:new vt(c,i,u.state)}function yt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function bt(e,t,n,r,i,o,a){var l=n.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,u=0,c=null,d=new $e(t,e.options.tabSize,r),h=e.options.addModeClass&&[null];for(""==t&&yt(mt(n,r.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(l=!1,a&&pt(e,t,r,d.pos),d.pos=t.length,s=null):s=yt(gt(n,d,r.state,h),o),h){var f=h[0].name;f&&(s="m-"+(s?f+" "+s:f))}if(!l||c!=s){for(;u<d.start;)i(u=Math.min(d.start,u+5e3),c);c=s}d.start=d.pos}for(;u<d.pos;){var p=Math.min(d.pos,u+5e3);i(p,c),u=p}}var Dt=!1,Ct=!1;function wt(e,t,n){this.marker=e,this.from=t,this.to=n}function kt(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function St(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function Ft(e,t){if(t.full)return null;var n=Qe(e,t.from.line)&&Ge(e,t.from.line).markedSpans,r=Qe(e,t.to.line)&&Ge(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,a=0==tt(t.from,t.to),l=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!n||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new wt(a,o.from,l?null:o.to))}}return r}(n,i,a),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!n||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new wt(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return r}(r,o,a),u=1==t.text.length,c=X(t.text).length+(u?i:0);if(l)for(var d=0;d<l.length;++d){var h=l[d];if(null==h.to){var f=kt(s,h.marker);f?u&&(h.to=null==f.to?null:f.to+c):h.to=i}}if(s)for(var p=0;p<s.length;++p){var m=s[p];if(null!=m.to&&(m.to+=c),null==m.from)kt(l,m.marker)||(m.from=c,u&&(l||(l=[])).push(m));else m.from+=c,u&&(l||(l=[])).push(m)}l&&(l=At(l)),s&&s!=l&&(s=At(s));var g=[l];if(!u){var v,x=t.text.length-2;if(x>0&&l)for(var y=0;y<l.length;++y)null==l[y].to&&(v||(v=[])).push(new wt(l[y].marker,null,null));for(var b=0;b<x;++b)g.push(v);g.push(s)}return g}function At(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Et(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function Tt(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Lt(e){return e.inclusiveLeft?-1:0}function Mt(e){return e.inclusiveRight?1:0}function Bt(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=tt(r.from,i.from)||Lt(e)-Lt(t);if(o)return-o;var a=tt(r.to,i.to)||Mt(e)-Mt(t);return a||t.id-e.id}function Nt(e,t){var n,r=Ct&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||Bt(n,i.marker)<0)&&(n=i.marker);return n}function Ot(e){return Nt(e,!0)}function It(e){return Nt(e,!1)}function zt(e,t){var n,r=Ct&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Bt(n,o.marker)<0)&&(n=o.marker)}return n}function Ht(e,t,n,r,i){var o=Ge(e,t),a=Ct&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var u=s.marker.find(0),c=tt(u.from,n)||Lt(s.marker)-Lt(i),d=tt(u.to,r)||Mt(s.marker)-Mt(i);if(!(c>=0&&d<=0||c<=0&&d>=0)&&(c<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?tt(u.to,n)>=0:tt(u.to,n)>0)||c>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?tt(u.from,r)<=0:tt(u.from,r)<0)))return!0}}}function Rt(e){for(var t;t=Ot(e);)e=t.find(-1,!0).line;return e}function Pt(e,t){var n=Ge(e,t),r=Rt(n);return n==r?t:Ze(r)}function _t(e,t){if(t>e.lastLine())return t;var n,r=Ge(e,t);if(!Wt(e,r))return t;for(;n=It(r);)r=n.find(1,!0).line;return Ze(r)+1}function Wt(e,t){var n=Ct&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&jt(e,t,r))return!0}}function jt(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return jt(e,r.line,kt(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&jt(e,t,i))return!0}function qt(e){for(var t=0,n=(e=Rt(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==n)break;t+=l.height}return t}function Ut(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=Ot(r);){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=e;t=It(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function $t(e){var t=e.display,n=e.doc;t.maxLine=Ge(n,n.first),t.maxLineLength=Ut(t.maxLine),t.maxLineChanged=!0,n.iter((function(e){var n=Ut(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)}))}var Gt=function(e,t,n){this.text=e,Tt(this,t),this.height=n?n(this):1};function Vt(e){e.parent=null,Et(e)}Gt.prototype.lineNo=function(){return Ze(this)},xe(Gt);var Xt={},Kt={};function Zt(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?Kt:Xt;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function Yt(e,t){var n=L("span",null,null,s?"padding-right: .1px":null),r={pre:L("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;r.pos=0,r.addToken=Jt,Te(e.display.measure)&&(a=ue(o,e.doc.direction))&&(r.addToken=en(r.addToken,a)),r.map=[],nn(o,r,ht(e,o,t!=e.display.externalMeasured&&Ze(o))),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=O(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=O(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(Ee(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var l=r.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return pe(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=O(r.pre.className,r.textClass||"")),r}function Qt(e){var t=T("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Jt(e,t,n,r,i,o,s){if(t){var u,c=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,d=e.cm.state.specialChars,h=!1;if(d.test(t)){u=document.createDocumentFragment();for(var f=0;;){d.lastIndex=f;var p=d.exec(t),m=p?p.index-f:t.length-f;if(m){var g=document.createTextNode(c.slice(f,f+m));a&&l<9?u.appendChild(T("span",[g])):u.appendChild(g),e.map.push(e.pos,e.pos+m,g),e.col+=m,e.pos+=m}if(!p)break;f+=m+1;var v=void 0;if("\t"==p[0]){var x=e.cm.options.tabSize,y=x-e.col%x;(v=u.appendChild(T("span",V(y),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=y}else"\r"==p[0]||"\n"==p[0]?((v=u.appendChild(T("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),a&&l<9?u.appendChild(T("span",[v])):u.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),a&&l<9&&(h=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),n||r||i||h||o||s){var b=n||"";r&&(b+=r),i&&(b+=i);var D=T("span",[u],b,o);if(s)for(var C in s)s.hasOwnProperty(C)&&"style"!=C&&"class"!=C&&D.setAttribute(C,s[C]);return e.content.appendChild(D)}e.content.appendChild(u)}}function en(e,t){return function(n,r,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=n.pos,c=u+r.length;;){for(var d=void 0,h=0;h<t.length&&!((d=t[h]).to>u&&d.from<=u);h++);if(d.to>=c)return e(n,r,i,o,a,l,s);e(n,r.slice(0,d.to-u),i,o,null,l,s),o=null,r=r.slice(d.to-u),u=d.to}}}function tn(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function nn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var a,l,s,u,c,d,h,f=i.length,p=0,m=1,g="",v=0;;){if(v==p){s=u=c=l="",h=null,d=null,v=1/0;for(var x=[],y=void 0,b=0;b<r.length;++b){var D=r[b],C=D.marker;if("bookmark"==C.type&&D.from==p&&C.widgetNode)x.push(C);else if(D.from<=p&&(null==D.to||D.to>p||C.collapsed&&D.to==p&&D.from==p)){if(null!=D.to&&D.to!=p&&v>D.to&&(v=D.to,u=""),C.className&&(s+=" "+C.className),C.css&&(l=(l?l+";":"")+C.css),C.startStyle&&D.from==p&&(c+=" "+C.startStyle),C.endStyle&&D.to==v&&(y||(y=[])).push(C.endStyle,D.to),C.title&&((h||(h={})).title=C.title),C.attributes)for(var w in C.attributes)(h||(h={}))[w]=C.attributes[w];C.collapsed&&(!d||Bt(d.marker,C)<0)&&(d=D)}else D.from>p&&v>D.from&&(v=D.from)}if(y)for(var k=0;k<y.length;k+=2)y[k+1]==v&&(u+=" "+y[k]);if(!d||d.from==p)for(var S=0;S<x.length;++S)tn(t,0,x[S]);if(d&&(d.from||0)==p){if(tn(t,(null==d.to?f+1:d.to)-p,d.marker,null==d.from),null==d.to)return;d.to==p&&(d=!1)}}if(p>=f)break;for(var F=Math.min(f,v);;){if(g){var A=p+g.length;if(!d){var E=A>F?g.slice(0,F-p):g;t.addToken(t,E,a?a+s:s,c,p+E.length==v?u:"",l,h)}if(A>=F){g=g.slice(F-p),p=F;break}p=A,c=""}g=i.slice(o,o=n[m++]),a=Zt(n[m++],t.cm.options)}}else for(var T=1;T<n.length;T+=2)t.addToken(t,i.slice(o,o=n[T]),Zt(n[T+1],t.cm.options))}function rn(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=It(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}(t),this.size=this.rest?Ze(X(this.rest))-n+1:1,this.node=this.text=null,this.hidden=Wt(e,t)}function on(e,t,n){for(var r,i=[],o=t;o<n;o=r){var a=new rn(e.doc,Ge(e.doc,o),o);r=o+a.size,i.push(a)}return i}var an=null;var ln=null;function sn(e,t){var n=he(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);an?r=an.delayedCallbacks:ln?r=ln:(r=ln=[],setTimeout(un,0));for(var o=function(e){r.push((function(){return n[e].apply(null,i)}))},a=0;a<n.length;++a)o(a)}}function un(){var e=ln;ln=null;for(var t=0;t<e.length;++t)e[t]()}function cn(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?fn(e,t):"gutter"==o?mn(e,t,n,r):"class"==o?pn(e,t):"widget"==o&&gn(e,t,r)}t.changes=null}function dn(e){return e.node==e.text&&(e.node=T("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&l<8&&(e.node.style.zIndex=2)),e.node}function hn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):Yt(e,t)}function fn(e,t){var n=t.text.className,r=hn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,pn(e,t)):n&&(t.text.className=n)}function pn(e,t){!function(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=dn(t);t.background=r.insertBefore(T("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?dn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function mn(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=dn(t);t.gutterBackground=T("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=dn(t),l=t.gutter=T("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(T("div",Je(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var u=e.display.gutterSpecs[s].className,c=o.hasOwnProperty(u)&&o[u];c&&l.appendChild(T("div",[c],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function gn(e,t,n){t.alignable&&(t.alignable=null);for(var r=k("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);xn(e,t,n)}function vn(e,t,n,r){var i=hn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),pn(e,t),mn(e,t,n,r),xn(e,t,r),t.node}function xn(e,t,n){if(yn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)yn(e,t.rest[r],t,n,!1)}function yn(e,t,n,r,i){if(t.widgets)for(var o=dn(n),a=0,l=t.widgets;a<l.length;++a){var s=l[a],u=T("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),bn(s,u,n,r),e.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),sn(s,"redraw")}}function bn(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Dn(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!M(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),E(t.display.measure,T("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Cn(e,t){for(var n=we(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function wn(e){return e.lineSpace.offsetTop}function kn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Sn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=E(e.measure,T("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function Fn(e){return 50-e.display.nativeBarWidth}function An(e){return e.display.scroller.clientWidth-Fn(e)-e.display.barWidth}function En(e){return e.display.scroller.clientHeight-Fn(e)-e.display.barHeight}function Tn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(Ze(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function Ln(e,t,n,r){return Nn(e,Bn(e,t),n,r)}function Mn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[cr(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function Bn(e,t){var n=Ze(t),r=Mn(e,n);r&&!r.text?r=null:r&&r.changes&&(cn(e,r,n,or(e)),e.curOp.forceUpdate=!0),r||(r=function(e,t){var n=Ze(t=Rt(t)),r=e.display.externalMeasured=new rn(e.doc,t,n);r.lineN=n;var i=r.built=Yt(e,r);return r.text=i.pre,E(e.display.lineMeasure,i.pre),r}(e,t));var i=Tn(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Nn(e,t,n,r,i){t.before&&(n=-1);var o,s=n+(r||"");return t.cache.hasOwnProperty(s)?o=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(!function(e,t,n){var r=e.options.lineWrapping,i=r&&An(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],u=a[l+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),o=function(e,t,n,r){var i,o=zn(t.map,n,r),s=o.node,u=o.start,c=o.end,d=o.collapse;if(3==s.nodeType){for(var h=0;h<4;h++){for(;u&&re(t.line.text.charAt(o.coverStart+u));)--u;for(;o.coverStart+c<o.coverEnd&&re(t.line.text.charAt(o.coverStart+c));)++c;if((i=a&&l<9&&0==u&&c==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():Hn(S(s,u,c).getClientRects(),r)).left||i.right||0==u)break;c=u,u-=1,d="right"}a&&l<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Oe)return Oe;var t=E(e,T("span","x")),n=t.getBoundingClientRect(),r=S(t,0,1).getBoundingClientRect();return Oe=Math.abs(n.left-r.left)>1}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}(e.display.measure,i))}else{var f;u>0&&(d=r="right"),i=e.options.lineWrapping&&(f=s.getClientRects()).length>1?f["right"==r?f.length-1:0]:s.getBoundingClientRect()}if(a&&l<9&&!u&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+ir(e.display),top:p.top,bottom:p.bottom}:In}for(var m=i.top-t.rect.top,g=i.bottom-t.rect.top,v=(m+g)/2,x=t.view.measure.heights,y=0;y<x.length-1&&!(v<x[y]);y++);var b=y?x[y-1]:0,D=x[y],C={left:("right"==d?i.right:i.left)-t.rect.left,right:("left"==d?i.left:i.right)-t.rect.left,top:b,bottom:D};i.left||i.right||(C.bogus=!0);e.options.singleCursorHeightPerLine||(C.rtop=m,C.rbottom=g);return C}(e,t,n,r),o.bogus||(t.cache[s]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var On,In={left:0,right:0,top:0,bottom:0};function zn(e,t,n){for(var r,i,o,a,l,s,u=0;u<e.length;u+=3)if(l=e[u],s=e[u+1],t<l?(i=0,o=1,a="left"):t<s?o=(i=t-l)+1:(u==e.length-3||t==s&&e[u+3]>t)&&(i=(o=s-l)-1,t>=s&&(a="right")),null!=i){if(r=e[u+2],l==s&&n==(r.insertLeft?"left":"right")&&(a=n),"left"==n&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[2+(u-=3)],a="left";if("right"==n&&i==s-l)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],a="right";break}return{node:r,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function Hn(e,t){var n=In;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function Rn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Pn(e){e.display.externalMeasure=null,A(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Rn(e.display.view[t])}function _n(e){Pn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Wn(){return c&&g?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function jn(){return c&&g?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function qn(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=Dn(e.widgets[n]));return t}function Un(e,t,n,r,i){if(!i){var o=qn(t);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var a=qt(t);if("local"==r?a+=wn(e.display):a-=e.display.viewOffset,"page"==r||"window"==r){var l=e.display.lineSpace.getBoundingClientRect();a+=l.top+("window"==r?0:jn());var s=l.left+("window"==r?0:Wn());n.left+=s,n.right+=s}return n.top+=a,n.bottom+=a,n}function $n(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=Wn(),i-=jn();else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:r-a.left,top:i-a.top}}function Gn(e,t,n,r,i){return r||(r=Ge(e.doc,t.line)),Un(e,r,Ln(e,r,t.ch,i),n)}function Vn(e,t,n,r,i,o){function a(t,a){var l=Nn(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,Un(e,r,l,n)}r=r||Ge(e.doc,t.line),i||(i=Bn(e,r));var l=ue(r,e.doc.direction),s=t.ch,u=t.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!l)return a("before"==u?s-1:s,"before"==u);function c(e,t,n){return a(n?e-1:e,1==l[t].level!=n)}var d=le(l,s,u),h=ae,f=c(s,d,"before"==u);return null!=h&&(f.other=c(s,h,"before"!=u)),f}function Xn(e,t){var n=0;t=lt(e.doc,t),e.options.lineWrapping||(n=ir(e.display)*t.ch);var r=Ge(e.doc,t.line),i=qt(r)+wn(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function Kn(e,t,n,r,i){var o=et(e,t,n);return o.xRel=i,r&&(o.outside=r),o}function Zn(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return Kn(r.first,0,null,-1,-1);var i=Ye(r,n),o=r.first+r.size-1;if(i>o)return Kn(r.first+r.size-1,Ge(r,o).text.length,null,1,1);t<0&&(t=0);for(var a=Ge(r,i);;){var l=er(e,a,i,t,n),s=zt(a,l.ch+(l.xRel>0||l.outside>0?1:0));if(!s)return l;var u=s.find(1);if(u.line==i)return u;a=Ge(r,i=u.line)}}function Yn(e,t,n,r){r-=qn(t);var i=t.text.length,o=oe((function(t){return Nn(e,n,t-1).bottom<=r}),i,0);return{begin:o,end:i=oe((function(t){return Nn(e,n,t).top>r}),o,i)}}function Qn(e,t,n,r){return n||(n=Bn(e,t)),Yn(e,t,n,Un(e,t,Nn(e,n,r),"line").top)}function Jn(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function er(e,t,n,r,i){i-=qt(t);var o=Bn(e,t),a=qn(t),l=0,s=t.text.length,u=!0,c=ue(t,e.doc.direction);if(c){var d=(e.options.lineWrapping?nr:tr)(e,t,n,o,c,r,i);l=(u=1!=d.level)?d.from:d.to-1,s=u?d.to:d.from-1}var h,f,p=null,m=null,g=oe((function(t){var n=Nn(e,o,t);return n.top+=a,n.bottom+=a,!!Jn(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=t,m=n),!0)}),l,s),v=!1;if(m){var x=r-m.left<m.right-r,y=x==u;g=p+(y?0:1),f=y?"after":"before",h=x?m.left:m.right}else{u||g!=s&&g!=l||g++,f=0==g?"after":g==t.text.length?"before":Nn(e,o,g-(u?1:0)).bottom+a<=i==u?"after":"before";var b=Vn(e,et(n,g,f),"line",t,o);h=b.left,v=i<b.top?-1:i>=b.bottom?1:0}return Kn(n,g=ie(t.text,g,1),f,v,r-h)}function tr(e,t,n,r,i,o,a){var l=oe((function(l){var s=i[l],u=1!=s.level;return Jn(Vn(e,et(n,u?s.to:s.from,u?"before":"after"),"line",t,r),o,a,!0)}),0,i.length-1),s=i[l];if(l>0){var u=1!=s.level,c=Vn(e,et(n,u?s.from:s.to,u?"after":"before"),"line",t,r);Jn(c,o,a,!0)&&c.top>a&&(s=i[l-1])}return s}function nr(e,t,n,r,i,o,a){var l=Yn(e,t,r,a),s=l.begin,u=l.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,d=null,h=0;h<i.length;h++){var f=i[h];if(!(f.from>=u||f.to<=s)){var p=Nn(e,r,1!=f.level?Math.min(u,f.to)-1:Math.max(s,f.from)).right,m=p<o?o-p+1e9:p-o;(!c||d>m)&&(c=f,d=m)}}return c||(c=i[i.length-1]),c.from<s&&(c={from:s,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function rr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==On){On=T("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)On.appendChild(document.createTextNode("x")),On.appendChild(T("br"));On.appendChild(document.createTextNode("x"))}E(e.measure,On);var n=On.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),A(e.measure),n||1}function ir(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=T("span","xxxxxxxxxx"),n=T("pre",[t],"CodeMirror-line-like");E(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function or(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var l=e.display.gutterSpecs[a].className;n[l]=o.offsetLeft+o.clientLeft+i,r[l]=o.clientWidth}return{fixedPos:ar(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function ar(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function lr(e){var t=rr(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/ir(e.display)-3);return function(i){if(Wt(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function sr(e){var t=e.doc,n=lr(e);t.iter((function(e){var t=n(e);t!=e.height&&Ke(e,t)}))}function ur(e,t,n,r){var i=e.display;if(!n&&"true"==we(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(e){return null}var s,u=Zn(e,o,a);if(r&&u.xRel>0&&(s=Ge(e.doc,u.line).text).length==u.ch){var c=R(s,s.length,e.options.tabSize)-s.length;u=et(u.line,Math.max(0,Math.round((o-Sn(e.display).left)/ir(e.display))-c))}return u}function cr(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function dr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Ct&&Pt(e.doc,t)<i.viewTo&&fr(e);else if(n<=i.viewFrom)Ct&&_t(e.doc,n+r)>i.viewFrom?fr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)fr(e);else if(t<=i.viewFrom){var o=pr(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):fr(e)}else if(n>=i.viewTo){var a=pr(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):fr(e)}else{var l=pr(e,t,t,-1),s=pr(e,n,n+r,1);l&&s?(i.view=i.view.slice(0,l.index).concat(on(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):fr(e)}var u=i.externalMeasured;u&&(n<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(i.externalMeasured=null))}function hr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[cr(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==_(a,n)&&a.push(n)}}}function fr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function pr(e,t,n,r){var i,o=cr(e,t),a=e.display.view;if(!Ct||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(r>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,n+=i}for(;Pt(e.doc,n)!=n;){if(o==(r<0?0:a.length-1))return null;n+=r*a[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function mr(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function gr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function vr(e,t){void 0===t&&(t=!0);var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),a=e.options.$customCursor;a&&(t=!0);for(var l=0;l<n.sel.ranges.length;l++)if(t||l!=n.sel.primIndex){var s=n.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var u=s.empty();if(a){var c=a(e,s);c&&xr(e,c,i)}else(u||e.options.showCursorWhenSelecting)&&xr(e,s.head,i);u||br(e,s,o)}}return r}function xr(e,t,n){var r=Vn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(T("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=Gn(e,t,"div",null,null),a=o.right-o.left;i.style.width=(a>0?a:e.defaultCharWidth())+"px"}if(r.other){var l=n.appendChild(T("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=r.other.left+"px",l.style.top=r.other.top+"px",l.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function yr(e,t){return e.top-t.top||e.left-t.left}function br(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),a=Sn(e.display),l=a.left,s=Math.max(r.sizerWidth,An(e)-r.sizer.offsetLeft)-a.right,u="ltr"==i.direction;function c(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),o.appendChild(T("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?s-e:n)+"px;\n                             height: "+(r-t)+"px"))}function d(t,n,r){var o,a,d=Ge(i,t),h=d.text.length;function f(n,r){return Gn(e,et(t,n),"div",d,r)}function p(t,n,r){var i=Qn(e,d,null,t),o="ltr"==n==("after"==r)?"left":"right";return f("after"==r?i.begin:i.end-(/\s/.test(d.text.charAt(i.end-1))?2:1),o)[o]}var m=ue(d,i.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<n&&a.to>t||t==n&&a.to==t)&&(r(Math.max(a.from,t),Math.min(a.to,n),1==a.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(m,n||0,null==r?h:r,(function(e,t,i,d){var g="ltr"==i,v=f(e,g?"left":"right"),x=f(t-1,g?"right":"left"),y=null==n&&0==e,b=null==r&&t==h,D=0==d,C=!m||d==m.length-1;if(x.top-v.top<=3){var w=(u?b:y)&&C,k=(u?y:b)&&D?l:(g?v:x).left,S=w?s:(g?x:v).right;c(k,v.top,S-k,v.bottom)}else{var F,A,E,T;g?(F=u&&y&&D?l:v.left,A=u?s:p(e,i,"before"),E=u?l:p(t,i,"after"),T=u&&b&&C?s:x.right):(F=u?p(e,i,"before"):l,A=!u&&y&&D?s:v.right,E=!u&&b&&C?l:x.left,T=u?p(t,i,"after"):s),c(F,v.top,A-F,v.bottom),v.bottom<x.top&&c(l,v.bottom,null,x.top),c(E,x.top,T-E,x.bottom)}(!o||yr(v,o)<0)&&(o=v),yr(x,o)<0&&(o=x),(!a||yr(v,a)<0)&&(a=v),yr(x,a)<0&&(a=x)})),{start:o,end:a}}var h=t.from(),f=t.to();if(h.line==f.line)d(h.line,h.ch,f.ch);else{var p=Ge(i,h.line),m=Ge(i,f.line),g=Rt(p)==Rt(m),v=d(h.line,h.ch,g?p.text.length+1:null).end,x=d(f.line,g?0:null,f.ch).start;g&&(v.top<x.top-2?(c(v.right,v.top,null,v.bottom),c(l,x.top,x.left,x.bottom)):c(v.right,v.top,x.left-v.right,v.bottom)),v.bottom<x.top&&c(l,v.bottom,null,x.top)}n.appendChild(o)}function Dr(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||Sr(e),t.cursorDiv.style.visibility=(n=!n)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Cr(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||kr(e))}function wr(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Sr(e))}),100)}function kr(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(pe(e,"focus",e,t),e.state.focused=!0,N(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Dr(e))}function Sr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(pe(e,"blur",e,t),e.state.focused=!1,F(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function Fr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,s=0;s<t.view.length;s++){var u=t.view[s],c=e.options.lineWrapping,d=void 0,h=0;if(!u.hidden){if(i+=u.line.height,a&&l<8){var f=u.node.offsetTop+u.node.offsetHeight;d=f-n,n=f}else{var p=u.node.getBoundingClientRect();d=p.bottom-p.top,!c&&u.text.firstChild&&(h=u.text.firstChild.getBoundingClientRect().right-p.left-1)}var m=u.line.height-d;if((m>.005||m<-.005)&&(i<r&&(o-=m),Ke(u.line,d),Ar(u.line),u.rest))for(var g=0;g<u.rest.length;g++)Ar(u.rest[g]);if(h>e.display.sizerWidth){var v=Math.ceil(h/ir(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=u.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function Ar(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function Er(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-wn(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=Ye(t,r),a=Ye(t,i);if(n&&n.ensure){var l=n.ensure.from.line,s=n.ensure.to.line;l<o?(o=l,a=Ye(t,qt(Ge(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=Ye(t,qt(Ge(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function Tr(e,t){var n=e.display,r=rr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=En(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var l=e.doc.height+kn(n),s=t.top<r,u=t.bottom>l-r;if(t.top<i)a.scrollTop=s?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?l:t.bottom)-o);c!=i&&(a.scrollTop=c)}var d=e.options.fixedGutter?0:n.gutters.offsetWidth,h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft-d,f=An(e)-n.gutters.offsetWidth,p=t.right-t.left>f;return p&&(t.right=t.left+f),t.left<10?a.scrollLeft=0:t.left<h?a.scrollLeft=Math.max(0,t.left+d-(p?0:10)):t.right>f+h-3&&(a.scrollLeft=t.right+(p?0:10)-f),a}function Lr(e,t){null!=t&&(Nr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Mr(e){Nr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Br(e,t,n){null==t&&null==n||Nr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function Nr(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,Or(e,Xn(e,t.from),Xn(e,t.to),t.margin))}function Or(e,t,n,r){var i=Tr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});Br(e,i.scrollLeft,i.scrollTop)}function Ir(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||si(e,{top:t}),zr(e,t,!0),n&&si(e),ri(e,100))}function zr(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Hr(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,di(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Rr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+kn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Fn(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var Pr=function(e,t,n){this.cm=n;var r=this.vert=T("div",[T("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=T("div",[T("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),de(r,"scroll",(function(){r.clientHeight&&t(r.scrollTop,"vertical")})),de(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,a&&l<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Pr.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},Pr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Pr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Pr.prototype.zeroWidthHack=function(){var e=x&&!f?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new P,this.disableVert=new P},Pr.prototype.enableZeroWidthBar=function(e,t,n){e.style.pointerEvents="auto",t.set(1e3,(function r(){var i=e.getBoundingClientRect();("vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.pointerEvents="none":t.set(1e3,r)}))},Pr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var _r=function(){};function Wr(e,t){t||(t=Rr(e));var n=e.display.barWidth,r=e.display.barHeight;jr(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&Fr(e),jr(e,Rr(e)),n=e.display.barWidth,r=e.display.barHeight}function jr(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}_r.prototype.update=function(){return{bottom:0,right:0}},_r.prototype.setScrollLeft=function(){},_r.prototype.setScrollTop=function(){},_r.prototype.clear=function(){};var qr={native:Pr,null:_r};function Ur(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new qr[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),de(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,n){"horizontal"==n?Hr(e,t):Ir(e,t)}),e),e.display.scrollbars.addClass&&N(e.display.wrapper,e.display.scrollbars.addClass)}var $r=0;function Gr(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++$r,markArrays:null},t=e.curOp,an?an.ops.push(t):t.ownsGroup=an={ops:[t],delayedCallbacks:[]}}function Vr(e){var t=e.curOp;t&&function(e,t){var n=e.ownsGroup;if(n)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(n)}finally{an=null,t(n)}}(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)Xr(t[n]);for(var r=0;r<t.length;r++)Kr(t[r]);for(var i=0;i<t.length;i++)Zr(t[i]);for(var o=0;o<t.length;o++)Yr(t[o]);for(var a=0;a<t.length;a++)Qr(t[a])}(e)}))}function Xr(e){var t=e.cm,n=t.display;!function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Fn(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Fn(e)+"px",t.scrollbarsClipped=!0)}(t),e.updateMaxLine&&$t(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new oi(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Kr(e){e.updatedDisplay=e.mustUpdate&&ai(e.cm,e.update)}function Zr(e){var t=e.cm,n=t.display;e.updatedDisplay&&Fr(t),e.barMeasure=Rr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Ln(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Fn(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-An(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function Yr(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Hr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==B();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Wr(t,e.barMeasure),e.updatedDisplay&&ci(t,e.barMeasure),e.selectionChanged&&Dr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&Cr(e.cm)}function Qr(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&li(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&zr(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Hr(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=function(e,t,n,r){var i;null==r&&(r=0),e.options.lineWrapping||t!=n||(n="before"==t.sticky?et(t.line,t.ch+1,"before"):t,t=t.ch?et(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var a=!1,l=Vn(e,t),s=n&&n!=t?Vn(e,n):l,u=Tr(e,i={left:Math.min(l.left,s.left),top:Math.min(l.top,s.top)-r,right:Math.max(l.left,s.left),bottom:Math.max(l.bottom,s.bottom)+r}),c=e.doc.scrollTop,d=e.doc.scrollLeft;if(null!=u.scrollTop&&(Ir(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(a=!0)),null!=u.scrollLeft&&(Hr(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-d)>1&&(a=!0)),!a)break}return i}(t,lt(r,e.scrollToPos.from),lt(r,e.scrollToPos.to),e.scrollToPos.margin);!function(e,t){if(!me(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null;if(t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!p){var o=T("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-wn(e.display))+"px;\n                         height: "+(t.bottom-t.top+Fn(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,i)}var o=e.maybeHiddenMarkers,a=e.maybeUnhiddenMarkers;if(o)for(var l=0;l<o.length;++l)o[l].lines.length||pe(o[l],"hide");if(a)for(var s=0;s<a.length;++s)a[s].lines.length&&pe(a[s],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&pe(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Jr(e,t){if(e.curOp)return t();Gr(e);try{return t()}finally{Vr(e)}}function ei(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Gr(e);try{return t.apply(e,arguments)}finally{Vr(e)}}}function ti(e){return function(){if(this.curOp)return e.apply(this,arguments);Gr(this);try{return e.apply(this,arguments)}finally{Vr(this)}}}function ni(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Gr(t);try{return e.apply(this,arguments)}finally{Vr(t)}}}function ri(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,z(ii,e))}function ii(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=ft(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(r.line>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength?je(t.mode,r.state):null,s=dt(e,o,r,!0);l&&(r.state=l),o.styles=s.styles;var u=o.styleClasses,c=s.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var d=!a||a.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),h=0;!d&&h<a.length;++h)d=a[h]!=o.styles[h];d&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&pt(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return ri(e,e.options.workDelay),!0})),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&Jr(e,(function(){for(var t=0;t<i.length;t++)hr(e,i[t],"text")}))}}var oi=function(e,t,n){var r=e.display;this.viewport=t,this.visible=Er(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=An(e),this.force=n,this.dims=or(e),this.events=[]};function ai(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return fr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==mr(e))return!1;hi(e)&&(fr(e),t.dims=or(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>a&&n.viewTo-a<20&&(a=Math.min(i,n.viewTo)),Ct&&(o=Pt(e.doc,o),a=_t(e.doc,a));var l=o!=n.viewFrom||a!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;!function(e,t,n){var r=e.display;0==r.view.length||t>=r.viewTo||n<=r.viewFrom?(r.view=on(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=on(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(cr(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(on(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,cr(e,n)))),r.viewTo=n}(e,o,a),n.viewOffset=qt(Ge(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var u=mr(e);if(!l&&0==u&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var c=function(e){if(e.hasFocus())return null;var t=B();if(!t||!M(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&M(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}(e);return u>4&&(n.lineDiv.style.display="none"),function(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,a=o.firstChild;function l(t){var n=t.nextSibling;return s&&x&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var u=r.view,c=r.viewFrom,d=0;d<u.length;d++){var h=u[d];if(h.hidden);else if(h.node&&h.node.parentNode==o){for(;a!=h.node;)a=l(a);var f=i&&null!=t&&t<=c&&h.lineNumber;h.changes&&(_(h.changes,"gutter")>-1&&(f=!1),cn(e,h,c,n)),f&&(A(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(Je(e.options,c)))),a=h.node.nextSibling}else{var p=vn(e,h,c,n);o.insertBefore(p,a)}c+=h.size}for(;a;)a=l(a)}(e,n.updateLineNumbers,t.dims),u>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,function(e){if(e&&e.activeElt&&e.activeElt!=B()&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&M(document.body,e.anchorNode)&&M(document.body,e.focusNode))){var t=window.getSelection(),n=document.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),t.removeAllRanges(),t.addRange(n),t.extend(e.focusNode,e.focusOffset)}}(c),A(n.cursorDiv),A(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,l&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,ri(e,400)),n.updateLineNumbers=null,!0}function li(e,t){for(var n=t.viewport,r=!0;;r=!1){if(r&&e.options.lineWrapping&&t.oldDisplayWidth!=An(e))r&&(t.visible=Er(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+kn(e.display)-En(e),n.top)}),t.visible=Er(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!ai(e,t))break;Fr(e);var i=Rr(e);gr(e),Wr(e,i),ci(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function si(e,t){var n=new oi(e,t);if(ai(e,n)){Fr(e),li(e,n);var r=Rr(e);gr(e),Wr(e,r),ci(e,r),n.finish()}}function ui(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",sn(e,"gutterChanged",e)}function ci(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Fn(e)+"px"}function di(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=ar(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",a=0;a<n.length;a++)if(!n[a].hidden){e.options.fixedGutter&&(n[a].gutter&&(n[a].gutter.style.left=o),n[a].gutterBackground&&(n[a].gutterBackground.style.left=o));var l=n[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function hi(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=Je(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(T("div",[T("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-a)+1,r.lineNumWidth=r.lineNumInnerWidth+a,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",ui(e.display),!0}return!1}function fi(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;r=!0}n.push({className:o,style:a})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function pi(e){var t=e.gutters,n=e.gutterSpecs;A(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,a=i.style,l=t.appendChild(T("div",null,"CodeMirror-gutter "+o));a&&(l.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=l,l.style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",ui(e)}function mi(e){pi(e.display),dr(e),di(e)}function gi(e,t,r,i){var o=this;this.input=r,o.scrollbarFiller=T("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=T("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=L("div",null,"CodeMirror-code"),o.selectionDiv=T("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=T("div",null,"CodeMirror-cursors"),o.measure=T("div",null,"CodeMirror-measure"),o.lineMeasure=T("div",null,"CodeMirror-measure"),o.lineSpace=L("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var u=L("div",[o.lineSpace],"CodeMirror-lines");o.mover=T("div",[u],null,"position: relative"),o.sizer=T("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=T("div",null,null,"position: absolute; height: 50px; width: 1px;"),o.gutters=T("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=T("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=T("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),o.wrapper.setAttribute("translate","no"),a&&l<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),s||n&&v||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=fi(i.gutters,i.lineNumbers),pi(o),r.init(o)}oi.prototype.signal=function(e,t){ve(e,t)&&this.events.push(arguments)},oi.prototype.finish=function(){for(var e=0;e<this.events.length;e++)pe.apply(null,this.events[e])};var vi=0,xi=null;function yi(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function bi(e){var t=yi(e);return t.x*=xi,t.y*=xi,t}function Di(e,t){var r=yi(t),i=r.x,o=r.y,a=xi;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,a=1);var l=e.display,u=l.scroller,c=u.scrollWidth>u.clientWidth,h=u.scrollHeight>u.clientHeight;if(i&&c||o&&h){if(o&&x&&s)e:for(var f=t.target,p=l.view;f!=u;f=f.parentNode)for(var m=0;m<p.length;m++)if(p[m].node==f){e.display.currentWheelTarget=f;break e}if(i&&!n&&!d&&null!=a)return o&&h&&Ir(e,Math.max(0,u.scrollTop+o*a)),Hr(e,Math.max(0,u.scrollLeft+i*a)),(!o||o&&h)&&ye(t),void(l.wheelStartX=null);if(o&&null!=a){var g=o*a,v=e.doc.scrollTop,y=v+l.wrapper.clientHeight;g<0?v=Math.max(0,v+g-50):y=Math.min(e.doc.height,y+g+50),si(e,{top:v,bottom:y})}vi<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=u.scrollLeft,l.wheelStartY=u.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var e=u.scrollLeft-l.wheelStartX,t=u.scrollTop-l.wheelStartY,n=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(xi=(xi*vi+n)/(vi+1),++vi)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}a?xi=-.53:n?xi=15:c?xi=-.7:h&&(xi=-1/3);var Ci=function(e,t){this.ranges=e,this.primIndex=t};Ci.prototype.primary=function(){return this.ranges[this.primIndex]},Ci.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!nt(n.anchor,r.anchor)||!nt(n.head,r.head))return!1}return!0},Ci.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new wi(rt(this.ranges[t].anchor),rt(this.ranges[t].head));return new Ci(e,this.primIndex)},Ci.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},Ci.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(tt(t,r.from())>=0&&tt(e,r.to())<=0)return n}return-1};var wi=function(e,t){this.anchor=e,this.head=t};function ki(e,t,n){var r=e&&e.options.selectionsMayTouch,i=t[n];t.sort((function(e,t){return tt(e.from(),t.from())})),n=_(t,i);for(var o=1;o<t.length;o++){var a=t[o],l=t[o-1],s=tt(l.to(),a.from());if(r&&!a.empty()?s>0:s>=0){var u=ot(l.from(),a.from()),c=it(l.to(),a.to()),d=l.empty()?a.from()==a.head:l.from()==l.head;o<=n&&--n,t.splice(--o,2,new wi(d?c:u,d?u:c))}}return new Ci(t,n)}function Si(e,t){return new Ci([new wi(e,t||e)],0)}function Fi(e){return e.text?et(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Ai(e,t){if(tt(e,t.from)<0)return e;if(tt(e,t.to)<=0)return Fi(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=Fi(t).ch-t.to.ch),et(n,r)}function Ei(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new wi(Ai(i.anchor,t),Ai(i.head,t)))}return ki(e.cm,n,e.sel.primIndex)}function Ti(e,t,n){return e.line==t.line?et(n.line,e.ch-t.ch+n.ch):et(n.line+(e.line-t.line),e.ch)}function Li(e){e.doc.mode=Pe(e.options,e.doc.modeOption),Mi(e)}function Mi(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,ri(e,100),e.state.modeGen++,e.curOp&&dr(e)}function Bi(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ni(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){!function(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Et(e),Tt(e,n);var i=r?r(e):1;i!=e.height&&Ke(e,i)}(e,n,i,r),sn(e,"change",e,t)}function a(e,t){for(var n=[],o=e;o<t;++o)n.push(new Gt(u[o],i(o),r));return n}var l=t.from,s=t.to,u=t.text,c=Ge(e,l.line),d=Ge(e,s.line),h=X(u),f=i(u.length-1),p=s.line-l.line;if(t.full)e.insert(0,a(0,u.length)),e.remove(u.length,e.size-u.length);else if(Bi(e,t)){var m=a(0,u.length-1);o(d,d.text,f),p&&e.remove(l.line,p),m.length&&e.insert(l.line,m)}else if(c==d)if(1==u.length)o(c,c.text.slice(0,l.ch)+h+c.text.slice(s.ch),f);else{var g=a(1,u.length-1);g.push(new Gt(h+c.text.slice(s.ch),f,r)),o(c,c.text.slice(0,l.ch)+u[0],i(0)),e.insert(l.line+1,g)}else if(1==u.length)o(c,c.text.slice(0,l.ch)+u[0]+d.text.slice(s.ch),i(0)),e.remove(l.line+1,p);else{o(c,c.text.slice(0,l.ch)+u[0],i(0)),o(d,h+d.text.slice(s.ch),f);var v=a(1,u.length-1);p>1&&e.remove(l.line+1,p-1),e.insert(l.line+1,v)}sn(e,"change",e,t)}function Oi(e,t,n){!function e(r,i,o){if(r.linked)for(var a=0;a<r.linked.length;++a){var l=r.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;n&&!s||(t(l.doc,s),e(l.doc,r,s))}}}(e,null,!0)}function Ii(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,sr(e),Li(e),zi(e),e.options.direction=t.direction,e.options.lineWrapping||$t(e),e.options.mode=t.modeOption,dr(e)}function zi(e){("rtl"==e.doc.direction?N:F)(e.display.lineDiv,"CodeMirror-rtl")}function Hi(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Ri(e,t){var n={from:rt(t.from),to:Fi(t),text:Ve(e,t.from,t.to)};return qi(e,n,t.from.line,t.to.line+1),Oi(e,(function(e){return qi(e,n,t.from.line,t.to.line+1)}),!0),n}function Pi(e){for(;e.length;){if(!X(e).ranges)break;e.pop()}}function _i(e,t,n,r){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>l-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Pi(e.done),X(e.done)):e.done.length&&!X(e.done).ranges?X(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),X(e.done)):void 0}(i,i.lastOp==r)))a=X(o.changes),0==tt(t.from,t.to)&&0==tt(t.from,a.to)?a.to=Fi(t):o.changes.push(Ri(e,t));else{var s=X(i.done);for(s&&s.ranges||ji(e.sel,i.done),o={changes:[Ri(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,a||pe(e,"historyAdded")}function Wi(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,X(i.done),t))?i.done[i.done.length-1]=t:ji(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&Pi(i.undone)}function ji(e,t){var n=X(t);n&&n.ranges&&n.equals(e)||t.push(e)}function qi(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),(function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o}))}function Ui(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function $i(e,t){var n=function(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(Ui(n[i]));return r}(e,t),r=Ft(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],a=r[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}else a&&(n[i]=a)}return n}function Gi(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?Ci.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];r.push({changes:l});for(var s=0;s<a.length;++s){var u=a[s],c=void 0;if(l.push({from:u.from,to:u.to,text:u.text}),t)for(var d in u)(c=d.match(/^spans_(\d+)$/))&&_(t,Number(c[1]))>-1&&(X(l)[d]=u[d],delete u[d])}}}return r}function Vi(e,t,n,r){if(r){var i=e.anchor;if(n){var o=tt(t,i)<0;o!=tt(n,i)<0?(i=t,t=n):o!=tt(t,n)<0&&(t=n)}return new wi(i,t)}return new wi(n||t,t)}function Xi(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Ji(e,new Ci([Vi(e.sel.primary(),t,n,i)],0),r)}function Ki(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Vi(e.sel.ranges[o],t[o],null,i);Ji(e,ki(e.cm,r,e.sel.primIndex),n)}function Zi(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,Ji(e,ki(e.cm,i,e.sel.primIndex),r)}function Yi(e,t,n,r){Ji(e,Si(t,n),r)}function Qi(e,t,n){var r=e.history.done,i=X(r);i&&i.ranges?(r[r.length-1]=t,eo(e,t,n)):Ji(e,t,n)}function Ji(e,t,n){eo(e,t,n),Wi(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function eo(e,t,n){(ve(e,"beforeSelectionChange")||e.cm&&ve(e.cm,"beforeSelectionChange"))&&(t=function(e,t,n){var r={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new wi(lt(e,t[n].anchor),lt(e,t[n].head))},origin:n&&n.origin};return pe(e,"beforeSelectionChange",e,r),e.cm&&pe(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?ki(e.cm,r.ranges,r.ranges.length-1):t}(e,t,n));var r=n&&n.bias||(tt(t.primary().head,e.sel.primary().head)<0?-1:1);to(e,ro(e,t,r,!0)),n&&!1===n.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||Mr(e.cm)}function to(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,ge(e.cm)),sn(e,"cursorActivity",e))}function no(e){to(e,ro(e,e.sel,null,!1))}function ro(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=oo(e,a.anchor,l&&l.anchor,n,r),u=oo(e,a.head,l&&l.head,n,r);(i||s!=a.anchor||u!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new wi(s,u))}return i?ki(e.cm,i,t.primIndex):t}function io(e,t,n,r,i){var o=Ge(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,c="selectRight"in s?!s.selectRight:s.inclusiveRight;if((null==l.from||(u?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(c?l.to>=t.ch:l.to>t.ch))){if(i&&(pe(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(n){var d=s.find(r<0?1:-1),h=void 0;if((r<0?c:u)&&(d=ao(e,d,-r,d&&d.line==t.line?o:null)),d&&d.line==t.line&&(h=tt(d,n))&&(r<0?h<0:h>0))return io(e,d,t,r,i)}var f=s.find(r<0?-1:1);return(r<0?u:c)&&(f=ao(e,f,r,f.line==t.line?o:null)),f?io(e,f,t,r,i):null}}return t}function oo(e,t,n,r,i){var o=r||1,a=io(e,t,n,o,i)||!i&&io(e,t,n,o,!0)||io(e,t,n,-o,i)||!i&&io(e,t,n,-o,!0);return a||(e.cantEdit=!0,et(e.first,0))}function ao(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?lt(e,et(t.line-1)):null:n>0&&t.ch==(r||Ge(e,t.line)).text.length?t.line<e.first+e.size-1?et(t.line+1,0):null:new et(t.line,t.ch+n)}function lo(e){e.setSelection(et(e.firstLine(),0),et(e.lastLine()),j)}function so(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=lt(e,t)),n&&(r.to=lt(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),pe(e,"beforeChange",e,r),e.cm&&pe(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function uo(e,t,n){if(e.cm){if(!e.cm.curOp)return ei(e.cm,uo)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(ve(e,"beforeChange")||e.cm&&ve(e.cm,"beforeChange"))||(t=so(e,t,!0))){var r=Dt&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=_(r,n)||(r||(r=[])).push(n)}})),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var a=r[o],l=a.find(0),s=0;s<i.length;++s){var u=i[s];if(!(tt(u.to,l.from)<0||tt(u.from,l.to)>0)){var c=[s,1],d=tt(u.from,l.from),h=tt(u.to,l.to);(d<0||!a.inclusiveLeft&&!d)&&c.push({from:u.from,to:l.from}),(h>0||!a.inclusiveRight&&!h)&&c.push({from:l.to,to:u.to}),i.splice.apply(i,c),s+=c.length-3}}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)co(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else co(e,t)}}function co(e,t){if(1!=t.text.length||""!=t.text[0]||0!=tt(t.from,t.to)){var n=Ei(e,t);_i(e,t,n,e.cm?e.cm.curOp.id:NaN),po(e,t,n,Ft(e,t));var r=[];Oi(e,(function(e,n){n||-1!=_(r,e.history)||(xo(e.history,t),r.push(e.history)),po(e,t,null,Ft(e,t))}))}}function ho(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!r||n){for(var i,o=e.history,a=e.sel,l="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,u=0;u<l.length&&(i=l[u],n?!i.ranges||i.equals(e.sel):i.ranges);u++);if(u!=l.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=l.pop()).ranges){if(r)return void l.push(i);break}if(ji(i,s),n&&!i.equals(e.sel))return void Ji(e,i,{clearRedo:!1});a=i}var c=[];ji(a,s),s.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var d=ve(e,"beforeChange")||e.cm&&ve(e.cm,"beforeChange"),h=function(n){var r=i.changes[n];if(r.origin=t,d&&!so(e,r,!1))return l.length=0,{};c.push(Ri(e,r));var o=n?Ei(e,r):X(l);po(e,r,o,$i(e,r)),!n&&e.cm&&e.cm.scrollIntoView({from:r.from,to:Fi(r)});var a=[];Oi(e,(function(e,t){t||-1!=_(a,e.history)||(xo(e.history,r),a.push(e.history)),po(e,r,null,$i(e,r))}))},f=i.changes.length-1;f>=0;--f){var p=h(f);if(p)return p.v}}}}function fo(e,t){if(0!=t&&(e.first+=t,e.sel=new Ci(K(e.sel.ranges,(function(e){return new wi(et(e.anchor.line+t,e.anchor.ch),et(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){dr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)hr(e.cm,r,"gutter")}}function po(e,t,n,r){if(e.cm&&!e.cm.curOp)return ei(e.cm,po)(e,t,n,r);if(t.to.line<e.first)fo(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);fo(e,i),t={from:et(e.first,0),to:et(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:et(o,Ge(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Ve(e,t.from,t.to),n||(n=Ei(e,t)),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=Ze(Rt(Ge(r,o.line))),r.iter(s,a.line+1,(function(e){if(e==i.maxLine)return l=!0,!0})));r.sel.contains(t.from,t.to)>-1&&ge(e);Ni(r,t,n,lr(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,(function(e){var t=Ut(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)})),l&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=Ge(e,r).stateAfter;if(i&&(!(i instanceof ut)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}})(r,o.line),ri(e,400);var u=t.text.length-(a.line-o.line)-1;t.full?dr(e):o.line!=a.line||1!=t.text.length||Bi(e.doc,t)?dr(e,o.line,a.line+1,u):hr(e,o.line,"text");var c=ve(e,"changes"),d=ve(e,"change");if(d||c){var h={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&sn(e,"change",e,h),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(h)}e.display.selForContextMenu=null}(e.cm,t,r):Ni(e,t,r),eo(e,n,j),e.cantEdit&&oo(e,et(e.firstLine(),0))&&(e.cantEdit=!1)}}function mo(e,t,n,r,i){var o;r||(r=n),tt(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),uo(e,{from:n,to:r,text:t,origin:i})}function go(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function vo(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)go(o.ranges[l].anchor,t,n,r),go(o.ranges[l].head,t,n,r)}else{for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(n<u.from.line)u.from=et(u.from.line+r,u.from.ch),u.to=et(u.to.line+r,u.to.ch);else if(t<=u.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function xo(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;vo(e.done,n,r,i),vo(e.undone,n,r,i)}function yo(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=Ge(e,at(e,t)):i=Ze(t),null==i?null:(r(o,i)&&e.cm&&hr(e.cm,i,n),o)}function bo(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function Do(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}wi.prototype.from=function(){return ot(this.anchor,this.head)},wi.prototype.to=function(){return it(this.anchor,this.head)},wi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},bo.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,Vt(i),sn(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},Do.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),a=r.height;if(r.removeInner(e,o),this.height-=a-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof bo))){var l=[];this.collapse(l),this.children=[new bo(l)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,l=a;l<i.lines.length;){var s=new bo(i.lines.slice(l,l+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new Do(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var n=_(e.parent.children,e);e.parent.children.splice(n+1,0,t)}else{var r=new Do(e.children);r.parent=e,e.children=[r,t],e=r}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,n))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var Co=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};function wo(e,t,n){qt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Lr(e,n)}Co.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=Ze(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=Dn(this);Ke(n,Math.max(0,n.height-o)),e&&(Jr(e,(function(){wo(e,n,-o),hr(e,r,"widget")})),sn(e,"lineWidgetCleared",e,this,r))}},Co.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=Dn(this)-t;i&&(Wt(this.doc,r)||Ke(r,r.height+i),n&&Jr(n,(function(){n.curOp.forceUpdate=!0,wo(n,r,i),sn(n,"lineWidgetChanged",n,e,Ze(r))})))},xe(Co);var ko=0,So=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++ko};function Fo(e,t,n,r,i){if(r&&r.shared)return function(e,t,n,r,i){(r=H(r)).shared=!1;var o=[Fo(e,t,n,r,i)],a=o[0],l=r.widgetNode;return Oi(e,(function(e){l&&(r.widgetNode=l.cloneNode(!0)),o.push(Fo(e,lt(e,t),lt(e,n),r,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=X(o)})),new Ao(o,a)}(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return ei(e.cm,Fo)(e,t,n,r,i);var o=new So(e,i),a=tt(t,n);if(r&&H(r,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=L("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Ht(e,t.line,t,n,o)||t.line!=n.line&&Ht(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Ct=!0}o.addToHistory&&_i(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var l,s=t.line,u=e.cm;if(e.iter(s,n.line+1,(function(r){u&&o.collapsed&&!u.options.lineWrapping&&Rt(r)==u.display.maxLine&&(l=!0),o.collapsed&&s!=t.line&&Ke(r,0),function(e,t,n){var r=n&&window.WeakSet&&(n.markedSpans||(n.markedSpans=new WeakSet));r&&r.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],r&&r.add(e.markedSpans)),t.marker.attachLine(e)}(r,new wt(o,s==t.line?t.ch:null,s==n.line?n.ch:null),e.cm&&e.cm.curOp),++s})),o.collapsed&&e.iter(t.line,n.line+1,(function(t){Wt(e,t)&&Ke(t,0)})),o.clearOnEnter&&de(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Dt=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++ko,o.atomic=!0),u){if(l&&(u.curOp.updateMaxLine=!0),o.collapsed)dr(u,t.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=t.line;c<=n.line;c++)hr(u,c,"text");o.atomic&&no(u.doc),sn(u,"markerAdded",u,o)}return o}So.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Gr(e),ve(this,"clear")){var n=this.find();n&&sn(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],l=kt(a.markedSpans,this);e&&!this.collapsed?hr(e,Ze(a),"text"):e&&(null!=l.to&&(i=Ze(a)),null!=l.from&&(r=Ze(a))),a.markedSpans=St(a.markedSpans,l),null==l.from&&this.collapsed&&!Wt(this.doc,a)&&e&&Ke(a,rr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=Rt(this.lines[s]),c=Ut(u);c>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=c,e.display.maxLineChanged=!0)}null!=r&&e&&this.collapsed&&dr(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&no(e.doc)),e&&sn(e,"markerCleared",e,this,r,i),t&&Vr(e),this.parent&&this.parent.clear()}},So.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=kt(o.markedSpans,this);if(null!=a.from&&(n=et(t?o:Ze(o),a.from),-1==e))return n;if(null!=a.to&&(r=et(t?o:Ze(o),a.to),1==e))return r}return n&&{from:n,to:r}},So.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;t&&r&&Jr(r,(function(){var i=t.line,o=Ze(t.line),a=Mn(r,o);if(a&&(Rn(a),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Wt(n.doc,i)&&null!=n.height){var l=n.height;n.height=null;var s=Dn(n)-l;s&&Ke(i,i.height+s)}sn(r,"markerChanged",r,e)}))},So.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=_(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},So.prototype.detachLine=function(e){if(this.lines.splice(_(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},xe(So);var Ao=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function Eo(e){return e.findMarks(et(e.first,0),e.clipPos(et(e.lastLine())),(function(e){return e.parent}))}function To(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];Oi(n.primary.doc,(function(e){return r.push(e)}));for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==_(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}Ao.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();sn(this,"clear")}},Ao.prototype.find=function(e,t){return this.primary.find(e,t)},xe(Ao);var Lo=0,Mo=function(e,t,n,r,i){if(!(this instanceof Mo))return new Mo(e,t,n,r,i);null==n&&(n=0),Do.call(this,[new bo([new Gt("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=et(n,0);this.sel=Si(o),this.history=new Hi(null),this.id=++Lo,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ni(this,{from:o,to:o,text:e}),Ji(this,Si(o),j)};Mo.prototype=Y(Do.prototype,{constructor:Mo,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Xe(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:ni((function(e){var t=et(this.first,0),n=this.first+this.size-1;uo(this,{from:t,to:et(n,Ge(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Br(this.cm,0,0),Ji(this,Si(t),j)})),replaceRange:function(e,t,n,r){mo(this,e,t=lt(this,t),n=n?lt(this,n):t,r)},getRange:function(e,t,n){var r=Ve(this,lt(this,e),lt(this,t));return!1===n?r:""===n?r.join(""):r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(Qe(this,e))return Ge(this,e)},getLineNumber:function(e){return Ze(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=Ge(this,e)),Rt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return lt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:ni((function(e,t,n){Yi(this,lt(this,"number"==typeof e?et(e,t||0):e),null,n)})),setSelection:ni((function(e,t,n){Yi(this,lt(this,e),lt(this,t||e),n)})),extendSelection:ni((function(e,t,n){Xi(this,lt(this,e),t&&lt(this,t),n)})),extendSelections:ni((function(e,t){Ki(this,st(this,e),t)})),extendSelectionsBy:ni((function(e,t){Ki(this,st(this,K(this.sel.ranges,e)),t)})),setSelections:ni((function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new wi(lt(this,e[i].anchor),lt(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Ji(this,ki(this.cm,r,t),n)}})),addSelection:ni((function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new wi(lt(this,e),lt(this,t||e))),Ji(this,ki(this.cm,r,r.length-1),n)})),getSelection:function(e){for(var t,n=this.sel.ranges,r=0;r<n.length;r++){var i=Ve(this,n[r].from(),n[r].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=Ve(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:ni((function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];r[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:n}}for(var l=t&&"end"!=t&&function(e,t,n){for(var r=[],i=et(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=Ti(l.from,i,o),u=Ti(Fi(l),i,o);if(i=l.to,o=u,"around"==n){var c=e.sel.ranges[a],d=tt(c.head,c.anchor)<0;r[a]=new wi(d?u:s,d?s:u)}else r[a]=new wi(s,s)}return new Ci(r,e.sel.primIndex)}(this,r,t),s=r.length-1;s>=0;s--)uo(this,r[s]);l?Qi(this,l):this.cm&&Mr(this.cm)})),undo:ni((function(){ho(this,"undo")})),redo:ni((function(){ho(this,"redo")})),undoSelection:ni((function(){ho(this,"undo",!0)})),redoSelection:ni((function(){ho(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var e=this;this.history=new Hi(this.history),Oi(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Gi(this.history.done),undone:Gi(this.history.undone)}},setHistory:function(e){var t=this.history=new Hi(this.history);t.done=Gi(e.done.slice(0),null,!0),t.undone=Gi(e.undone.slice(0),null,!0)},setGutterMarker:ni((function(e,t,n){return yo(this,e,"gutter",(function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&te(r)&&(e.gutterMarkers=null),!0}))})),clearGutter:ni((function(e){var t=this;this.iter((function(n){n.gutterMarkers&&n.gutterMarkers[e]&&yo(t,n,"gutter",(function(){return n.gutterMarkers[e]=null,te(n.gutterMarkers)&&(n.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!Qe(this,e))return null;if(t=e,!(e=Ge(this,e)))return null}else if(null==(t=Ze(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:ni((function(e,t,n){return yo(this,e,"gutter"==t?"gutter":"class",(function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[r]){if(k(n).test(e[r]))return!1;e[r]+=" "+n}else e[r]=n;return!0}))})),removeLineClass:ni((function(e,t,n){return yo(this,e,"gutter"==t?"gutter":"class",(function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[r];if(!i)return!1;if(null==n)e[r]=null;else{var o=i.match(k(n));if(!o)return!1;var a=o.index+o[0].length;e[r]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0}))})),addLineWidget:ni((function(e,t,n){return function(e,t,n,r){var i=new Co(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),yo(e,t,"widget",(function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Wt(e,t)){var r=qt(t)<e.scrollTop;Ke(t,t.height+Dn(i)),r&&Lr(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&sn(o,"lineWidgetAdded",o,i,"number"==typeof t?t:Ze(t)),i}(this,e,t,n)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return Fo(this,lt(this,e),lt(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return Fo(this,e=lt(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=Ge(this,(e=lt(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=lt(this,e),t=lt(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i})),r},getAllMarks:function(){var e=[];return this.iter((function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)})),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n})),lt(this,et(n,t))},indexFromPos:function(e){var t=(e=lt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+n})),t},copy:function(e){var t=new Mo(Xe(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new Mo(Xe(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(tt(o,a)){var l=Fo(e,o,a,r.primary,r.primary.type);r.markers.push(l),l.parent=r}}}(r,Eo(this)),r},unlinkDoc:function(e){if(e instanceof Aa&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),To(Eo(this));break}}if(e.history==this.history){var n=[e.id];Oi(e,(function(e){return n.push(e.id)}),!0),e.history=new Hi(null),e.history.done=Gi(this.history.done,n),e.history.undone=Gi(this.history.undone,n)}},iterLinkedDocs:function(e){Oi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Me(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:ni((function(e){var t;("rtl"!=e&&(e="ltr"),e!=this.direction)&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&Jr(t=this.cm,(function(){zi(t),dr(t)})))}))}),Mo.prototype.eachLine=Mo.prototype.iter;var Bo=0;function No(e){var t=this;if(Oo(t),!me(t,e)&&!Cn(t.display,e)){ye(e),a&&(Bo=+new Date);var n=ur(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,s=function(){++l==i&&ei(t,(function(){var e={from:n=lt(t.doc,n),to:n,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};uo(t.doc,e),Qi(t.doc,Si(lt(t.doc,n),lt(t.doc,Fi(e))))}))()},u=function(e,n){if(t.options.allowDropFileTypes&&-1==_(t.options.allowDropFileTypes,e.type))s();else{var r=new FileReader;r.onerror=function(){return s()},r.onload=function(){var e=r.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[n]=e),s()},r.readAsText(e)}},c=0;c<r.length;c++)u(r[c],c);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var d=e.dataTransfer.getData("Text");if(d){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),eo(t.doc,Si(n,n)),h)for(var f=0;f<h.length;++f)mo(t.doc,"",h[f].anchor,h[f].head,"drag");t.replaceSelection(d,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Oo(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Io(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<t.length;r++){var i=t[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation((function(){for(var t=0;t<n.length;t++)e(n[t])}))}}var zo=!1;function Ho(){var e;zo||(de(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Io(Ro)}),100))})),de(window,"blur",(function(){return Io(Sr)})),zo=!0)}function Ro(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Po={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},_o=0;_o<10;_o++)Po[_o+48]=Po[_o+96]=String(_o);for(var Wo=65;Wo<=90;Wo++)Po[Wo]=String.fromCharCode(Wo);for(var jo=1;jo<=12;jo++)Po[jo+111]=Po[jo+63235]="F"+jo;var qo={};function Uo(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))n=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function $o(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=K(n.split(" "),Uo),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=r):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[n]}for(var u in t)e[u]=t[u];return e}function Go(e,t,n,r){var i=(t=Zo(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Go(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var a=Go(e,t.fallthrough[o],n,r);if(a)return a}}}function Vo(e){var t="string"==typeof e?e:Po[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Xo(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(C?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(C?t.ctrlKey:t.metaKey)&&"Mod"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function Ko(e,t){if(d&&34==e.keyCode&&e.char)return!1;var n=Po[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),Xo(n,e,t))}function Zo(e){return"string"==typeof e?qo[e]:e}function Yo(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&tt(o.from,X(r).to)<=0;){var a=r.pop();if(tt(a.from,o.from)<0){o.from=a.from;break}}r.push(o)}Jr(e,(function(){for(var t=r.length-1;t>=0;t--)mo(e.doc,"",r[t].from,r[t].to,"+delete");Mr(e)}))}function Qo(e,t,n){var r=ie(e.text,t+n,n);return r<0||r>e.text.length?null:r}function Jo(e,t,n){var r=Qo(e,t.ch,n);return null==r?null:new et(t.line,r,n<0?"after":"before")}function ea(e,t,n,r,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=ue(n,t.doc.direction);if(o){var a,l=i<0?X(o):o[0],s=i<0==(1==l.level)?"after":"before";if(l.level>0||"rtl"==t.doc.direction){var u=Bn(t,n);a=i<0?n.text.length-1:0;var c=Nn(t,u,a).top;a=oe((function(e){return Nn(t,u,e).top==c}),i<0==(1==l.level)?l.from:l.to-1,a),"before"==s&&(a=Qo(n,a,1))}else a=i<0?l.to:l.from;return new et(r,a,s)}}return new et(r,i<0?n.text.length:0,i<0?"before":"after")}qo.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},qo.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},qo.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},qo.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},qo.default=x?qo.macDefault:qo.pcDefault;var ta={selectAll:lo,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),j)},killLine:function(e){return Yo(e,(function(t){if(t.empty()){var n=Ge(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:et(t.head.line+1,0)}:{from:t.head,to:et(t.head.line,n)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Yo(e,(function(t){return{from:et(t.from().line,0),to:lt(e.doc,et(t.to().line+1,0))}}))},delLineLeft:function(e){return Yo(e,(function(e){return{from:et(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Yo(e,(function(t){var n=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:n},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Yo(e,(function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(et(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(et(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return na(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return ra(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return function(e,t){var n=Ge(e.doc,t),r=function(e){for(var t;t=It(e);)e=t.find(1,!0).line;return e}(n);r!=n&&(t=Ze(r));return ea(!0,e,n,t,-1)}(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")}),U)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")}),U)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?ra(e,t.head):r}),U)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),a=R(e.getLine(o.line),o.ch,r);t.push(V(r-a%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Jr(e,(function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=Ge(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new et(i.line,i.ch-1)),i.ch>0)i=new et(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),et(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=Ge(e.doc,i.line-1).text;a&&(i=new et(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),et(i.line-1,a.length-1),i,"+transpose"))}n.push(new wi(i,i))}e.setSelections(n)}))},newlineAndIndent:function(e){return Jr(e,(function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Mr(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function na(e,t){var n=Ge(e.doc,t),r=Rt(n);return r!=n&&(t=Ze(r)),ea(!0,e,r,t,1)}function ra(e,t){var n=na(e,t.line),r=Ge(e.doc,n.line),i=ue(r,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(n.ch,r.text.search(/\S/)),a=t.line==n.line&&t.ch<=o&&t.ch;return et(n.line,a?0:o,n.sticky)}return n}function ia(e,t,n){if("string"==typeof t&&!(t=ta[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=W}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var oa=new P;function aa(e,t,n,r){var i=e.state.keySeq;if(i){if(Vo(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:oa.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),la(e,i+" "+t,n,r))return!0}return la(e,t,n,r)}function la(e,t,n,r){var i=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Go(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Go(t,e.options.extraKeys,n,e)||Go(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&sn(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(ye(n),Dr(e)),!!i}function sa(e,t){var n=Ko(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?aa(e,"Shift-"+n,t,(function(t){return ia(e,t,!0)}))||aa(e,n,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return ia(e,t)})):aa(e,n,t,(function(t){return ia(e,t)})))}var ua=null;function ca(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=B(),me(t,e)))){a&&l<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var i=sa(t,e);d&&(ua=i?r:null,i||88!=r||Ne||!(x?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),n&&!x&&!i&&46==r&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(F(t,"CodeMirror-crosshair"),fe(document,"keyup",n),fe(document,"mouseover",n))}N(t,"CodeMirror-crosshair"),de(document,"keyup",n),de(document,"mouseover",n)}(t)}}function da(e){16==e.keyCode&&(this.doc.sel.shift=!1),me(this,e)}function ha(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||Cn(t.display,e)||me(t,e)||e.ctrlKey&&!e.altKey||x&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(d&&n==ua)return ua=null,void ye(e);if(!d||e.which&&!(e.which<10)||!sa(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(function(e,t,n){return aa(e,"'"+n+"'",t,(function(t){return ia(e,t,!0)}))}(t,e,i)||t.display.input.onKeyPress(e))}}}var fa,pa,ma=function(e,t,n){this.time=e,this.pos=t,this.button=n};function ga(e){var t=this,n=t.display;if(!(me(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,Cn(n,e))s||(n.scroller.draggable=!1,setTimeout((function(){return n.scroller.draggable=!0}),100));else if(!ya(t,e)){var r=ur(t,e),i=ke(e),o=r?function(e,t){var n=+new Date;return pa&&pa.compare(n,e,t)?(fa=pa=null,"triple"):fa&&fa.compare(n,e,t)?(pa=new ma(n,e,t),fa=null,"double"):(fa=new ma(n,e,t),pa=null,"single")}(r,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),r&&function(e,t,n,r,i){var o="Click";"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o);return aa(e,Xo(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,(function(t){if("string"==typeof t&&(t=ta[t]),!t)return!1;var r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r=t(e,n)!=W}finally{e.state.suppressEdits=!1}return r}))}(t,i,r,o,e)||(1==i?r?function(e,t,n,r){a?setTimeout(z(Cr,e),0):e.curOp.focus=B();var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(null==i.unit){var o=y?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey);null==i.addNew&&(i.addNew=x?n.metaKey:n.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(x?n.altKey:n.ctrlKey));return i}(e,n,r),u=e.doc.sel;e.options.dragDrop&&Ae&&!e.isReadOnly()&&"single"==n&&(i=u.contains(t))>-1&&(tt((i=u.ranges[i]).from(),t)<0||t.xRel>0)&&(tt(i.to(),t)>0||t.xRel<0)?function(e,t,n,r){var i=e.display,o=!1,u=ei(e,(function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:wr(e)),fe(i.wrapper.ownerDocument,"mouseup",u),fe(i.wrapper.ownerDocument,"mousemove",c),fe(i.scroller,"dragstart",d),fe(i.scroller,"drop",u),o||(ye(t),r.addNew||Xi(e.doc,n,null,null,r.extend),s&&!h||a&&9==l?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),c=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},d=function(){return o=!0};s&&(i.scroller.draggable=!0);e.state.draggingText=u,u.copy=!r.moveOnDrag,de(i.wrapper.ownerDocument,"mouseup",u),de(i.wrapper.ownerDocument,"mousemove",c),de(i.scroller,"dragstart",d),de(i.scroller,"drop",u),e.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}(e,r,t,o):function(e,t,n,r){a&&wr(e);var i=e.display,o=e.doc;ye(t);var l,s,u=o.sel,c=u.ranges;r.addNew&&!r.extend?(s=o.sel.contains(n),l=s>-1?c[s]:new wi(n,n)):(l=o.sel.primary(),s=o.sel.primIndex);if("rectangle"==r.unit)r.addNew||(l=new wi(n,n)),n=ur(e,t,!0,!0),s=-1;else{var d=va(e,n,r.unit);l=r.extend?Vi(l,d.anchor,d.head,r.extend):d}r.addNew?-1==s?(s=c.length,Ji(o,ki(e,c.concat([l]),s),{scroll:!1,origin:"*mouse"})):c.length>1&&c[s].empty()&&"char"==r.unit&&!r.extend?(Ji(o,ki(e,c.slice(0,s).concat(c.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),u=o.sel):Zi(o,s,l,q):(s=0,Ji(o,new Ci([l],0),q),u=o.sel);var h=n;function f(t){if(0!=tt(h,t))if(h=t,"rectangle"==r.unit){for(var i=[],a=e.options.tabSize,c=R(Ge(o,n.line).text,n.ch,a),d=R(Ge(o,t.line).text,t.ch,a),f=Math.min(c,d),p=Math.max(c,d),m=Math.min(n.line,t.line),g=Math.min(e.lastLine(),Math.max(n.line,t.line));m<=g;m++){var v=Ge(o,m).text,x=$(v,f,a);f==p?i.push(new wi(et(m,x),et(m,x))):v.length>x&&i.push(new wi(et(m,x),et(m,$(v,p,a))))}i.length||i.push(new wi(n,n)),Ji(o,ki(e,u.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var y,b=l,D=va(e,t,r.unit),C=b.anchor;tt(D.anchor,C)>0?(y=D.head,C=ot(b.from(),D.anchor)):(y=D.anchor,C=it(b.to(),D.head));var w=u.ranges.slice(0);w[s]=function(e,t){var n=t.anchor,r=t.head,i=Ge(e.doc,n.line);if(0==tt(n,r)&&n.sticky==r.sticky)return t;var o=ue(i);if(!o)return t;var a=le(o,n.ch,n.sticky),l=o[a];if(l.from!=n.ch&&l.to!=n.ch)return t;var s,u=a+(l.from==n.ch==(1!=l.level)?0:1);if(0==u||u==o.length)return t;if(r.line!=n.line)s=(r.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var c=le(o,r.ch,r.sticky),d=c-a||(r.ch-n.ch)*(1==l.level?-1:1);s=c==u-1||c==u?d<0:d>0}var h=o[u+(s?-1:0)],f=s==(1==h.level),p=f?h.from:h.to,m=f?"after":"before";return n.ch==p&&n.sticky==m?t:new wi(new et(n.line,p,m),r)}(e,new wi(lt(o,C),y)),Ji(o,ki(e,w,s),q)}}var p=i.wrapper.getBoundingClientRect(),m=0;function g(t){var n=++m,a=ur(e,t,!0,"rectangle"==r.unit);if(a)if(0!=tt(a,h)){e.curOp.focus=B(),f(a);var l=Er(i,o);(a.line>=l.to||a.line<l.from)&&setTimeout(ei(e,(function(){m==n&&g(t)})),150)}else{var s=t.clientY<p.top?-20:t.clientY>p.bottom?20:0;s&&setTimeout(ei(e,(function(){m==n&&(i.scroller.scrollTop+=s,g(t))})),50)}}function v(t){e.state.selectingText=!1,m=1/0,t&&(ye(t),i.input.focus()),fe(i.wrapper.ownerDocument,"mousemove",x),fe(i.wrapper.ownerDocument,"mouseup",y),o.history.lastSelOrigin=null}var x=ei(e,(function(e){0!==e.buttons&&ke(e)?g(e):v(e)})),y=ei(e,v);e.state.selectingText=y,de(i.wrapper.ownerDocument,"mousemove",x),de(i.wrapper.ownerDocument,"mouseup",y)}(e,r,t,o)}(t,r,o,e):we(e)==n.scroller&&ye(e):2==i?(r&&Xi(t.doc,r),setTimeout((function(){return n.input.focus()}),20)):3==i&&(w?t.display.input.onContextMenu(e):wr(t)))}}function va(e,t,n){if("char"==n)return new wi(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new wi(et(t.line,0),lt(e.doc,et(t.line+1,0)));var r=n(e,t);return new wi(r.from,r.to)}function xa(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&ye(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!ve(e,n))return De(t);o-=l.top-a.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var u=a.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i)return pe(e,n,e,Ye(e.doc,o),e.display.gutterSpecs[s].className,t),De(t)}}function ya(e,t){return xa(e,t,"gutterClick",!0)}function ba(e,t){Cn(e.display,t)||function(e,t){if(!ve(e,"gutterContextMenu"))return!1;return xa(e,t,"gutterContextMenu",!1)}(e,t)||me(e,t,"contextmenu")||w||e.display.input.onContextMenu(t)}function Da(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),_n(e)}ma.prototype.compare=function(e,t,n){return this.time+400>e&&0==tt(t,this.pos)&&n==this.button};var Ca={toString:function(){return"CodeMirror.Init"}},wa={},ka={};function Sa(e,t,n){if(!t!=!(n&&n!=Ca)){var r=e.display.dragFunctions,i=t?de:fe;i(e.display.scroller,"dragstart",r.start),i(e.display.scroller,"dragenter",r.enter),i(e.display.scroller,"dragover",r.over),i(e.display.scroller,"dragleave",r.leave),i(e.display.scroller,"drop",r.drop)}}function Fa(e){e.options.lineWrapping?(N(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(F(e.display.wrapper,"CodeMirror-wrap"),$t(e)),sr(e),dr(e),_n(e),setTimeout((function(){return Wr(e)}),100)}function Aa(e,t){var n=this;if(!(this instanceof Aa))return new Aa(e,t);this.options=t=t?H(t):{},H(wa,t,!1);var r=t.value;"string"==typeof r?r=new Mo(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i=new Aa.inputStyles[t.inputStyle](this),o=this.display=new gi(e,r,i,t);for(var u in o.wrapper.CodeMirror=this,Da(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Ur(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new P,keySeq:null,specialChars:null},t.autofocus&&!v&&o.input.focus(),a&&l<11&&setTimeout((function(){return n.display.input.reset(!0)}),20),function(e){var t=e.display;de(t.scroller,"mousedown",ei(e,ga)),de(t.scroller,"dblclick",a&&l<11?ei(e,(function(t){if(!me(e,t)){var n=ur(e,t);if(n&&!ya(e,t)&&!Cn(e.display,t)){ye(t);var r=e.findWordAt(n);Xi(e.doc,r.anchor,r.head)}}})):function(t){return me(e,t)||ye(t)});de(t.scroller,"contextmenu",(function(t){return ba(e,t)})),de(t.input.getField(),"contextmenu",(function(n){t.scroller.contains(n.target)||ba(e,n)}));var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout((function(){return t.activeTouch=null}),1e3),(r=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}de(t.scroller,"touchstart",(function(i){if(!me(e,i)&&!o(i)&&!ya(e,i)){t.input.ensurePolled(),clearTimeout(n);var a=+new Date;t.activeTouch={start:a,moved:!1,prev:a-r.end<=300?r:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),de(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),de(t.scroller,"touchend",(function(n){var r=t.activeTouch;if(r&&!Cn(t,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!r.prev||s(r,r.prev)?new wi(a,a):!r.prev.prev||s(r,r.prev.prev)?e.findWordAt(a):new wi(et(a.line,0),lt(e.doc,et(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),ye(n)}i()})),de(t.scroller,"touchcancel",i),de(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(Ir(e,t.scroller.scrollTop),Hr(e,t.scroller.scrollLeft,!0),pe(e,"scroll",e))})),de(t.scroller,"mousewheel",(function(t){return Di(e,t)})),de(t.scroller,"DOMMouseScroll",(function(t){return Di(e,t)})),de(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){me(e,t)||Ce(t)},over:function(t){me(e,t)||(!function(e,t){var n=ur(e,t);if(n){var r=document.createDocumentFragment();xr(e,n,r),e.display.dragCursor||(e.display.dragCursor=T("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),E(e.display.dragCursor,r)}}(e,t),Ce(t))},start:function(t){return function(e,t){if(a&&(!e.state.draggingText||+new Date-Bo<100))Ce(t);else if(!me(e,t)&&!Cn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!h)){var n=T("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",d&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),d&&n.parentNode.removeChild(n)}}(e,t)},drop:ei(e,No),leave:function(t){me(e,t)||Oo(e)}};var u=t.input.getField();de(u,"keyup",(function(t){return da.call(e,t)})),de(u,"keydown",ei(e,ca)),de(u,"keypress",ei(e,ha)),de(u,"focus",(function(t){return kr(e,t)})),de(u,"blur",(function(t){return Sr(e,t)}))}(this),Ho(),Gr(this),this.curOp.forceUpdate=!0,Ii(this,r),t.autofocus&&!v||this.hasFocus()?setTimeout((function(){n.hasFocus()&&!n.state.focused&&kr(n)}),20):Sr(this),ka)ka.hasOwnProperty(u)&&ka[u](this,t[u],Ca);hi(this),t.finishInit&&t.finishInit(this);for(var c=0;c<Ea.length;++c)Ea[c](this);Vr(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}Aa.defaults=wa,Aa.optionHandlers=ka;var Ea=[];function Ta(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=ft(e,t).state:n="prev");var a=e.options.tabSize,l=Ge(o,t),s=R(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var u,c=l.text.match(/^\s*/)[0];if(r||/\S/.test(l.text)){if("smart"==n&&((u=o.mode.indent(i,l.text.slice(c.length),l.text))==W||u>150)){if(!r)return;n="prev"}}else u=0,n="not";"prev"==n?u=t>o.first?R(Ge(o,t-1).text,null,a):0:"add"==n?u=s+e.options.indentUnit:"subtract"==n?u=s-e.options.indentUnit:"number"==typeof n&&(u=s+n),u=Math.max(0,u);var d="",h=0;if(e.options.indentWithTabs)for(var f=Math.floor(u/a);f;--f)h+=a,d+="\t";if(h<u&&(d+=V(u-h)),d!=c)return mo(o,d,et(t,0),et(t,c.length),"+input"),l.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var m=o.sel.ranges[p];if(m.head.line==t&&m.head.ch<c.length){var g=et(t,c.length);Zi(o,p,new wi(g,g));break}}}Aa.defineInitHook=function(e){return Ea.push(e)};var La=null;function Ma(e){La=e}function Ba(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var a=+new Date-200,l="paste"==i||e.state.pasteIncoming>a,s=Me(t),u=null;if(l&&r.ranges.length>1)if(La&&La.text.join("\n")==t){if(r.ranges.length%La.text.length==0){u=[];for(var c=0;c<La.text.length;c++)u.push(o.splitLines(La.text[c]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=K(s,(function(e){return[e]})));for(var d=e.curOp.updateInput,h=r.ranges.length-1;h>=0;h--){var f=r.ranges[h],p=f.from(),m=f.to();f.empty()&&(n&&n>0?p=et(p.line,p.ch-n):e.state.overwrite&&!l?m=et(m.line,Math.min(Ge(o,m.line).text.length,m.ch+X(s).length)):l&&La&&La.lineWise&&La.text.join("\n")==s.join("\n")&&(p=m=et(p.line,0)));var g={from:p,to:m,text:u?u[h%u.length]:s,origin:i||(l?"paste":e.state.cutIncoming>a?"cut":"+input")};uo(e.doc,g),sn(e,"inputRead",e,g)}t&&!l&&Oa(e,t),Mr(e),e.curOp.updateInput<2&&(e.curOp.updateInput=d),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Na(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Jr(t,(function(){return Ba(t,n,0,null,"paste")})),!0}function Oa(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=Ta(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Ge(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Ta(e,i.head.line,"smart"));a&&sn(e,"electricInput",e,i.head.line)}}}function Ia(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:et(i,0),head:et(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function za(e,t,n,r){e.setAttribute("autocorrect",n?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function Ha(){var e=T("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=T("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),m&&(e.style.border="1px solid black"),za(e),t}function Ra(e,t,n,r,i){var o=t,a=n,l=Ge(e,t.line),s=i&&"rtl"==e.direction?-n:n;function u(o){var a,u;if("codepoint"==r){var c=l.text.charCodeAt(t.ch+(n>0?0:-1));if(isNaN(c))a=null;else{var d=n>0?c>=55296&&c<56320:c>=56320&&c<57343;a=new et(t.line,Math.max(0,Math.min(l.text.length,t.ch+n*(d?2:1))),-n)}}else a=i?function(e,t,n,r){var i=ue(t,e.doc.direction);if(!i)return Jo(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=le(i,n.ch,n.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(r>0?a.to>n.ch:a.from<n.ch))return Jo(t,n,r);var l,s=function(e,n){return Qo(t,e instanceof et?e.ch:e,n)},u=function(n){return e.options.lineWrapping?(l=l||Bn(e,t),Qn(e,t,l,n)):{begin:0,end:t.text.length}},c=u("before"==n.sticky?s(n,-1):n.ch);if("rtl"==e.doc.direction||1==a.level){var d=1==a.level==r<0,h=s(n,d?1:-1);if(null!=h&&(d?h<=a.to&&h<=c.end:h>=a.from&&h>=c.begin)){var f=d?"before":"after";return new et(n.line,h,f)}}var p=function(e,t,r){for(var o=function(e,t){return t?new et(n.line,s(e,1),"before"):new et(n.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],l=t>0==(1!=a.level),u=l?r.begin:s(r.end,-1);if(a.from<=u&&u<a.to)return o(u,l);if(u=l?a.from:s(a.to,-1),r.begin<=u&&u<r.end)return o(u,l)}},m=p(o+r,r,c);if(m)return m;var g=r>0?c.end:s(c.begin,-1);return null==g||r>0&&g==t.text.length||!(m=p(r>0?0:i.length-1,r,u(g)))?null:m}(e.cm,l,t,n):Jo(l,t,n);if(null==a){if(o||(u=t.line+s)<e.first||u>=e.first+e.size||(t=new et(u,t.ch,t.sticky),!(l=Ge(e,u))))return!1;t=ea(i,e.cm,l,t.line,s)}else t=a;return!0}if("char"==r||"codepoint"==r)u();else if("column"==r)u(!0);else if("word"==r||"group"==r)for(var c=null,d="group"==r,h=e.cm&&e.cm.getHelper(t,"wordChars"),f=!0;!(n<0)||u(!f);f=!1){var p=l.text.charAt(t.ch)||"\n",m=ee(p,h)?"w":d&&"\n"==p?"n":!d||/\s/.test(p)?null:"p";if(!d||f||m||(m="s"),c&&c!=m){n<0&&(n=1,u(),t.sticky="after");break}if(m&&(c=m),n>0&&!u(!f))break}var g=oo(e,t,o,a,!0);return nt(o,g)&&(g.hitSide=!0),g}function Pa(e,t,n,r){var i,o,a=e.doc,l=t.left;if("page"==r){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(s-.5*rr(e.display),3);i=(n>0?t.bottom:t.top)+n*u}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(;(o=Zn(e,l,i)).outside;){if(n<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*n}return o}var _a=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new P,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Wa(e,t){var n=Mn(e,t.line);if(!n||n.hidden)return null;var r=Ge(e.doc,t.line),i=Tn(n,r,t.line),o=ue(r,e.doc.direction),a="left";o&&(a=le(o,t.ch)%2?"right":"left");var l=zn(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function ja(e,t){return t&&(e.bad=!0),e}function qa(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return ja(e.clipPos(et(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return Ua(o,t,n)}}function Ua(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!M(r,t))return ja(et(Ze(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?X(e.rest):e.line;return ja(et(Ze(o),o.text.length),i)}var a=3==t.nodeType?t:null,l=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,n&&(n=a.nodeValue.length));l.parentNode!=r;)l=l.parentNode;var s=e.measure,u=s.maps;function c(t,n,r){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?s.map:u[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==n){var c=Ze(i<0?e.line:e.rest[i]),d=o[a]+r;return(r<0||l!=t)&&(d=o[a+(r?1:0)]),et(c,d)}}}var d=c(a,l,n);if(d)return ja(d,i);for(var h=l.nextSibling,f=a?a.nodeValue.length-n:0;h;h=h.nextSibling){if(d=c(h,h.firstChild,0))return ja(et(d.line,d.ch-f),i);f+=h.textContent.length}for(var p=l.previousSibling,m=n;p;p=p.previousSibling){if(d=c(p,p.firstChild,-1))return ja(et(d.line,d.ch+m),i);m+=p.textContent.length}}_a.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function a(e){if(o(e)&&!me(r,e)){if(r.somethingSelected())Ma({lineWise:!1,text:r.getSelections()}),"cut"==e.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var t=Ia(r);Ma({lineWise:!0,text:t.text}),"cut"==e.type&&r.operation((function(){r.setSelections(t.ranges,0,j),r.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var a=La.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a)return void e.preventDefault()}var l=Ha(),s=l.firstChild;r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild),s.value=La.text.join("\n");var u=B();I(s),setTimeout((function(){r.display.lineSpace.removeChild(l),u.focus(),u==i&&n.showPrimarySelection()}),50)}}i.contentEditable=!0,za(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize),de(i,"paste",(function(e){!o(e)||me(r,e)||Na(e,r)||l<=11&&setTimeout(ei(r,(function(){return t.updateFromDOM()})),20)})),de(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),de(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),de(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),de(i,"touchstart",(function(){return n.forceCompositionEnd()})),de(i,"input",(function(){t.composing||t.readFromDOMSoon()})),de(i,"copy",a),de(i,"cut",a)},_a.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},_a.prototype.prepareSelection=function(){var e=vr(this.cm,!1);return e.focus=B()==this.div,e},_a.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},_a.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},_a.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),i=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=qa(t,e.anchorNode,e.anchorOffset),l=qa(t,e.focusNode,e.focusOffset);if(!a||a.bad||!l||l.bad||0!=tt(ot(a,l),i)||0!=tt(it(a,l),o)){var s=t.display.view,u=i.line>=t.display.viewFrom&&Wa(t,i)||{node:s[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&Wa(t,o);if(!c){var d=s[s.length-1].measure,h=d.maps?d.maps[d.maps.length-1]:d.map;c={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(u&&c){var f,p=e.rangeCount&&e.getRangeAt(0);try{f=S(u.node,u.offset,c.offset,c.node)}catch(e){}f&&(!n&&t.state.focused?(e.collapse(u.node,u.offset),f.collapsed||(e.removeAllRanges(),e.addRange(f))):(e.removeAllRanges(),e.addRange(f)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},_a.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},_a.prototype.showMultipleSelections=function(e){E(this.cm.display.cursorDiv,e.cursors),E(this.cm.display.selectionDiv,e.selection)},_a.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},_a.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return M(this.div,t)},_a.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&B()==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},_a.prototype.blur=function(){this.div.blur()},_a.prototype.getField=function(){return this.div},_a.prototype.supportsTouch=function(){return!0},_a.prototype.receivedFocus=function(){var e=this,t=this;this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):Jr(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,(function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))}))},_a.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},_a.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(g&&c&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=qa(t,e.anchorNode,e.anchorOffset),r=qa(t,e.focusNode,e.focusOffset);n&&r&&Jr(t,(function(){Ji(t.doc,Si(n,r),j),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)}))}}},_a.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>r.firstLine()&&(a=et(a.line-1,Ge(r.doc,a.line-1).length)),l.ch==Ge(r.doc,l.line).text.length&&l.line<r.lastLine()&&(l=et(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=cr(r,a.line))?(t=Ze(i.view[0].line),n=i.view[0].node):(t=Ze(i.view[e].line),n=i.view[e-1].node.nextSibling);var s,u,c=cr(r,l.line);if(c==i.view.length-1?(s=i.viewTo-1,u=i.lineDiv.lastChild):(s=Ze(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!n)return!1;for(var d=r.doc.splitLines(function(e,t,n,r,i){var o="",a=!1,l=e.doc.lineSeparator(),s=!1;function u(e){return function(t){return t.id==e}}function c(){a&&(o+=l,s&&(o+=l),a=s=!1)}function d(e){e&&(c(),o+=e)}function h(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void d(n);var o,f=t.getAttribute("cm-marker");if(f){var p=e.findMarks(et(r,0),et(i+1,0),u(+f));return void(p.length&&(o=p[0].find(0))&&d(Ve(e.doc,o.from,o.to).join(l)))}if("false"==t.getAttribute("contenteditable"))return;var m=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;m&&c();for(var g=0;g<t.childNodes.length;g++)h(t.childNodes[g]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),m&&(a=!0)}else 3==t.nodeType&&d(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;h(t),t!=n;)t=t.nextSibling,s=!1;return o}(r,n,u,t,s)),h=Ve(r.doc,et(t,0),et(s,Ge(r.doc,s).text.length));d.length>1&&h.length>1;)if(X(d)==X(h))d.pop(),h.pop(),s--;else{if(d[0]!=h[0])break;d.shift(),h.shift(),t++}for(var f=0,p=0,m=d[0],g=h[0],v=Math.min(m.length,g.length);f<v&&m.charCodeAt(f)==g.charCodeAt(f);)++f;for(var x=X(d),y=X(h),b=Math.min(x.length-(1==d.length?f:0),y.length-(1==h.length?f:0));p<b&&x.charCodeAt(x.length-p-1)==y.charCodeAt(y.length-p-1);)++p;if(1==d.length&&1==h.length&&t==a.line)for(;f&&f>a.ch&&x.charCodeAt(x.length-p-1)==y.charCodeAt(y.length-p-1);)f--,p++;d[d.length-1]=x.slice(0,x.length-p).replace(/^\u200b+/,""),d[0]=d[0].slice(f).replace(/\u200b+$/,"");var D=et(t,f),C=et(s,h.length?X(h).length-p:0);return d.length>1||d[0]||tt(D,C)?(mo(r.doc,d,D,C,"+input"),!0):void 0},_a.prototype.ensurePolled=function(){this.forceCompositionEnd()},_a.prototype.reset=function(){this.forceCompositionEnd()},_a.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},_a.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},_a.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Jr(this.cm,(function(){return dr(e.cm)}))},_a.prototype.setUneditable=function(e){e.contentEditable="false"},_a.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||ei(this.cm,Ba)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},_a.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},_a.prototype.onContextMenu=function(){},_a.prototype.resetPosition=function(){},_a.prototype.needsContentAttribute=!0;var $a=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new P,this.hasSelection=!1,this.composing=null};$a.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!me(r,e)){if(r.somethingSelected())Ma({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var t=Ia(r);Ma({lineWise:!0,text:t.text}),"cut"==e.type?r.setSelections(t.ranges,null,j):(n.prevInput="",i.value=t.text.join("\n"),I(i))}"cut"==e.type&&(r.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),m&&(i.style.width="0px"),de(i,"input",(function(){a&&l>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()})),de(i,"paste",(function(e){me(r,e)||Na(e,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())})),de(i,"cut",o),de(i,"copy",o),de(e.scroller,"paste",(function(t){if(!Cn(e,t)&&!me(r,t)){if(!i.dispatchEvent)return r.state.pasteIncoming=+new Date,void n.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),de(e.lineSpace,"selectstart",(function(t){Cn(e,t)||ye(t)})),de(i,"compositionstart",(function(){var e=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:r.markText(e,r.getCursor("to"),{className:"CodeMirror-composing"})}})),de(i,"compositionend",(function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)}))},$a.prototype.createField=function(e){this.wrapper=Ha(),this.textarea=this.wrapper.firstChild},$a.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},$a.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=vr(e);if(e.options.moveInputWithCursor){var i=Vn(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return r},$a.prototype.showSelection=function(e){var t=this.cm.display;E(t.cursorDiv,e.cursors),E(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},$a.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&I(this.textarea),a&&l>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",a&&l>=9&&(this.hasSelection=null))}},$a.prototype.getField=function(){return this.textarea},$a.prototype.supportsTouch=function(){return!1},$a.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!v||B()!=this.textarea))try{this.textarea.focus()}catch(e){}},$a.prototype.blur=function(){this.textarea.blur()},$a.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},$a.prototype.receivedFocus=function(){this.slowPoll()},$a.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},$a.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,(function n(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))}))},$a.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||Be(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(a&&l>=9&&this.hasSelection===i||x&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,u=Math.min(r.length,i.length);s<u&&r.charCodeAt(s)==i.charCodeAt(s);)++s;return Jr(t,(function(){Ba(t,i.slice(s),r.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},$a.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},$a.prototype.onKeyPress=function(){a&&l>=9&&(this.hasSelection=null),this.fastPoll()},$a.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=ur(n,e),u=r.scroller.scrollTop;if(o&&!d){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(o)&&ei(n,Ji)(n.doc,Si(o),j);var c,h=i.style.cssText,f=t.wrapper.style.cssText,p=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(c=window.scrollY),r.input.focus(),s&&window.scrollTo(null,c),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),a&&l>=9&&g(),w){Ce(e);var m=function(){fe(window,"mouseup",m),setTimeout(v,20)};de(window,"mouseup",m)}else setTimeout(v,50)}function g(){if(null!=i.selectionStart){var e=n.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=f,i.style.cssText=h,a&&l<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=u),null!=i.selectionStart)){(!a||a&&l<9)&&g();var e=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?ei(n,lo)(n):e++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},$a.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},$a.prototype.setUneditable=function(){},$a.prototype.needsContentAttribute=!1,function(e){var t=e.optionHandlers;function n(n,r,i,o){e.defaults[n]=r,i&&(t[n]=o?function(e,t,n){n!=Ca&&i(e,t,n)}:i)}e.defineOption=n,e.Init=Ca,n("value","",(function(e,t){return e.setValue(t)}),!0),n("mode",null,(function(e,t){e.doc.modeOption=t,Li(e)}),!0),n("indentUnit",2,Li,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,(function(e){Mi(e),_n(e),dr(e)}),!0),n("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,n.push(et(r,o))}r++}));for(var i=n.length-1;i>=0;i--)mo(e.doc,t,n[i],et(n[i].line,n[i].ch+t.length))}})),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,(function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=Ca&&e.refresh()})),n("specialCharPlaceholder",Qt,(function(e){return e.refresh()}),!0),n("electricChars",!0),n("inputStyle",v?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),n("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),n("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),n("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),n("rtlMoveVisually",!b),n("wholeLineUpdateBefore",!0),n("theme","default",(function(e){Da(e),mi(e)}),!0),n("keyMap","default",(function(e,t,n){var r=Zo(t),i=n!=Ca&&Zo(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)})),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,Fa,!0),n("gutters",[],(function(e,t){e.display.gutterSpecs=fi(t,e.options.lineNumbers),mi(e)}),!0),n("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?ar(e.display)+"px":"0",e.refresh()}),!0),n("coverGutterNextToScrollbar",!1,(function(e){return Wr(e)}),!0),n("scrollbarStyle","native",(function(e){Ur(e),Wr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),n("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=fi(e.options.gutters,t),mi(e)}),!0),n("firstLineNumber",1,mi,!0),n("lineNumberFormatter",(function(e){return e}),mi,!0),n("showCursorWhenSelecting",!1,gr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,(function(e,t){"nocursor"==t&&(Sr(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),n("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),n("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),n("dragDrop",!0,Sa),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,gr,!0),n("singleCursorHeightPerLine",!0,gr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,Mi,!0),n("addModeClass",!1,Mi,!0),n("pollInterval",100),n("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),n("historyEventDelay",1250),n("viewportMargin",10,(function(e){return e.refresh()}),!0),n("maxHighlightLength",1e4,Mi,!0),n("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),n("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),n("autofocus",null),n("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),n("phrases",null)}(Aa),function(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&ei(this,t[e])(this,n,i),pe(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Zo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:ti((function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");!function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},(function(e){return e.priority})),this.state.modeGen++,dr(this)})),removeOverlay:ti((function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void dr(this)}})),indentLine:ti((function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),Qe(this.doc,e)&&Ta(this,e,t,n)})),indentSelection:ti((function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(Ta(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&Mr(this));else{var o=i.from(),a=i.to(),l=Math.max(n,o.line);n=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var s=l;s<n;++s)Ta(this,s,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&u[r].from().ch>0&&Zi(this.doc,r,new wi(o,u[r].to()),j)}}})),getTokenAt:function(e,t){return xt(this,e,t)},getLineTokens:function(e,t){return xt(this,et(e),t,!0)},getTokenTypeAt:function(e){e=lt(this.doc,e);var t,n=ht(this,Ge(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var a=r+i>>1;if((a?n[2*a-1]:0)>=o)i=a;else{if(!(n[2*a+1]<o)){t=n[2*a+2];break}r=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!n.hasOwnProperty(t))return r;var i=n[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&r.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var l=i[o[t][a]];l&&r.push(l)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var s=0;s<i._global.length;s++){var u=i._global[s];u.pred(o,this)&&-1==_(r,u.val)&&r.push(u.val)}return r},getStateAfter:function(e,t){var n=this.doc;return ft(this,(e=at(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary();return Vn(this,null==e?n.head:"object"==typeof e?lt(this.doc,e):e?n.from():n.to(),t||"page")},charCoords:function(e,t){return Gn(this,lt(this.doc,e),t||"page")},coordsChar:function(e,t){return Zn(this,(e=$n(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=$n(this,{top:e,left:0},t||"page").top,Ye(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=Ge(this.doc,e)}else r=e;return Un(this,r,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-qt(r):0)},defaultTextHeight:function(){return rr(this.display)},defaultCharWidth:function(){return ir(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o,a,l,s=this.display,u=(e=Vn(this,lt(this.doc,e))).bottom,c=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),s.sizer.appendChild(t),"over"==r)u=e.top;else if("above"==r||"near"==r){var d=Math.max(s.wrapper.clientHeight,this.doc.height),h=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>d)&&e.top>t.offsetHeight?u=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=d&&(u=e.bottom),c+t.offsetWidth>h&&(c=h-t.offsetWidth)}t.style.top=u+"px",t.style.left=t.style.right="","right"==i?(c=s.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?c=0:"middle"==i&&(c=(s.sizer.clientWidth-t.offsetWidth)/2),t.style.left=c+"px"),n&&(o=this,a={left:c,top:u,right:c+t.offsetWidth,bottom:u+t.offsetHeight},null!=(l=Tr(o,a)).scrollTop&&Ir(o,l.scrollTop),null!=l.scrollLeft&&Hr(o,l.scrollLeft))},triggerOnKeyDown:ti(ca),triggerOnKeyPress:ti(ha),triggerOnKeyUp:da,triggerOnMouseDown:ti(ga),execCommand:function(e){if(ta.hasOwnProperty(e))return ta[e].call(null,this)},triggerElectric:ti((function(e){Oa(this,e)})),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=lt(this.doc,e),a=0;a<t&&!(o=Ra(this.doc,o,i,n,r)).hitSide;++a);return o},moveH:ti((function(e,t){var n=this;this.extendSelectionsBy((function(r){return n.display.shift||n.doc.extend||r.empty()?Ra(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()}),U)})),deleteH:ti((function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):Yo(this,(function(n){var i=Ra(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}}))})),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var a=lt(this.doc,e),l=0;l<t;++l){var s=Vn(this,a,"div");if(null==o?o=s.left:s.left=o,(a=Pa(this,s,i,n)).hitSide)break}return a},moveV:ti((function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy((function(a){if(o)return e<0?a.from():a.to();var l=Vn(n,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=Pa(n,l,e,t);return"page"==t&&a==r.sel.primary()&&Lr(n,Gn(n,s,"div").top-l.top),s}),U),i.length)for(var a=0;a<r.sel.ranges.length;a++)r.sel.ranges[a].goalColumn=i[a]})),findWordAt:function(e){var t=Ge(this.doc,e.line).text,n=e.ch,r=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&r!=t.length||!n?++r:--n;for(var o=t.charAt(n),a=ee(o,i)?function(e){return ee(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!ee(e)};n>0&&a(t.charAt(n-1));)--n;for(;r<t.length&&a(t.charAt(r));)++r}return new wi(et(e.line,n),et(e.line,r))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?N(this.display.cursorDiv,"CodeMirror-overwrite"):F(this.display.cursorDiv,"CodeMirror-overwrite"),pe(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==B()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:ti((function(e,t){Br(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Fn(this)-this.display.barHeight,width:e.scrollWidth-Fn(this)-this.display.barWidth,clientHeight:En(this),clientWidth:An(this)}},scrollIntoView:ti((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:et(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?function(e,t){Nr(e),e.curOp.scrollToPos=t}(this,e):Or(this,e.from,e.to,e.margin)})),setSize:ti((function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&Pn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){hr(n,i,"widget");break}++i})),this.curOp.forceUpdate=!0,pe(this,"refresh",this)})),operation:function(e){return Jr(this,e)},startOperation:function(){return Gr(this)},endOperation:function(){return Vr(this)},refresh:ti((function(){var e=this.display.cachedTextHeight;dr(this),this.curOp.forceUpdate=!0,_n(this),Br(this,this.doc.scrollLeft,this.doc.scrollTop),ui(this.display),(null==e||Math.abs(e-rr(this.display))>.5||this.options.lineWrapping)&&sr(this),pe(this,"refresh",this)})),swapDoc:ti((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Ii(this,e),_n(this),this.display.input.reset(),Br(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,sn(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},xe(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}(Aa);var Ga="iter insert remove copy getEditor constructor".split(" ");for(var Va in Mo.prototype)Mo.prototype.hasOwnProperty(Va)&&_(Ga,Va)<0&&(Aa.prototype[Va]=function(e){return function(){return e.apply(this.doc,arguments)}}(Mo.prototype[Va]));return xe(Mo),Aa.inputStyles={textarea:$a,contenteditable:_a},Aa.defineMode=function(e){Aa.defaults.mode||"null"==e||(Aa.defaults.mode=e),He.apply(this,arguments)},Aa.defineMIME=function(e,t){ze[e]=t},Aa.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),Aa.defineMIME("text/plain","null"),Aa.defineExtension=function(e,t){Aa.prototype[e]=t},Aa.defineDocExtension=function(e,t){Mo.prototype[e]=t},Aa.fromTextArea=function(e,t){if((t=t?H(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=B();t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function r(){e.value=l.getValue()}var i;if(e.form&&(de(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(n){n.save=r,n.getTextArea=function(){return e},n.toTextArea=function(){n.toTextArea=isNaN,r(),e.parentNode.removeChild(n.getWrapperElement()),e.style.display="",e.form&&(fe(e.form,"submit",r),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var l=Aa((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return l},function(e){e.off=fe,e.on=de,e.wheelEventPixels=bi,e.Doc=Mo,e.splitLines=Me,e.countColumn=R,e.findColumn=$,e.isWordChar=J,e.Pass=W,e.signal=pe,e.Line=Gt,e.changeEnd=Fi,e.scrollbarModel=qr,e.Pos=et,e.cmpPos=tt,e.modes=Ie,e.mimeModes=ze,e.resolveMode=Re,e.getMode=Pe,e.modeExtensions=_e,e.extendMode=We,e.copyState=je,e.startState=Ue,e.innerMode=qe,e.commands=ta,e.keyMap=qo,e.keyName=Ko,e.isModifierKey=Vo,e.lookupKey=Go,e.normalizeKeyMap=$o,e.StringStream=$e,e.SharedTextMarker=Ao,e.TextMarker=So,e.LineWidget=Co,e.e_preventDefault=ye,e.e_stopPropagation=be,e.e_stop=Ce,e.addClass=N,e.contains=M,e.rmClass=F,e.keyNames=Po}(Aa),Aa.version="5.65.0",Aa}))},{}],11:[function(e,t,n){var r;r=function(e){"use strict";var t=/^((?:(?:aaas?|about|acap|adiumxtra|af[ps]|aim|apt|attachment|aw|beshare|bitcoin|bolo|callto|cap|chrome(?:-extension)?|cid|coap|com-eventbrite-attendee|content|crid|cvs|data|dav|dict|dlna-(?:playcontainer|playsingle)|dns|doi|dtn|dvb|ed2k|facetime|feed|file|finger|fish|ftp|geo|gg|git|gizmoproject|go|gopher|gtalk|h323|hcp|https?|iax|icap|icon|im|imap|info|ipn|ipp|irc[6s]?|iris(?:\.beep|\.lwz|\.xpc|\.xpcs)?|itms|jar|javascript|jms|keyparc|lastfm|ldaps?|magnet|mailto|maps|market|message|mid|mms|ms-help|msnim|msrps?|mtqp|mumble|mupdate|mvn|news|nfs|nih?|nntp|notes|oid|opaquelocktoken|palm|paparazzi|platform|pop|pres|proxy|psyc|query|res(?:ource)?|rmi|rsync|rtmp|rtsp|secondlife|service|session|sftp|sgn|shttp|sieve|sips?|skype|sm[bs]|snmp|soap\.beeps?|soldat|spotify|ssh|steam|svn|tag|teamspeak|tel(?:net)?|tftp|things|thismessage|tip|tn3270|tv|udp|unreal|urn|ut2004|vemmi|ventrilo|view-source|webcal|wss?|wtai|wyciwyg|xcon(?:-userid)?|xfire|xmlrpc\.beeps?|xmpp|xri|ymsgr|z39\.50[rs]?):(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`*!()\[\]{};:'".,<>?«»“”‘’]))/i;e.defineMode("gfm",(function(n,r){var i=0,o={startState:function(){return{code:!1,codeBlock:!1,ateSpace:!1}},copyState:function(e){return{code:e.code,codeBlock:e.codeBlock,ateSpace:e.ateSpace}},token:function(e,n){if(n.combineTokens=null,n.codeBlock)return e.match(/^```+/)?(n.codeBlock=!1,null):(e.skipToEnd(),null);if(e.sol()&&(n.code=!1),e.sol()&&e.match(/^```+/))return e.skipToEnd(),n.codeBlock=!0,null;if("`"===e.peek()){e.next();var o=e.pos;e.eatWhile("`");var a=1+e.pos-o;return n.code?a===i&&(n.code=!1):(i=a,n.code=!0),null}if(n.code)return e.next(),null;if(e.eatSpace())return n.ateSpace=!0,null;if((e.sol()||n.ateSpace)&&(n.ateSpace=!1,!1!==r.gitHubSpice)){if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+@)?(?=.{0,6}\d)(?:[a-f0-9]{7,40}\b)/))return n.combineTokens=!0,"link";if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+)?#[0-9]+\b/))return n.combineTokens=!0,"link"}return e.match(t)&&"]("!=e.string.slice(e.start-2,e.start)&&(0==e.start||/\W/.test(e.string.charAt(e.start-1)))?(n.combineTokens=!0,"link"):(e.next(),null)},blankLine:function(e){return e.code=!1,null}},a={taskLists:!0,strikethrough:!0,emoji:!0};for(var l in r)a[l]=r[l];return a.name="markdown",e.overlayMode(e.getMode(n,a),o)}),"markdown"),e.defineMIME("text/x-gfm","gfm")},"object"==typeof n&&"object"==typeof t?r(e("../../lib/codemirror"),e("../markdown/markdown"),e("../../addon/mode/overlay")):r(CodeMirror)},{"../../addon/mode/overlay":7,"../../lib/codemirror":10,"../markdown/markdown":12}],12:[function(e,t,n){var r;r=function(e){"use strict";e.defineMode("markdown",(function(t,n){var r=e.getMode(t,"text/html"),i="null"==r.name;void 0===n.highlightFormatting&&(n.highlightFormatting=!1),void 0===n.maxBlockquoteDepth&&(n.maxBlockquoteDepth=0),void 0===n.taskLists&&(n.taskLists=!1),void 0===n.strikethrough&&(n.strikethrough=!1),void 0===n.emoji&&(n.emoji=!1),void 0===n.fencedCodeBlockHighlighting&&(n.fencedCodeBlockHighlighting=!0),void 0===n.fencedCodeBlockDefaultMode&&(n.fencedCodeBlockDefaultMode="text/plain"),void 0===n.xml&&(n.xml=!0),void 0===n.tokenTypeOverrides&&(n.tokenTypeOverrides={});var o={header:"header",code:"comment",quote:"quote",list1:"variable-2",list2:"variable-3",list3:"keyword",hr:"hr",image:"image",imageAltText:"image-alt-text",imageMarker:"image-marker",formatting:"formatting",linkInline:"link",linkEmail:"link",linkText:"link",linkHref:"string",em:"em",strong:"strong",strikethrough:"strikethrough",emoji:"builtin"};for(var a in o)o.hasOwnProperty(a)&&n.tokenTypeOverrides[a]&&(o[a]=n.tokenTypeOverrides[a]);var l=/^([*\-_])(?:\s*\1){2,}\s*$/,s=/^(?:[*\-+]|^[0-9]+([.)]))\s+/,u=/^\[(x| )\](?=\s)/i,c=n.allowAtxHeaderWithoutSpace?/^(#+)/:/^(#+)(?: |$)/,d=/^ {0,3}(?:\={1,}|-{2,})\s*$/,h=/^[^#!\[\]*_\\<>` "'(~:]+/,f=/^(~~~+|```+)[ \t]*([\w\/+#-]*)[^\n`]*$/,p=/^\s*\[[^\]]+?\]:.*$/,m=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E42\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC9\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDF3C-\uDF3E]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]/;function g(e,t,n){return t.f=t.inline=n,n(e,t)}function v(e,t,n){return t.f=t.block=n,n(e,t)}function x(t){if(t.linkTitle=!1,t.linkHref=!1,t.linkText=!1,t.em=!1,t.strong=!1,t.strikethrough=!1,t.quote=0,t.indentedCode=!1,t.f==b){var n=i;if(!n){var o=e.innerMode(r,t.htmlState);n="xml"==o.mode.name&&null===o.state.tagStart&&!o.state.context&&o.state.tokenize.isInText}n&&(t.f=k,t.block=y,t.htmlState=null)}return t.trailingSpace=0,t.trailingSpaceNewLine=!1,t.prevLine=t.thisLine,t.thisLine={stream:null},null}function y(r,i){var a,h=r.column()===i.indentation,m=!(a=i.prevLine.stream)||!/\S/.test(a.string),v=i.indentedCode,x=i.prevLine.hr,y=!1!==i.list,b=(i.listStack[i.listStack.length-1]||0)+3;i.indentedCode=!1;var w=i.indentation;if(null===i.indentationDiff&&(i.indentationDiff=i.indentation,y)){for(i.list=null;w<i.listStack[i.listStack.length-1];)i.listStack.pop(),i.listStack.length?i.indentation=i.listStack[i.listStack.length-1]:i.list=!1;!1!==i.list&&(i.indentationDiff=w-i.listStack[i.listStack.length-1])}var k=!(m||x||i.prevLine.header||y&&v||i.prevLine.fencedCodeEnd),S=(!1===i.list||x||m)&&i.indentation<=b&&r.match(l),F=null;if(i.indentationDiff>=4&&(v||i.prevLine.fencedCodeEnd||i.prevLine.header||m))return r.skipToEnd(),i.indentedCode=!0,o.code;if(r.eatSpace())return null;if(h&&i.indentation<=b&&(F=r.match(c))&&F[1].length<=6)return i.quote=0,i.header=F[1].length,i.thisLine.header=!0,n.highlightFormatting&&(i.formatting="header"),i.f=i.inline,C(i);if(i.indentation<=b&&r.eat(">"))return i.quote=h?1:i.quote+1,n.highlightFormatting&&(i.formatting="quote"),r.eatSpace(),C(i);if(!S&&!i.setext&&h&&i.indentation<=b&&(F=r.match(s))){var A=F[1]?"ol":"ul";return i.indentation=w+r.current().length,i.list=!0,i.quote=0,i.listStack.push(i.indentation),i.em=!1,i.strong=!1,i.code=!1,i.strikethrough=!1,n.taskLists&&r.match(u,!1)&&(i.taskList=!0),i.f=i.inline,n.highlightFormatting&&(i.formatting=["list","list-"+A]),C(i)}return h&&i.indentation<=b&&(F=r.match(f,!0))?(i.quote=0,i.fencedEndRE=new RegExp(F[1]+"+ *$"),i.localMode=n.fencedCodeBlockHighlighting&&function(n){if(e.findModeByName){var r=e.findModeByName(n);r&&(n=r.mime||r.mimes[0])}var i=e.getMode(t,n);return"null"==i.name?null:i}(F[2]||n.fencedCodeBlockDefaultMode),i.localMode&&(i.localState=e.startState(i.localMode)),i.f=i.block=D,n.highlightFormatting&&(i.formatting="code-block"),i.code=-1,C(i)):i.setext||!(k&&y||i.quote||!1!==i.list||i.code||S||p.test(r.string))&&(F=r.lookAhead(1))&&(F=F.match(d))?(i.setext?(i.header=i.setext,i.setext=0,r.skipToEnd(),n.highlightFormatting&&(i.formatting="header")):(i.header="="==F[0].charAt(0)?1:2,i.setext=i.header),i.thisLine.header=!0,i.f=i.inline,C(i)):S?(r.skipToEnd(),i.hr=!0,i.thisLine.hr=!0,o.hr):"["===r.peek()?g(r,i,E):g(r,i,i.inline)}function b(t,n){var o=r.token(t,n.htmlState);if(!i){var a=e.innerMode(r,n.htmlState);("xml"==a.mode.name&&null===a.state.tagStart&&!a.state.context&&a.state.tokenize.isInText||n.md_inside&&t.current().indexOf(">")>-1)&&(n.f=k,n.block=y,n.htmlState=null)}return o}function D(e,t){var r,i=t.listStack[t.listStack.length-1]||0,a=t.indentation<i,l=i+3;return t.fencedEndRE&&t.indentation<=l&&(a||e.match(t.fencedEndRE))?(n.highlightFormatting&&(t.formatting="code-block"),a||(r=C(t)),t.localMode=t.localState=null,t.block=y,t.f=k,t.fencedEndRE=null,t.code=0,t.thisLine.fencedCodeEnd=!0,a?v(e,t,t.block):r):t.localMode?t.localMode.token(e,t.localState):(e.skipToEnd(),o.code)}function C(e){var t=[];if(e.formatting){t.push(o.formatting),"string"==typeof e.formatting&&(e.formatting=[e.formatting]);for(var r=0;r<e.formatting.length;r++)t.push(o.formatting+"-"+e.formatting[r]),"header"===e.formatting[r]&&t.push(o.formatting+"-"+e.formatting[r]+"-"+e.header),"quote"===e.formatting[r]&&(!n.maxBlockquoteDepth||n.maxBlockquoteDepth>=e.quote?t.push(o.formatting+"-"+e.formatting[r]+"-"+e.quote):t.push("error"))}if(e.taskOpen)return t.push("meta"),t.length?t.join(" "):null;if(e.taskClosed)return t.push("property"),t.length?t.join(" "):null;if(e.linkHref?t.push(o.linkHref,"url"):(e.strong&&t.push(o.strong),e.em&&t.push(o.em),e.strikethrough&&t.push(o.strikethrough),e.emoji&&t.push(o.emoji),e.linkText&&t.push(o.linkText),e.code&&t.push(o.code),e.image&&t.push(o.image),e.imageAltText&&t.push(o.imageAltText,"link"),e.imageMarker&&t.push(o.imageMarker)),e.header&&t.push(o.header,o.header+"-"+e.header),e.quote&&(t.push(o.quote),!n.maxBlockquoteDepth||n.maxBlockquoteDepth>=e.quote?t.push(o.quote+"-"+e.quote):t.push(o.quote+"-"+n.maxBlockquoteDepth)),!1!==e.list){var i=(e.listStack.length-1)%3;i?1===i?t.push(o.list2):t.push(o.list3):t.push(o.list1)}return e.trailingSpaceNewLine?t.push("trailing-space-new-line"):e.trailingSpace&&t.push("trailing-space-"+(e.trailingSpace%2?"a":"b")),t.length?t.join(" "):null}function w(e,t){if(e.match(h,!0))return C(t)}function k(t,i){var a=i.text(t,i);if(void 0!==a)return a;if(i.list)return i.list=null,C(i);if(i.taskList)return" "===t.match(u,!0)[1]?i.taskOpen=!0:i.taskClosed=!0,n.highlightFormatting&&(i.formatting="task"),i.taskList=!1,C(i);if(i.taskOpen=!1,i.taskClosed=!1,i.header&&t.match(/^#+$/,!0))return n.highlightFormatting&&(i.formatting="header"),C(i);var l=t.next();if(i.linkTitle){i.linkTitle=!1;var s=l;"("===l&&(s=")");var c="^\\s*(?:[^"+(s=(s+"").replace(/([.?*+^\[\]\\(){}|-])/g,"\\$1"))+"\\\\]+|\\\\\\\\|\\\\.)"+s;if(t.match(new RegExp(c),!0))return o.linkHref}if("`"===l){var d=i.formatting;n.highlightFormatting&&(i.formatting="code"),t.eatWhile("`");var h=t.current().length;if(0!=i.code||i.quote&&1!=h){if(h==i.code){var f=C(i);return i.code=0,f}return i.formatting=d,C(i)}return i.code=h,C(i)}if(i.code)return C(i);if("\\"===l&&(t.next(),n.highlightFormatting)){var p=C(i),g=o.formatting+"-escape";return p?p+" "+g:g}if("!"===l&&t.match(/\[[^\]]*\] ?(?:\(|\[)/,!1))return i.imageMarker=!0,i.image=!0,n.highlightFormatting&&(i.formatting="image"),C(i);if("["===l&&i.imageMarker&&t.match(/[^\]]*\](\(.*?\)| ?\[.*?\])/,!1))return i.imageMarker=!1,i.imageAltText=!0,n.highlightFormatting&&(i.formatting="image"),C(i);if("]"===l&&i.imageAltText){n.highlightFormatting&&(i.formatting="image");var p=C(i);return i.imageAltText=!1,i.image=!1,i.inline=i.f=F,p}if("["===l&&!i.image)return i.linkText&&t.match(/^.*?\]/)||(i.linkText=!0,n.highlightFormatting&&(i.formatting="link")),C(i);if("]"===l&&i.linkText){n.highlightFormatting&&(i.formatting="link");var p=C(i);return i.linkText=!1,i.inline=i.f=t.match(/\(.*?\)| ?\[.*?\]/,!1)?F:k,p}if("<"===l&&t.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/,!1))return i.f=i.inline=S,n.highlightFormatting&&(i.formatting="link"),(p=C(i))?p+=" ":p="",p+o.linkInline;if("<"===l&&t.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/,!1))return i.f=i.inline=S,n.highlightFormatting&&(i.formatting="link"),(p=C(i))?p+=" ":p="",p+o.linkEmail;if(n.xml&&"<"===l&&t.match(/^(!--|\?|!\[CDATA\[|[a-z][a-z0-9-]*(?:\s+[a-z_:.\-]+(?:\s*=\s*[^>]+)?)*\s*(?:>|$))/i,!1)){var x=t.string.indexOf(">",t.pos);if(-1!=x){var y=t.string.substring(t.start,x);/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(y)&&(i.md_inside=!0)}return t.backUp(1),i.htmlState=e.startState(r),v(t,i,b)}if(n.xml&&"<"===l&&t.match(/^\/\w*?>/))return i.md_inside=!1,"tag";if("*"===l||"_"===l){for(var D=1,w=1==t.pos?" ":t.string.charAt(t.pos-2);D<3&&t.eat(l);)D++;var A=t.peek()||" ",E=!/\s/.test(A)&&(!m.test(A)||/\s/.test(w)||m.test(w)),T=!/\s/.test(w)&&(!m.test(w)||/\s/.test(A)||m.test(A)),L=null,M=null;if(D%2&&(i.em||!E||"*"!==l&&T&&!m.test(w)?i.em!=l||!T||"*"!==l&&E&&!m.test(A)||(L=!1):L=!0),D>1&&(i.strong||!E||"*"!==l&&T&&!m.test(w)?i.strong!=l||!T||"*"!==l&&E&&!m.test(A)||(M=!1):M=!0),null!=M||null!=L)return n.highlightFormatting&&(i.formatting=null==L?"strong":null==M?"em":"strong em"),!0===L&&(i.em=l),!0===M&&(i.strong=l),f=C(i),!1===L&&(i.em=!1),!1===M&&(i.strong=!1),f}else if(" "===l&&(t.eat("*")||t.eat("_"))){if(" "===t.peek())return C(i);t.backUp(1)}if(n.strikethrough)if("~"===l&&t.eatWhile(l)){if(i.strikethrough)return n.highlightFormatting&&(i.formatting="strikethrough"),f=C(i),i.strikethrough=!1,f;if(t.match(/^[^\s]/,!1))return i.strikethrough=!0,n.highlightFormatting&&(i.formatting="strikethrough"),C(i)}else if(" "===l&&t.match("~~",!0)){if(" "===t.peek())return C(i);t.backUp(2)}if(n.emoji&&":"===l&&t.match(/^(?:[a-z_\d+][a-z_\d+-]*|\-[a-z_\d+][a-z_\d+-]*):/)){i.emoji=!0,n.highlightFormatting&&(i.formatting="emoji");var B=C(i);return i.emoji=!1,B}return" "===l&&(t.match(/^ +$/,!1)?i.trailingSpace++:i.trailingSpace&&(i.trailingSpaceNewLine=!0)),C(i)}function S(e,t){if(">"===e.next()){t.f=t.inline=k,n.highlightFormatting&&(t.formatting="link");var r=C(t);return r?r+=" ":r="",r+o.linkInline}return e.match(/^[^>]+/,!0),o.linkInline}function F(e,t){if(e.eatSpace())return null;var r,i=e.next();return"("===i||"["===i?(t.f=t.inline=(r="("===i?")":"]",function(e,t){if(e.next()===r){t.f=t.inline=k,n.highlightFormatting&&(t.formatting="link-string");var i=C(t);return t.linkHref=!1,i}return e.match(A[r]),t.linkHref=!0,C(t)}),n.highlightFormatting&&(t.formatting="link-string"),t.linkHref=!0,C(t)):"error"}var A={")":/^(?:[^\\\(\)]|\\.|\((?:[^\\\(\)]|\\.)*\))*?(?=\))/,"]":/^(?:[^\\\[\]]|\\.|\[(?:[^\\\[\]]|\\.)*\])*?(?=\])/};function E(e,t){return e.match(/^([^\]\\]|\\.)*\]:/,!1)?(t.f=T,e.next(),n.highlightFormatting&&(t.formatting="link"),t.linkText=!0,C(t)):g(e,t,k)}function T(e,t){if(e.match("]:",!0)){t.f=t.inline=L,n.highlightFormatting&&(t.formatting="link");var r=C(t);return t.linkText=!1,r}return e.match(/^([^\]\\]|\\.)+/,!0),o.linkText}function L(e,t){return e.eatSpace()?null:(e.match(/^[^\s]+/,!0),void 0===e.peek()?t.linkTitle=!0:e.match(/^(?:\s+(?:"(?:[^"\\]|\\.)+"|'(?:[^'\\]|\\.)+'|\((?:[^)\\]|\\.)+\)))?/,!0),t.f=t.inline=k,o.linkHref+" url")}var M={startState:function(){return{f:y,prevLine:{stream:null},thisLine:{stream:null},block:y,htmlState:null,indentation:0,inline:k,text:w,formatting:!1,linkText:!1,linkHref:!1,linkTitle:!1,code:0,em:!1,strong:!1,header:0,setext:0,hr:!1,taskList:!1,list:!1,listStack:[],quote:0,trailingSpace:0,trailingSpaceNewLine:!1,strikethrough:!1,emoji:!1,fencedEndRE:null}},copyState:function(t){return{f:t.f,prevLine:t.prevLine,thisLine:t.thisLine,block:t.block,htmlState:t.htmlState&&e.copyState(r,t.htmlState),indentation:t.indentation,localMode:t.localMode,localState:t.localMode?e.copyState(t.localMode,t.localState):null,inline:t.inline,text:t.text,formatting:!1,linkText:t.linkText,linkTitle:t.linkTitle,linkHref:t.linkHref,code:t.code,em:t.em,strong:t.strong,strikethrough:t.strikethrough,emoji:t.emoji,header:t.header,setext:t.setext,hr:t.hr,taskList:t.taskList,list:t.list,listStack:t.listStack.slice(0),quote:t.quote,indentedCode:t.indentedCode,trailingSpace:t.trailingSpace,trailingSpaceNewLine:t.trailingSpaceNewLine,md_inside:t.md_inside,fencedEndRE:t.fencedEndRE}},token:function(e,t){if(t.formatting=!1,e!=t.thisLine.stream){if(t.header=0,t.hr=!1,e.match(/^\s*$/,!0))return x(t),null;if(t.prevLine=t.thisLine,t.thisLine={stream:e},t.taskList=!1,t.trailingSpace=0,t.trailingSpaceNewLine=!1,!t.localState&&(t.f=t.block,t.f!=b)){var n=e.match(/^\s*/,!0)[0].replace(/\t/g,"    ").length;if(t.indentation=n,t.indentationDiff=null,n>0)return null}}return t.f(e,t)},innerMode:function(e){return e.block==b?{state:e.htmlState,mode:r}:e.localState?{state:e.localState,mode:e.localMode}:{state:e,mode:M}},indent:function(t,n,i){return t.block==b&&r.indent?r.indent(t.htmlState,n,i):t.localState&&t.localMode.indent?t.localMode.indent(t.localState,n,i):e.Pass},blankLine:x,getType:C,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",closeBrackets:"()[]{}''\"\"``",fold:"markdown"};return M}),"xml"),e.defineMIME("text/markdown","markdown"),e.defineMIME("text/x-markdown","markdown")},"object"==typeof n&&"object"==typeof t?r(e("../../lib/codemirror"),e("../xml/xml"),e("../meta")):r(CodeMirror)},{"../../lib/codemirror":10,"../meta":13,"../xml/xml":14}],13:[function(e,t,n){(function(e){"use strict";e.modeInfo=[{name:"APL",mime:"text/apl",mode:"apl",ext:["dyalog","apl"]},{name:"PGP",mimes:["application/pgp","application/pgp-encrypted","application/pgp-keys","application/pgp-signature"],mode:"asciiarmor",ext:["asc","pgp","sig"]},{name:"ASN.1",mime:"text/x-ttcn-asn",mode:"asn.1",ext:["asn","asn1"]},{name:"Asterisk",mime:"text/x-asterisk",mode:"asterisk",file:/^extensions\.conf$/i},{name:"Brainfuck",mime:"text/x-brainfuck",mode:"brainfuck",ext:["b","bf"]},{name:"C",mime:"text/x-csrc",mode:"clike",ext:["c","h","ino"]},{name:"C++",mime:"text/x-c++src",mode:"clike",ext:["cpp","c++","cc","cxx","hpp","h++","hh","hxx"],alias:["cpp"]},{name:"Cobol",mime:"text/x-cobol",mode:"cobol",ext:["cob","cpy","cbl"]},{name:"C#",mime:"text/x-csharp",mode:"clike",ext:["cs"],alias:["csharp","cs"]},{name:"Clojure",mime:"text/x-clojure",mode:"clojure",ext:["clj","cljc","cljx"]},{name:"ClojureScript",mime:"text/x-clojurescript",mode:"clojure",ext:["cljs"]},{name:"Closure Stylesheets (GSS)",mime:"text/x-gss",mode:"css",ext:["gss"]},{name:"CMake",mime:"text/x-cmake",mode:"cmake",ext:["cmake","cmake.in"],file:/^CMakeLists\.txt$/},{name:"CoffeeScript",mimes:["application/vnd.coffeescript","text/coffeescript","text/x-coffeescript"],mode:"coffeescript",ext:["coffee"],alias:["coffee","coffee-script"]},{name:"Common Lisp",mime:"text/x-common-lisp",mode:"commonlisp",ext:["cl","lisp","el"],alias:["lisp"]},{name:"Cypher",mime:"application/x-cypher-query",mode:"cypher",ext:["cyp","cypher"]},{name:"Cython",mime:"text/x-cython",mode:"python",ext:["pyx","pxd","pxi"]},{name:"Crystal",mime:"text/x-crystal",mode:"crystal",ext:["cr"]},{name:"CSS",mime:"text/css",mode:"css",ext:["css"]},{name:"CQL",mime:"text/x-cassandra",mode:"sql",ext:["cql"]},{name:"D",mime:"text/x-d",mode:"d",ext:["d"]},{name:"Dart",mimes:["application/dart","text/x-dart"],mode:"dart",ext:["dart"]},{name:"diff",mime:"text/x-diff",mode:"diff",ext:["diff","patch"]},{name:"Django",mime:"text/x-django",mode:"django"},{name:"Dockerfile",mime:"text/x-dockerfile",mode:"dockerfile",file:/^Dockerfile$/},{name:"DTD",mime:"application/xml-dtd",mode:"dtd",ext:["dtd"]},{name:"Dylan",mime:"text/x-dylan",mode:"dylan",ext:["dylan","dyl","intr"]},{name:"EBNF",mime:"text/x-ebnf",mode:"ebnf"},{name:"ECL",mime:"text/x-ecl",mode:"ecl",ext:["ecl"]},{name:"edn",mime:"application/edn",mode:"clojure",ext:["edn"]},{name:"Eiffel",mime:"text/x-eiffel",mode:"eiffel",ext:["e"]},{name:"Elm",mime:"text/x-elm",mode:"elm",ext:["elm"]},{name:"Embedded JavaScript",mime:"application/x-ejs",mode:"htmlembedded",ext:["ejs"]},{name:"Embedded Ruby",mime:"application/x-erb",mode:"htmlembedded",ext:["erb"]},{name:"Erlang",mime:"text/x-erlang",mode:"erlang",ext:["erl"]},{name:"Esper",mime:"text/x-esper",mode:"sql"},{name:"Factor",mime:"text/x-factor",mode:"factor",ext:["factor"]},{name:"FCL",mime:"text/x-fcl",mode:"fcl"},{name:"Forth",mime:"text/x-forth",mode:"forth",ext:["forth","fth","4th"]},{name:"Fortran",mime:"text/x-fortran",mode:"fortran",ext:["f","for","f77","f90","f95"]},{name:"F#",mime:"text/x-fsharp",mode:"mllike",ext:["fs"],alias:["fsharp"]},{name:"Gas",mime:"text/x-gas",mode:"gas",ext:["s"]},{name:"Gherkin",mime:"text/x-feature",mode:"gherkin",ext:["feature"]},{name:"GitHub Flavored Markdown",mime:"text/x-gfm",mode:"gfm",file:/^(readme|contributing|history)\.md$/i},{name:"Go",mime:"text/x-go",mode:"go",ext:["go"]},{name:"Groovy",mime:"text/x-groovy",mode:"groovy",ext:["groovy","gradle"],file:/^Jenkinsfile$/},{name:"HAML",mime:"text/x-haml",mode:"haml",ext:["haml"]},{name:"Haskell",mime:"text/x-haskell",mode:"haskell",ext:["hs"]},{name:"Haskell (Literate)",mime:"text/x-literate-haskell",mode:"haskell-literate",ext:["lhs"]},{name:"Haxe",mime:"text/x-haxe",mode:"haxe",ext:["hx"]},{name:"HXML",mime:"text/x-hxml",mode:"haxe",ext:["hxml"]},{name:"ASP.NET",mime:"application/x-aspx",mode:"htmlembedded",ext:["aspx"],alias:["asp","aspx"]},{name:"HTML",mime:"text/html",mode:"htmlmixed",ext:["html","htm","handlebars","hbs"],alias:["xhtml"]},{name:"HTTP",mime:"message/http",mode:"http"},{name:"IDL",mime:"text/x-idl",mode:"idl",ext:["pro"]},{name:"Pug",mime:"text/x-pug",mode:"pug",ext:["jade","pug"],alias:["jade"]},{name:"Java",mime:"text/x-java",mode:"clike",ext:["java"]},{name:"Java Server Pages",mime:"application/x-jsp",mode:"htmlembedded",ext:["jsp"],alias:["jsp"]},{name:"JavaScript",mimes:["text/javascript","text/ecmascript","application/javascript","application/x-javascript","application/ecmascript"],mode:"javascript",ext:["js"],alias:["ecmascript","js","node"]},{name:"JSON",mimes:["application/json","application/x-json"],mode:"javascript",ext:["json","map"],alias:["json5"]},{name:"JSON-LD",mime:"application/ld+json",mode:"javascript",ext:["jsonld"],alias:["jsonld"]},{name:"JSX",mime:"text/jsx",mode:"jsx",ext:["jsx"]},{name:"Jinja2",mime:"text/jinja2",mode:"jinja2",ext:["j2","jinja","jinja2"]},{name:"Julia",mime:"text/x-julia",mode:"julia",ext:["jl"],alias:["jl"]},{name:"Kotlin",mime:"text/x-kotlin",mode:"clike",ext:["kt"]},{name:"LESS",mime:"text/x-less",mode:"css",ext:["less"]},{name:"LiveScript",mime:"text/x-livescript",mode:"livescript",ext:["ls"],alias:["ls"]},{name:"Lua",mime:"text/x-lua",mode:"lua",ext:["lua"]},{name:"Markdown",mime:"text/x-markdown",mode:"markdown",ext:["markdown","md","mkd"]},{name:"mIRC",mime:"text/mirc",mode:"mirc"},{name:"MariaDB SQL",mime:"text/x-mariadb",mode:"sql"},{name:"Mathematica",mime:"text/x-mathematica",mode:"mathematica",ext:["m","nb","wl","wls"]},{name:"Modelica",mime:"text/x-modelica",mode:"modelica",ext:["mo"]},{name:"MUMPS",mime:"text/x-mumps",mode:"mumps",ext:["mps"]},{name:"MS SQL",mime:"text/x-mssql",mode:"sql"},{name:"mbox",mime:"application/mbox",mode:"mbox",ext:["mbox"]},{name:"MySQL",mime:"text/x-mysql",mode:"sql"},{name:"Nginx",mime:"text/x-nginx-conf",mode:"nginx",file:/nginx.*\.conf$/i},{name:"NSIS",mime:"text/x-nsis",mode:"nsis",ext:["nsh","nsi"]},{name:"NTriples",mimes:["application/n-triples","application/n-quads","text/n-triples"],mode:"ntriples",ext:["nt","nq"]},{name:"Objective-C",mime:"text/x-objectivec",mode:"clike",ext:["m"],alias:["objective-c","objc"]},{name:"Objective-C++",mime:"text/x-objectivec++",mode:"clike",ext:["mm"],alias:["objective-c++","objc++"]},{name:"OCaml",mime:"text/x-ocaml",mode:"mllike",ext:["ml","mli","mll","mly"]},{name:"Octave",mime:"text/x-octave",mode:"octave",ext:["m"]},{name:"Oz",mime:"text/x-oz",mode:"oz",ext:["oz"]},{name:"Pascal",mime:"text/x-pascal",mode:"pascal",ext:["p","pas"]},{name:"PEG.js",mime:"null",mode:"pegjs",ext:["jsonld"]},{name:"Perl",mime:"text/x-perl",mode:"perl",ext:["pl","pm"]},{name:"PHP",mimes:["text/x-php","application/x-httpd-php","application/x-httpd-php-open"],mode:"php",ext:["php","php3","php4","php5","php7","phtml"]},{name:"Pig",mime:"text/x-pig",mode:"pig",ext:["pig"]},{name:"Plain Text",mime:"text/plain",mode:"null",ext:["txt","text","conf","def","list","log"]},{name:"PLSQL",mime:"text/x-plsql",mode:"sql",ext:["pls"]},{name:"PostgreSQL",mime:"text/x-pgsql",mode:"sql"},{name:"PowerShell",mime:"application/x-powershell",mode:"powershell",ext:["ps1","psd1","psm1"]},{name:"Properties files",mime:"text/x-properties",mode:"properties",ext:["properties","ini","in"],alias:["ini","properties"]},{name:"ProtoBuf",mime:"text/x-protobuf",mode:"protobuf",ext:["proto"]},{name:"Python",mime:"text/x-python",mode:"python",ext:["BUILD","bzl","py","pyw"],file:/^(BUCK|BUILD)$/},{name:"Puppet",mime:"text/x-puppet",mode:"puppet",ext:["pp"]},{name:"Q",mime:"text/x-q",mode:"q",ext:["q"]},{name:"R",mime:"text/x-rsrc",mode:"r",ext:["r","R"],alias:["rscript"]},{name:"reStructuredText",mime:"text/x-rst",mode:"rst",ext:["rst"],alias:["rst"]},{name:"RPM Changes",mime:"text/x-rpm-changes",mode:"rpm"},{name:"RPM Spec",mime:"text/x-rpm-spec",mode:"rpm",ext:["spec"]},{name:"Ruby",mime:"text/x-ruby",mode:"ruby",ext:["rb"],alias:["jruby","macruby","rake","rb","rbx"]},{name:"Rust",mime:"text/x-rustsrc",mode:"rust",ext:["rs"]},{name:"SAS",mime:"text/x-sas",mode:"sas",ext:["sas"]},{name:"Sass",mime:"text/x-sass",mode:"sass",ext:["sass"]},{name:"Scala",mime:"text/x-scala",mode:"clike",ext:["scala"]},{name:"Scheme",mime:"text/x-scheme",mode:"scheme",ext:["scm","ss"]},{name:"SCSS",mime:"text/x-scss",mode:"css",ext:["scss"]},{name:"Shell",mimes:["text/x-sh","application/x-sh"],mode:"shell",ext:["sh","ksh","bash"],alias:["bash","sh","zsh"],file:/^PKGBUILD$/},{name:"Sieve",mime:"application/sieve",mode:"sieve",ext:["siv","sieve"]},{name:"Slim",mimes:["text/x-slim","application/x-slim"],mode:"slim",ext:["slim"]},{name:"Smalltalk",mime:"text/x-stsrc",mode:"smalltalk",ext:["st"]},{name:"Smarty",mime:"text/x-smarty",mode:"smarty",ext:["tpl"]},{name:"Solr",mime:"text/x-solr",mode:"solr"},{name:"SML",mime:"text/x-sml",mode:"mllike",ext:["sml","sig","fun","smackspec"]},{name:"Soy",mime:"text/x-soy",mode:"soy",ext:["soy"],alias:["closure template"]},{name:"SPARQL",mime:"application/sparql-query",mode:"sparql",ext:["rq","sparql"],alias:["sparul"]},{name:"Spreadsheet",mime:"text/x-spreadsheet",mode:"spreadsheet",alias:["excel","formula"]},{name:"SQL",mime:"text/x-sql",mode:"sql",ext:["sql"]},{name:"SQLite",mime:"text/x-sqlite",mode:"sql"},{name:"Squirrel",mime:"text/x-squirrel",mode:"clike",ext:["nut"]},{name:"Stylus",mime:"text/x-styl",mode:"stylus",ext:["styl"]},{name:"Swift",mime:"text/x-swift",mode:"swift",ext:["swift"]},{name:"sTeX",mime:"text/x-stex",mode:"stex"},{name:"LaTeX",mime:"text/x-latex",mode:"stex",ext:["text","ltx","tex"],alias:["tex"]},{name:"SystemVerilog",mime:"text/x-systemverilog",mode:"verilog",ext:["v","sv","svh"]},{name:"Tcl",mime:"text/x-tcl",mode:"tcl",ext:["tcl"]},{name:"Textile",mime:"text/x-textile",mode:"textile",ext:["textile"]},{name:"TiddlyWiki",mime:"text/x-tiddlywiki",mode:"tiddlywiki"},{name:"Tiki wiki",mime:"text/tiki",mode:"tiki"},{name:"TOML",mime:"text/x-toml",mode:"toml",ext:["toml"]},{name:"Tornado",mime:"text/x-tornado",mode:"tornado"},{name:"troff",mime:"text/troff",mode:"troff",ext:["1","2","3","4","5","6","7","8","9"]},{name:"TTCN",mime:"text/x-ttcn",mode:"ttcn",ext:["ttcn","ttcn3","ttcnpp"]},{name:"TTCN_CFG",mime:"text/x-ttcn-cfg",mode:"ttcn-cfg",ext:["cfg"]},{name:"Turtle",mime:"text/turtle",mode:"turtle",ext:["ttl"]},{name:"TypeScript",mime:"application/typescript",mode:"javascript",ext:["ts"],alias:["ts"]},{name:"TypeScript-JSX",mime:"text/typescript-jsx",mode:"jsx",ext:["tsx"],alias:["tsx"]},{name:"Twig",mime:"text/x-twig",mode:"twig"},{name:"Web IDL",mime:"text/x-webidl",mode:"webidl",ext:["webidl"]},{name:"VB.NET",mime:"text/x-vb",mode:"vb",ext:["vb"]},{name:"VBScript",mime:"text/vbscript",mode:"vbscript",ext:["vbs"]},{name:"Velocity",mime:"text/velocity",mode:"velocity",ext:["vtl"]},{name:"Verilog",mime:"text/x-verilog",mode:"verilog",ext:["v"]},{name:"VHDL",mime:"text/x-vhdl",mode:"vhdl",ext:["vhd","vhdl"]},{name:"Vue.js Component",mimes:["script/x-vue","text/x-vue"],mode:"vue",ext:["vue"]},{name:"XML",mimes:["application/xml","text/xml"],mode:"xml",ext:["xml","xsl","xsd","svg"],alias:["rss","wsdl","xsd"]},{name:"XQuery",mime:"application/xquery",mode:"xquery",ext:["xy","xquery"]},{name:"Yacas",mime:"text/x-yacas",mode:"yacas",ext:["ys"]},{name:"YAML",mimes:["text/x-yaml","text/yaml"],mode:"yaml",ext:["yaml","yml"],alias:["yml"]},{name:"Z80",mime:"text/x-z80",mode:"z80",ext:["z80"]},{name:"mscgen",mime:"text/x-mscgen",mode:"mscgen",ext:["mscgen","mscin","msc"]},{name:"xu",mime:"text/x-xu",mode:"mscgen",ext:["xu"]},{name:"msgenny",mime:"text/x-msgenny",mode:"mscgen",ext:["msgenny"]},{name:"WebAssembly",mime:"text/webassembly",mode:"wast",ext:["wat","wast"]}];for(var t=0;t<e.modeInfo.length;t++){var n=e.modeInfo[t];n.mimes&&(n.mime=n.mimes[0])}e.findModeByMIME=function(t){t=t.toLowerCase();for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.mime==t)return r;if(r.mimes)for(var i=0;i<r.mimes.length;i++)if(r.mimes[i]==t)return r}return/\+xml$/.test(t)?e.findModeByMIME("application/xml"):/\+json$/.test(t)?e.findModeByMIME("application/json"):void 0},e.findModeByExtension=function(t){t=t.toLowerCase();for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.ext)for(var i=0;i<r.ext.length;i++)if(r.ext[i]==t)return r}},e.findModeByFileName=function(t){for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.file&&r.file.test(t))return r}var i=t.lastIndexOf("."),o=i>-1&&t.substring(i+1,t.length);if(o)return e.findModeByExtension(o)},e.findModeByName=function(t){t=t.toLowerCase();for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.name.toLowerCase()==t)return r;if(r.alias)for(var i=0;i<r.alias.length;i++)if(r.alias[i].toLowerCase()==t)return r}}})("object"==typeof n&&"object"==typeof t?e("../lib/codemirror"):CodeMirror)},{"../lib/codemirror":10}],14:[function(e,t,n){(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},n={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",(function(r,i){var o,a,l=r.indentUnit,s={},u=i.htmlMode?t:n;for(var c in u)s[c]=u[c];for(var c in i)s[c]=i[c];function d(e,t){function n(n){return t.tokenize=n,n(e,t)}var r=e.next();return"<"==r?e.eat("!")?e.eat("[")?e.match("CDATA[")?n(f("atom","]]>")):null:e.match("--")?n(f("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),n(p(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=f("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=h,"tag bracket"):"&"==r?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function h(e,t){var n,r,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=d,o=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return o="equals",null;if("<"==i){t.tokenize=d,t.state=y,t.tagName=t.tagStart=null;var a=t.tokenize(e,t);return a?a+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(n=i,r=function(e,t){for(;!e.eol();)if(e.next()==n){t.tokenize=h;break}return"string"},r.isInAttribute=!0,r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function f(e,t){return function(n,r){for(;!n.eol();){if(n.match(t)){r.tokenize=d;break}n.next()}return e}}function p(e){return function(t,n){for(var r;null!=(r=t.next());){if("<"==r)return n.tokenize=p(e+1),n.tokenize(t,n);if(">"==r){if(1==e){n.tokenize=d;break}return n.tokenize=p(e-1),n.tokenize(t,n)}}return"meta"}}function m(e){return e&&e.toLowerCase()}function g(e,t,n){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=n,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function v(e){e.context&&(e.context=e.context.prev)}function x(e,t){for(var n;;){if(!e.context)return;if(n=e.context.tagName,!s.contextGrabbers.hasOwnProperty(m(n))||!s.contextGrabbers[m(n)].hasOwnProperty(m(t)))return;v(e)}}function y(e,t,n){return"openTag"==e?(n.tagStart=t.column(),b):"closeTag"==e?D:y}function b(e,t,n){return"word"==e?(n.tagName=t.current(),a="tag",k):s.allowMissingTagName&&"endTag"==e?(a="tag bracket",k(e,0,n)):(a="error",b)}function D(e,t,n){if("word"==e){var r=t.current();return n.context&&n.context.tagName!=r&&s.implicitlyClosed.hasOwnProperty(m(n.context.tagName))&&v(n),n.context&&n.context.tagName==r||!1===s.matchClosing?(a="tag",C):(a="tag error",w)}return s.allowMissingTagName&&"endTag"==e?(a="tag bracket",C(e,0,n)):(a="error",w)}function C(e,t,n){return"endTag"!=e?(a="error",C):(v(n),y)}function w(e,t,n){return a="error",C(e,0,n)}function k(e,t,n){if("word"==e)return a="attribute",S;if("endTag"==e||"selfcloseTag"==e){var r=n.tagName,i=n.tagStart;return n.tagName=n.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(m(r))?x(n,r):(x(n,r),n.context=new g(n,r,i==n.indented)),y}return a="error",k}function S(e,t,n){return"equals"==e?F:(s.allowMissing||(a="error"),k(e,0,n))}function F(e,t,n){return"string"==e?A:"word"==e&&s.allowUnquoted?(a="string",k):(a="error",k(e,0,n))}function A(e,t,n){return"string"==e?A:k(e,0,n)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:y,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var n=t.tokenize(e,t);return(n||o)&&"comment"!=n&&(a=null,t.state=t.state(o||n,e,t),a&&(n="error"==a?n+" error":a)),n},indent:function(t,n,r){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=h&&t.tokenize!=d)return r?r.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==s.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(n))return 0;var o=n&&/^<(\/)?([\w_:\.-]*)/.exec(n);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!s.implicitlyClosed.hasOwnProperty(m(i.tagName)))break;i=i.prev}else if(o)for(;i;){var a=s.contextGrabbers[m(i.tagName)];if(!a||!a.hasOwnProperty(m(o[2])))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==F&&(e.state=k)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],n=e.context;n;n=n.prev)t.push(n.tagName);return t.reverse()}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})})("object"==typeof n&&"object"==typeof t?e("../../lib/codemirror"):CodeMirror)},{"../../lib/codemirror":10}],15:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?r(n):r((e="undefined"!=typeof globalThis?globalThis:e||self).marked={})}(this,(function(e){"use strict";function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function r(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0;return function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(){return{baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}e.defaults={baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};var o=/[&<>"']/,a=/[&<>"']/g,l=/[<>"']|&(?!#?\w+;)/,s=/[<>"']|&(?!#?\w+;)/g,u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},c=function(e){return u[e]};function d(e,t){if(t){if(o.test(e))return e.replace(a,c)}else if(l.test(e))return e.replace(s,c);return e}var h=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function f(e){return e.replace(h,(function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}var p=/(^|[^\[])\^/g;function m(e,t){e=e.source||e,t=t||"";var n={replace:function(t,r){return r=(r=r.source||r).replace(p,"$1"),e=e.replace(t,r),n},getRegex:function(){return new RegExp(e,t)}};return n}var g=/[^\w:]/g,v=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function x(e,t,n){if(e){var r;try{r=decodeURIComponent(f(n)).replace(g,"").toLowerCase()}catch(e){return null}if(0===r.indexOf("javascript:")||0===r.indexOf("vbscript:")||0===r.indexOf("data:"))return null}t&&!v.test(n)&&(n=function(e,t){y[" "+e]||(b.test(e)?y[" "+e]=e+"/":y[" "+e]=F(e,"/",!0));var n=-1===(e=y[" "+e]).indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(D,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(C,"$1")+t:e+t}(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(e){return null}return n}var y={},b=/^[^:]+:\/*[^/]*$/,D=/^([^:]+:)[\s\S]*$/,C=/^([^:]+:\/*[^/]*)[\s\S]*$/;var w={exec:function(){}};function k(e){for(var t,n,r=1;r<arguments.length;r++)for(n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function S(e,t){var n=e.replace(/\|/g,(function(e,t,n){for(var r=!1,i=t;--i>=0&&"\\"===n[i];)r=!r;return r?"|":" |"})).split(/ \|/),r=0;if(n[0].trim()||n.shift(),n[n.length-1].trim()||n.pop(),n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function F(e,t,n){var r=e.length;if(0===r)return"";for(var i=0;i<r;){var o=e.charAt(r-i-1);if(o!==t||n){if(o===t||!n)break;i++}else i++}return e.substr(0,r-i)}function A(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function E(e,t){if(t<1)return"";for(var n="";t>1;)1&t&&(n+=e),t>>=1,e+=e;return n+e}function T(e,t,n,r){var i=t.href,o=t.title?d(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;var l={type:"link",raw:n,href:i,title:o,text:a,tokens:r.inlineTokens(a,[])};return r.state.inLink=!1,l}return{type:"image",raw:n,href:i,title:o,text:d(a)}}var L=function(){function t(t){this.options=t||e.defaults}var n=t.prototype;return n.space=function(e){var t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}},n.code=function(e){var t=this.rules.block.code.exec(e);if(t){var n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:F(n,"\n")}}},n.fences=function(e){var t=this.rules.block.fences.exec(e);if(t){var n=t[0],r=function(e,t){var n=e.match(/^(\s+)(?:```)/);if(null===n)return t;var r=n[1];return t.split("\n").map((function(e){var t=e.match(/^\s+/);return null===t?e:t[0].length>=r.length?e.slice(r.length):e})).join("\n")}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim():t[2],text:r}}},n.heading=function(e){var t=this.rules.block.heading.exec(e);if(t){var n=t[2].trim();if(/#$/.test(n)){var r=F(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}var i={type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:[]};return this.lexer.inline(i.text,i.tokens),i}},n.hr=function(e){var t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}},n.blockquote=function(e){var t=this.rules.block.blockquote.exec(e);if(t){var n=t[0].replace(/^ *> ?/gm,"");return{type:"blockquote",raw:t[0],tokens:this.lexer.blockTokens(n,[]),text:n}}},n.list=function(e){var t=this.rules.block.list.exec(e);if(t){var n,i,o,a,l,s,u,c,d,h,f,p,m=t[1].trim(),g=m.length>1,v={type:"list",raw:"",ordered:g,start:g?+m.slice(0,-1):"",loose:!1,items:[]};m=g?"\\d{1,9}\\"+m.slice(-1):"\\"+m,this.options.pedantic&&(m=g?m:"[*+-]");for(var x=new RegExp("^( {0,3}"+m+")((?: [^\\n]*)?(?:\\n|$))");e&&(p=!1,t=x.exec(e))&&!this.rules.block.hr.test(e);){if(n=t[0],e=e.substring(n.length),c=t[2].split("\n",1)[0],d=e.split("\n",1)[0],this.options.pedantic?(a=2,f=c.trimLeft()):(a=(a=t[2].search(/[^ ]/))>4?1:a,f=c.slice(a),a+=t[1].length),s=!1,!c&&/^ *$/.test(d)&&(n+=d+"\n",e=e.substring(d.length+1),p=!0),!p)for(var y=new RegExp("^ {0,"+Math.min(3,a-1)+"}(?:[*+-]|\\d{1,9}[.)])");e&&(c=h=e.split("\n",1)[0],this.options.pedantic&&(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!y.test(c));){if(c.search(/[^ ]/)>=a||!c.trim())f+="\n"+c.slice(a);else{if(s)break;f+="\n"+c}s||c.trim()||(s=!0),n+=h+"\n",e=e.substring(h.length+1)}v.loose||(u?v.loose=!0:/\n *\n *$/.test(n)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(f))&&(o="[ ] "!==i[0],f=f.replace(/^\[[ xX]\] +/,"")),v.items.push({type:"list_item",raw:n,task:!!i,checked:o,loose:!1,text:f}),v.raw+=n}v.items[v.items.length-1].raw=n.trimRight(),v.items[v.items.length-1].text=f.trimRight(),v.raw=v.raw.trimRight();var b=v.items.length;for(l=0;l<b;l++){this.lexer.state.top=!1,v.items[l].tokens=this.lexer.blockTokens(v.items[l].text,[]);var D=v.items[l].tokens.filter((function(e){return"space"===e.type})),C=D.every((function(e){for(var t,n=0,i=r(e.raw.split(""));!(t=i()).done;){if("\n"===t.value&&(n+=1),n>1)return!0}return!1}));!v.loose&&D.length&&C&&(v.loose=!0,v.items[l].loose=!0)}return v}},n.html=function(e){var t=this.rules.block.html.exec(e);if(t){var n={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};return this.options.sanitize&&(n.type="paragraph",n.text=this.options.sanitizer?this.options.sanitizer(t[0]):d(t[0]),n.tokens=[],this.lexer.inline(n.text,n.tokens)),n}},n.def=function(e){var t=this.rules.block.def.exec(e);if(t)return t[3]&&(t[3]=t[3].substring(1,t[3].length-1)),{type:"def",tag:t[1].toLowerCase().replace(/\s+/g," "),raw:t[0],href:t[2],title:t[3]}},n.table=function(e){var t=this.rules.block.table.exec(e);if(t){var n={type:"table",header:S(t[1]).map((function(e){return{text:e}})),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(n.header.length===n.align.length){n.raw=t[0];var r,i,o,a,l=n.align.length;for(r=0;r<l;r++)/^ *-+: *$/.test(n.align[r])?n.align[r]="right":/^ *:-+: *$/.test(n.align[r])?n.align[r]="center":/^ *:-+ *$/.test(n.align[r])?n.align[r]="left":n.align[r]=null;for(l=n.rows.length,r=0;r<l;r++)n.rows[r]=S(n.rows[r],n.header.length).map((function(e){return{text:e}}));for(l=n.header.length,i=0;i<l;i++)n.header[i].tokens=[],this.lexer.inlineTokens(n.header[i].text,n.header[i].tokens);for(l=n.rows.length,i=0;i<l;i++)for(a=n.rows[i],o=0;o<a.length;o++)a[o].tokens=[],this.lexer.inlineTokens(a[o].text,a[o].tokens);return n}}},n.lheading=function(e){var t=this.rules.block.lheading.exec(e);if(t){var n={type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:[]};return this.lexer.inline(n.text,n.tokens),n}},n.paragraph=function(e){var t=this.rules.block.paragraph.exec(e);if(t){var n={type:"paragraph",raw:t[0],text:"\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1],tokens:[]};return this.lexer.inline(n.text,n.tokens),n}},n.text=function(e){var t=this.rules.block.text.exec(e);if(t){var n={type:"text",raw:t[0],text:t[0],tokens:[]};return this.lexer.inline(n.text,n.tokens),n}},n.escape=function(e){var t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:d(t[1])}},n.tag=function(e){var t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):d(t[0]):t[0]}},n.link=function(e){var t=this.rules.inline.link.exec(e);if(t){var n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;var r=F(n.slice(0,-1),"\\");if((n.length-r.length)%2==0)return}else{var i=function(e,t){if(-1===e.indexOf(t[1]))return-1;for(var n=e.length,r=0,i=0;i<n;i++)if("\\"===e[i])i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&--r<0)return i;return-1}(t[2],"()");if(i>-1){var o=(0===t[0].indexOf("!")?5:4)+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,o).trim(),t[3]=""}}var a=t[2],l="";if(this.options.pedantic){var s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(a);s&&(a=s[1],l=s[3])}else l=t[3]?t[3].slice(1,-1):"";return a=a.trim(),/^</.test(a)&&(a=this.options.pedantic&&!/>$/.test(n)?a.slice(1):a.slice(1,-1)),T(t,{href:a?a.replace(this.rules.inline._escapes,"$1"):a,title:l?l.replace(this.rules.inline._escapes,"$1"):l},t[0],this.lexer)}},n.reflink=function(e,t){var n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){var r=(n[2]||n[1]).replace(/\s+/g," ");if(!(r=t[r.toLowerCase()])||!r.href){var i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return T(n,r,n[0],this.lexer)}},n.emStrong=function(e,t,n){void 0===n&&(n="");var r=this.rules.inline.emStrong.lDelim.exec(e);if(r&&(!r[3]||!n.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])/))){var i=r[1]||r[2]||"";if(!i||i&&(""===n||this.rules.inline.punctuation.exec(n))){var o,a,l=r[0].length-1,s=l,u=0,c="*"===r[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+l);null!=(r=c.exec(t));)if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6])if(a=o.length,r[3]||r[4])s+=a;else if(!((r[5]||r[6])&&l%3)||(l+a)%3){if(!((s-=a)>0)){if(a=Math.min(a,a+s+u),Math.min(l,a)%2){var d=e.slice(1,l+r.index+a);return{type:"em",raw:e.slice(0,l+r.index+a+1),text:d,tokens:this.lexer.inlineTokens(d,[])}}var h=e.slice(2,l+r.index+a-1);return{type:"strong",raw:e.slice(0,l+r.index+a+1),text:h,tokens:this.lexer.inlineTokens(h,[])}}}else u+=a}}},n.codespan=function(e){var t=this.rules.inline.code.exec(e);if(t){var n=t[2].replace(/\n/g," "),r=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return r&&i&&(n=n.substring(1,n.length-1)),n=d(n,!0),{type:"codespan",raw:t[0],text:n}}},n.br=function(e){var t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}},n.del=function(e){var t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2],[])}},n.autolink=function(e,t){var n,r,i=this.rules.inline.autolink.exec(e);if(i)return r="@"===i[2]?"mailto:"+(n=d(this.options.mangle?t(i[1]):i[1])):n=d(i[1]),{type:"link",raw:i[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}},n.url=function(e,t){var n;if(n=this.rules.inline.url.exec(e)){var r,i;if("@"===n[2])i="mailto:"+(r=d(this.options.mangle?t(n[0]):n[0]));else{var o;do{o=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0]}while(o!==n[0]);r=d(n[0]),i="www."===n[1]?"http://"+r:r}return{type:"link",raw:n[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}},n.inlineText=function(e,t){var n,r=this.rules.inline.text.exec(e);if(r)return n=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):d(r[0]):r[0]:d(this.options.smartypants?t(r[0]):r[0]),{type:"text",raw:r[0],text:n}},t}(),M={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)( [^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?<?([^\s>]+)>?(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:w,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};M.def=m(M.def).replace("label",M._label).replace("title",M._title).getRegex(),M.bullet=/(?:[*+-]|\d{1,9}[.)])/,M.listItemStart=m(/^( *)(bull) */).replace("bull",M.bullet).getRegex(),M.list=m(M.list).replace(/bull/g,M.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+M.def.source+")").getRegex(),M._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",M._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,M.html=m(M.html,"i").replace("comment",M._comment).replace("tag",M._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),M.paragraph=m(M._paragraph).replace("hr",M.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M._tag).getRegex(),M.blockquote=m(M.blockquote).replace("paragraph",M.paragraph).getRegex(),M.normal=k({},M),M.gfm=k({},M.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),M.gfm.table=m(M.gfm.table).replace("hr",M.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M._tag).getRegex(),M.gfm.paragraph=m(M._paragraph).replace("hr",M.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",M.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M._tag).getRegex(),M.pedantic=k({},M.normal,{html:m("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",M._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:w,paragraph:m(M.normal._paragraph).replace("hr",M.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",M.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});var B={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:w,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^[^_*]*?\_\_[^_*]*?\*[^_*]*?(?=\_\_)|[punct_](\*+)(?=[\s]|$)|[^punct*_\s](\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|[^punct*_\s](\*+)(?=[^punct*_\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?\_[^_*]*?(?=\*\*)|[punct*](\_+)(?=[\s]|$)|[^punct*_\s](\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:w,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function N(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function O(e){var t,n,r="",i=e.length;for(t=0;t<i;t++)n=e.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),r+="&#"+n+";";return r}B._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",B.punctuation=m(B.punctuation).replace(/punctuation/g,B._punctuation).getRegex(),B.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,B.escapedEmSt=/\\\*|\\_/g,B._comment=m(M._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),B.emStrong.lDelim=m(B.emStrong.lDelim).replace(/punct/g,B._punctuation).getRegex(),B.emStrong.rDelimAst=m(B.emStrong.rDelimAst,"g").replace(/punct/g,B._punctuation).getRegex(),B.emStrong.rDelimUnd=m(B.emStrong.rDelimUnd,"g").replace(/punct/g,B._punctuation).getRegex(),B._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,B._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,B._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,B.autolink=m(B.autolink).replace("scheme",B._scheme).replace("email",B._email).getRegex(),B._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,B.tag=m(B.tag).replace("comment",B._comment).replace("attribute",B._attribute).getRegex(),B._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,B._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,B._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,B.link=m(B.link).replace("label",B._label).replace("href",B._href).replace("title",B._title).getRegex(),B.reflink=m(B.reflink).replace("label",B._label).replace("ref",M._label).getRegex(),B.nolink=m(B.nolink).replace("ref",M._label).getRegex(),B.reflinkSearch=m(B.reflinkSearch,"g").replace("reflink",B.reflink).replace("nolink",B.nolink).getRegex(),B.normal=k({},B),B.pedantic=k({},B.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:m(/^!?\[(label)\]\((.*?)\)/).replace("label",B._label).getRegex(),reflink:m(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",B._label).getRegex()}),B.gfm=k({},B.normal,{escape:m(B.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),B.gfm.url=m(B.gfm.url,"i").replace("email",B.gfm._extended_email).getRegex(),B.breaks=k({},B.gfm,{br:m(B.br).replace("{2,}","*").getRegex(),text:m(B.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});var I=function(){function n(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||e.defaults,this.options.tokenizer=this.options.tokenizer||new L,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};var n={block:M.normal,inline:B.normal};this.options.pedantic?(n.block=M.pedantic,n.inline=B.pedantic):this.options.gfm&&(n.block=M.gfm,this.options.breaks?n.inline=B.breaks:n.inline=B.gfm),this.tokenizer.rules=n}n.lex=function(e,t){return new n(t).lex(e)},n.lexInline=function(e,t){return new n(t).inlineTokens(e)};var r,i,o,a=n.prototype;return a.lex=function(e){var t;for(e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    "),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens},a.blockTokens=function(e,t){var n,r,i,o,a=this;for(void 0===t&&(t=[]),this.options.pedantic&&(e=e.replace(/^ +$/gm,""));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((function(r){return!!(n=r.call({lexer:a},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)}))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),1===n.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),!(r=t[t.length-1])||"paragraph"!==r.type&&"text"!==r.type?t.push(n):(r.raw+="\n"+n.raw,r.text+="\n"+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),!(r=t[t.length-1])||"paragraph"!==r.type&&"text"!==r.type?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(r.raw+="\n"+n.raw,r.text+="\n"+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else if(i=e,this.options.extensions&&this.options.extensions.startBlock&&function(){var t=1/0,n=e.slice(1),r=void 0;a.options.extensions.startBlock.forEach((function(e){"number"==typeof(r=e.call({lexer:this},n))&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(i=e.substring(0,t+1))}(),this.state.top&&(n=this.tokenizer.paragraph(i)))r=t[t.length-1],o&&"paragraph"===r.type?(r.raw+="\n"+n.raw,r.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),o=i.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),(r=t[t.length-1])&&"text"===r.type?(r.raw+="\n"+n.raw,r.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);else if(e){var l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}return this.state.top=!0,t},a.inline=function(e,t){this.inlineQueue.push({src:e,tokens:t})},a.inlineTokens=function(e,t){var n,r,i,o=this;void 0===t&&(t=[]);var a,l,s,u=e;if(this.tokens.links){var c=Object.keys(this.tokens.links);if(c.length>0)for(;null!=(a=this.tokenizer.rules.inline.reflinkSearch.exec(u));)c.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,a.index)+"["+E("a",a[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(a=this.tokenizer.rules.inline.blockSkip.exec(u));)u=u.slice(0,a.index)+"["+E("a",a[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(a=this.tokenizer.rules.inline.escapedEmSt.exec(u));)u=u.slice(0,a.index)+"++"+u.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex);for(;e;)if(l||(s=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((function(r){return!!(n=r.call({lexer:o},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)}))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),(r=t[t.length-1])&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),(r=t[t.length-1])&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,u,s))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e,O))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e,O))){if(i=e,this.options.extensions&&this.options.extensions.startInline&&function(){var t=1/0,n=e.slice(1),r=void 0;o.options.extensions.startInline.forEach((function(e){"number"==typeof(r=e.call({lexer:this},n))&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(i=e.substring(0,t+1))}(),n=this.tokenizer.inlineText(i,N))e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(s=n.raw.slice(-1)),l=!0,(r=t[t.length-1])&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){var d="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(d);break}throw new Error(d)}}else e=e.substring(n.raw.length),t.push(n);return t},r=n,o=[{key:"rules",get:function(){return{block:M,inline:B}}}],(i=null)&&t(r.prototype,i),o&&t(r,o),Object.defineProperty(r,"prototype",{writable:!1}),n}(),z=function(){function t(t){this.options=t||e.defaults}var n=t.prototype;return n.code=function(e,t,n){var r=(t||"").match(/\S*/)[0];if(this.options.highlight){var i=this.options.highlight(e,r);null!=i&&i!==e&&(n=!0,e=i)}return e=e.replace(/\n$/,"")+"\n",r?'<pre><code class="'+this.options.langPrefix+d(r,!0)+'">'+(n?e:d(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:d(e,!0))+"</code></pre>\n"},n.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},n.html=function(e){return e},n.heading=function(e,t,n,r){return this.options.headerIds?"<h"+t+' id="'+this.options.headerPrefix+r.slug(n)+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},n.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},n.list=function(e,t,n){var r=t?"ol":"ul";return"<"+r+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+r+">\n"},n.listitem=function(e){return"<li>"+e+"</li>\n"},n.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},n.paragraph=function(e){return"<p>"+e+"</p>\n"},n.table=function(e,t){return t&&(t="<tbody>"+t+"</tbody>"),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"},n.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},n.tablecell=function(e,t){var n=t.header?"th":"td";return(t.align?"<"+n+' align="'+t.align+'">':"<"+n+">")+e+"</"+n+">\n"},n.strong=function(e){return"<strong>"+e+"</strong>"},n.em=function(e){return"<em>"+e+"</em>"},n.codespan=function(e){return"<code>"+e+"</code>"},n.br=function(){return this.options.xhtml?"<br/>":"<br>"},n.del=function(e){return"<del>"+e+"</del>"},n.link=function(e,t,n){if(null===(e=x(this.options.sanitize,this.options.baseUrl,e)))return n;var r='<a href="'+d(e)+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>"},n.image=function(e,t,n){if(null===(e=x(this.options.sanitize,this.options.baseUrl,e)))return n;var r='<img src="'+e+'" alt="'+n+'"';return t&&(r+=' title="'+t+'"'),r+=this.options.xhtml?"/>":">"},n.text=function(e){return e},t}(),H=function(){function e(){}var t=e.prototype;return t.strong=function(e){return e},t.em=function(e){return e},t.codespan=function(e){return e},t.del=function(e){return e},t.html=function(e){return e},t.text=function(e){return e},t.link=function(e,t,n){return""+n},t.image=function(e,t,n){return""+n},t.br=function(){return""},e}(),R=function(){function e(){this.seen={}}var t=e.prototype;return t.serialize=function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},t.getNextSafeSlug=function(e,t){var n=e,r=0;if(this.seen.hasOwnProperty(n)){r=this.seen[e];do{n=e+"-"+ ++r}while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=r,this.seen[n]=0),n},t.slug=function(e,t){void 0===t&&(t={});var n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)},e}(),P=function(){function t(t){this.options=t||e.defaults,this.options.renderer=this.options.renderer||new z,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new H,this.slugger=new R}t.parse=function(e,n){return new t(n).parse(e)},t.parseInline=function(e,n){return new t(n).parseInline(e)};var n=t.prototype;return n.parse=function(e,t){void 0===t&&(t=!0);var n,r,i,o,a,l,s,u,c,d,h,p,m,g,v,x,y,b,D,C="",w=e.length;for(n=0;n<w;n++)if(d=e[n],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[d.type])||!1===(D=this.options.extensions.renderers[d.type].call({parser:this},d))&&["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(d.type))switch(d.type){case"space":continue;case"hr":C+=this.renderer.hr();continue;case"heading":C+=this.renderer.heading(this.parseInline(d.tokens),d.depth,f(this.parseInline(d.tokens,this.textRenderer)),this.slugger);continue;case"code":C+=this.renderer.code(d.text,d.lang,d.escaped);continue;case"table":for(u="",s="",o=d.header.length,r=0;r<o;r++)s+=this.renderer.tablecell(this.parseInline(d.header[r].tokens),{header:!0,align:d.align[r]});for(u+=this.renderer.tablerow(s),c="",o=d.rows.length,r=0;r<o;r++){for(s="",a=(l=d.rows[r]).length,i=0;i<a;i++)s+=this.renderer.tablecell(this.parseInline(l[i].tokens),{header:!1,align:d.align[i]});c+=this.renderer.tablerow(s)}C+=this.renderer.table(u,c);continue;case"blockquote":c=this.parse(d.tokens),C+=this.renderer.blockquote(c);continue;case"list":for(h=d.ordered,p=d.start,m=d.loose,o=d.items.length,c="",r=0;r<o;r++)x=(v=d.items[r]).checked,y=v.task,g="",v.task&&(b=this.renderer.checkbox(x),m?v.tokens.length>0&&"paragraph"===v.tokens[0].type?(v.tokens[0].text=b+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&"text"===v.tokens[0].tokens[0].type&&(v.tokens[0].tokens[0].text=b+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:b}):g+=b),g+=this.parse(v.tokens,m),c+=this.renderer.listitem(g,y,x);C+=this.renderer.list(c,h,p);continue;case"html":C+=this.renderer.html(d.text);continue;case"paragraph":C+=this.renderer.paragraph(this.parseInline(d.tokens));continue;case"text":for(c=d.tokens?this.parseInline(d.tokens):d.text;n+1<w&&"text"===e[n+1].type;)c+="\n"+((d=e[++n]).tokens?this.parseInline(d.tokens):d.text);C+=t?this.renderer.paragraph(c):c;continue;default:var k='Token with "'+d.type+'" type was not found.';if(this.options.silent)return void console.error(k);throw new Error(k)}else C+=D||"";return C},n.parseInline=function(e,t){t=t||this.renderer;var n,r,i,o="",a=e.length;for(n=0;n<a;n++)if(r=e[n],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type])||!1===(i=this.options.extensions.renderers[r.type].call({parser:this},r))&&["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type))switch(r.type){case"escape":case"text":o+=t.text(r.text);break;case"html":o+=t.html(r.text);break;case"link":o+=t.link(r.href,r.title,this.parseInline(r.tokens,t));break;case"image":o+=t.image(r.href,r.title,r.text);break;case"strong":o+=t.strong(this.parseInline(r.tokens,t));break;case"em":o+=t.em(this.parseInline(r.tokens,t));break;case"codespan":o+=t.codespan(r.text);break;case"br":o+=t.br();break;case"del":o+=t.del(this.parseInline(r.tokens,t));break;default:var l='Token with "'+r.type+'" type was not found.';if(this.options.silent)return void console.error(l);throw new Error(l)}else o+=i||"";return o},t}();function _(e,t,n){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"==typeof t&&(n=t,t=null),A(t=k({},_.defaults,t||{})),n){var r,i=t.highlight;try{r=I.lex(e,t)}catch(e){return n(e)}var o=function(e){var o;if(!e)try{t.walkTokens&&_.walkTokens(r,t.walkTokens),o=P.parse(r,t)}catch(t){e=t}return t.highlight=i,e?n(e):n(null,o)};if(!i||i.length<3)return o();if(delete t.highlight,!r.length)return o();var a=0;return _.walkTokens(r,(function(e){"code"===e.type&&(a++,setTimeout((function(){i(e.text,e.lang,(function(t,n){if(t)return o(t);null!=n&&n!==e.text&&(e.text=n,e.escaped=!0),0===--a&&o()}))}),0))})),void(0===a&&o())}try{var l=I.lex(e,t);return t.walkTokens&&_.walkTokens(l,t.walkTokens),P.parse(l,t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+d(e.message+"",!0)+"</pre>";throw e}}_.options=_.setOptions=function(t){var n;return k(_.defaults,t),n=_.defaults,e.defaults=n,_},_.getDefaults=i,_.defaults=e.defaults,_.use=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=k.apply(void 0,[{}].concat(t)),o=_.defaults.extensions||{renderers:{},childTokens:{}};t.forEach((function(e){if(e.extensions&&(r=!0,e.extensions.forEach((function(e){if(!e.name)throw new Error("extension name required");if(e.renderer){var t=o.renderers?o.renderers[e.name]:null;o.renderers[e.name]=t?function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=e.renderer.apply(this,r);return!1===o&&(o=t.apply(this,r)),o}:e.renderer}if(e.tokenizer){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");o[e.level]?o[e.level].unshift(e.tokenizer):o[e.level]=[e.tokenizer],e.start&&("block"===e.level?o.startBlock?o.startBlock.push(e.start):o.startBlock=[e.start]:"inline"===e.level&&(o.startInline?o.startInline.push(e.start):o.startInline=[e.start]))}e.childTokens&&(o.childTokens[e.name]=e.childTokens)}))),e.renderer&&function(){var t=_.defaults.renderer||new z,n=function(n){var r=t[n];t[n]=function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var l=e.renderer[n].apply(t,o);return!1===l&&(l=r.apply(t,o)),l}};for(var r in e.renderer)n(r);i.renderer=t}(),e.tokenizer&&function(){var t=_.defaults.tokenizer||new L,n=function(n){var r=t[n];t[n]=function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var l=e.tokenizer[n].apply(t,o);return!1===l&&(l=r.apply(t,o)),l}};for(var r in e.tokenizer)n(r);i.tokenizer=t}(),e.walkTokens){var t=_.defaults.walkTokens;i.walkTokens=function(n){e.walkTokens.call(this,n),t&&t.call(this,n)}}r&&(i.extensions=o),_.setOptions(i)}))},_.walkTokens=function(e,t){for(var n,i=function(){var e=n.value;switch(t.call(_,e),e.type){case"table":for(var i,o=r(e.header);!(i=o()).done;){var a=i.value;_.walkTokens(a.tokens,t)}for(var l,s=r(e.rows);!(l=s()).done;)for(var u,c=r(l.value);!(u=c()).done;){var d=u.value;_.walkTokens(d.tokens,t)}break;case"list":_.walkTokens(e.items,t);break;default:_.defaults.extensions&&_.defaults.extensions.childTokens&&_.defaults.extensions.childTokens[e.type]?_.defaults.extensions.childTokens[e.type].forEach((function(n){_.walkTokens(e[n],t)})):e.tokens&&_.walkTokens(e.tokens,t)}},o=r(e);!(n=o()).done;)i()},_.parseInline=function(e,t){if(null==e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");A(t=k({},_.defaults,t||{}));try{var n=I.lexInline(e,t);return t.walkTokens&&_.walkTokens(n,t.walkTokens),P.parseInline(n,t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+d(e.message+"",!0)+"</pre>";throw e}},_.Parser=P,_.parser=P.parse,_.Renderer=z,_.TextRenderer=H,_.Lexer=I,_.lexer=I.lex,_.Tokenizer=L,_.Slugger=R,_.parse=_;var W=_.options,j=_.setOptions,q=_.use,U=_.walkTokens,$=_.parseInline,G=_,V=P.parse,X=I.lex;e.Lexer=I,e.Parser=P,e.Renderer=z,e.Slugger=R,e.TextRenderer=H,e.Tokenizer=L,e.getDefaults=i,e.lexer=X,e.marked=_,e.options=W,e.parse=G,e.parseInline=$,e.parser=V,e.setOptions=j,e.use=q,e.walkTokens=U,Object.defineProperty(e,"__esModule",{value:!0})}))},{}],16:[function(e,t,n){(function(n){(function(){var r;!function(){"use strict";(r=function(e,t,r,i){i=i||{},this.dictionary=null,this.rules={},this.dictionaryTable={},this.compoundRules=[],this.compoundRuleCodes={},this.replacementTable=[],this.flags=i.flags||{},this.memoized={},this.loaded=!1;var o,a,l,s,u,c=this;function d(e,t){var n=c._readFile(e,null,i.asyncLoad);i.asyncLoad?n.then((function(e){t(e)})):t(n)}function h(e){t=e,r&&p()}function f(e){r=e,t&&p()}function p(){for(c.rules=c._parseAFF(t),c.compoundRuleCodes={},a=0,s=c.compoundRules.length;a<s;a++){var e=c.compoundRules[a];for(l=0,u=e.length;l<u;l++)c.compoundRuleCodes[e[l]]=[]}for(a in"ONLYINCOMPOUND"in c.flags&&(c.compoundRuleCodes[c.flags.ONLYINCOMPOUND]=[]),c.dictionaryTable=c._parseDIC(r),c.compoundRuleCodes)0===c.compoundRuleCodes[a].length&&delete c.compoundRuleCodes[a];for(a=0,s=c.compoundRules.length;a<s;a++){var n=c.compoundRules[a],o="";for(l=0,u=n.length;l<u;l++){var d=n[l];d in c.compoundRuleCodes?o+="("+c.compoundRuleCodes[d].join("|")+")":o+=d}c.compoundRules[a]=new RegExp(o,"i")}c.loaded=!0,i.asyncLoad&&i.loadedCallback&&i.loadedCallback(c)}return e&&(c.dictionary=e,t&&r?p():"undefined"!=typeof window&&"chrome"in window&&"extension"in window.chrome&&"getURL"in window.chrome.extension?(o=i.dictionaryPath?i.dictionaryPath:"typo/dictionaries",t||d(chrome.extension.getURL(o+"/"+e+"/"+e+".aff"),h),r||d(chrome.extension.getURL(o+"/"+e+"/"+e+".dic"),f)):(o=i.dictionaryPath?i.dictionaryPath:void 0!==n?n+"/dictionaries":"./dictionaries",t||d(o+"/"+e+"/"+e+".aff",h),r||d(o+"/"+e+"/"+e+".dic",f))),this}).prototype={load:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);return this},_readFile:function(t,n,r){if(n=n||"utf8","undefined"!=typeof XMLHttpRequest){var i,o=new XMLHttpRequest;return o.open("GET",t,r),r&&(i=new Promise((function(e,t){o.onload=function(){200===o.status?e(o.responseText):t(o.statusText)},o.onerror=function(){t(o.statusText)}}))),o.overrideMimeType&&o.overrideMimeType("text/plain; charset="+n),o.send(null),r?i:o.responseText}if(void 0!==e){var a=e("fs");try{if(a.existsSync(t))return a.readFileSync(t,n);console.log("Path "+t+" does not exist.")}catch(e){return console.log(e),""}}},_parseAFF:function(e){var t,n,r,i,o,a,l,s={},u=e.split(/\r?\n/);for(i=0,a=u.length;i<a;i++)if(t=(t=this._removeAffixComments(u[i])).trim()){var c=t.split(/\s+/),d=c[0];if("PFX"==d||"SFX"==d){var h=c[1],f=c[2],p=[];for(o=i+1,l=i+1+(n=parseInt(c[3],10));o<l;o++){var m=(r=u[o].split(/\s+/))[2],g=r[3].split("/"),v=g[0];"0"===v&&(v="");var x=this.parseRuleCodes(g[1]),y=r[4],b={};b.add=v,x.length>0&&(b.continuationClasses=x),"."!==y&&(b.match="SFX"===d?new RegExp(y+"$"):new RegExp("^"+y)),"0"!=m&&(b.remove="SFX"===d?new RegExp(m+"$"):m),p.push(b)}s[h]={type:d,combineable:"Y"==f,entries:p},i+=n}else if("COMPOUNDRULE"===d){for(o=i+1,l=i+1+(n=parseInt(c[1],10));o<l;o++)r=(t=u[o]).split(/\s+/),this.compoundRules.push(r[1]);i+=n}else"REP"===d?3===(r=t.split(/\s+/)).length&&this.replacementTable.push([r[1],r[2]]):this.flags[d]=c[1]}return s},_removeAffixComments:function(e){return e.match(/^\s*#/,"")?"":e},_parseDIC:function(e){var t=(e=this._removeDicComments(e)).split(/\r?\n/),n={};function r(e,t){n.hasOwnProperty(e)||(n[e]=null),t.length>0&&(null===n[e]&&(n[e]=[]),n[e].push(t))}for(var i=1,o=t.length;i<o;i++){var a=t[i];if(a){var l=a.split("/",2),s=l[0];if(l.length>1){var u=this.parseRuleCodes(l[1]);"NEEDAFFIX"in this.flags&&-1!=u.indexOf(this.flags.NEEDAFFIX)||r(s,u);for(var c=0,d=u.length;c<d;c++){var h=u[c],f=this.rules[h];if(f)for(var p=this._applyRule(s,f),m=0,g=p.length;m<g;m++){var v=p[m];if(r(v,[]),f.combineable)for(var x=c+1;x<d;x++){var y=u[x],b=this.rules[y];if(b&&b.combineable&&f.type!=b.type)for(var D=this._applyRule(v,b),C=0,w=D.length;C<w;C++){r(D[C],[])}}}h in this.compoundRuleCodes&&this.compoundRuleCodes[h].push(s)}}else r(s.trim(),[])}}return n},_removeDicComments:function(e){return e=e.replace(/^\t.*$/gm,"")},parseRuleCodes:function(e){if(!e)return[];if(!("FLAG"in this.flags))return e.split("");if("long"===this.flags.FLAG){for(var t=[],n=0,r=e.length;n<r;n+=2)t.push(e.substr(n,2));return t}return"num"===this.flags.FLAG?e.split(","):void 0},_applyRule:function(e,t){for(var n=t.entries,r=[],i=0,o=n.length;i<o;i++){var a=n[i];if(!a.match||e.match(a.match)){var l=e;if(a.remove&&(l=l.replace(a.remove,"")),"SFX"===t.type?l+=a.add:l=a.add+l,r.push(l),"continuationClasses"in a)for(var s=0,u=a.continuationClasses.length;s<u;s++){var c=this.rules[a.continuationClasses[s]];c&&(r=r.concat(this._applyRule(l,c)))}}}return r},check:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t=e.replace(/^\s\s*/,"").replace(/\s\s*$/,"");if(this.checkExact(t))return!0;if(t.toUpperCase()===t){var n=t[0]+t.substring(1).toLowerCase();if(this.hasFlag(n,"KEEPCASE"))return!1;if(this.checkExact(n))return!0;if(this.checkExact(t.toLowerCase()))return!0}var r=t[0].toLowerCase()+t.substring(1);if(r!==t){if(this.hasFlag(r,"KEEPCASE"))return!1;if(this.checkExact(r))return!0}return!1},checkExact:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t,n,r=this.dictionaryTable[e];if(void 0===r){if("COMPOUNDMIN"in this.flags&&e.length>=this.flags.COMPOUNDMIN)for(t=0,n=this.compoundRules.length;t<n;t++)if(e.match(this.compoundRules[t]))return!0}else{if(null===r)return!0;if("object"==typeof r)for(t=0,n=r.length;t<n;t++)if(!this.hasFlag(e,"ONLYINCOMPOUND",r[t]))return!0}return!1},hasFlag:function(e,t,n){if(!this.loaded)throw"Dictionary not loaded.";return!(!(t in this.flags)||(void 0===n&&(n=Array.prototype.concat.apply([],this.dictionaryTable[e])),!n||-1===n.indexOf(this.flags[t])))},alphabet:"",suggest:function(e,t){if(!this.loaded)throw"Dictionary not loaded.";if(t=t||5,this.memoized.hasOwnProperty(e)){var n=this.memoized[e].limit;if(t<=n||this.memoized[e].suggestions.length<n)return this.memoized[e].suggestions.slice(0,t)}if(this.check(e))return[];for(var r=0,i=this.replacementTable.length;r<i;r++){var o=this.replacementTable[r];if(-1!==e.indexOf(o[0])){var a=e.replace(o[0],o[1]);if(this.check(a))return[a]}}var l=this;function s(e,t){var n,r,i,o,a={},s=l.alphabet.length;if("string"==typeof e){var u=e;(e={})[u]=!0}for(var u in e)for(n=0,i=u.length+1;n<i;n++){var c=[u.substring(0,n),u.substring(n)];if(c[1]&&(o=c[0]+c[1].substring(1),t&&!l.check(o)||(o in a?a[o]+=1:a[o]=1)),c[1].length>1&&c[1][1]!==c[1][0]&&(o=c[0]+c[1][1]+c[1][0]+c[1].substring(2),t&&!l.check(o)||(o in a?a[o]+=1:a[o]=1)),c[1]){var d=c[1].substring(0,1).toUpperCase()===c[1].substring(0,1)?"uppercase":"lowercase";for(r=0;r<s;r++){var h=l.alphabet[r];"uppercase"===d&&(h=h.toUpperCase()),h!=c[1].substring(0,1)&&(o=c[0]+h+c[1].substring(1),t&&!l.check(o)||(o in a?a[o]+=1:a[o]=1))}}if(c[1])for(r=0;r<s;r++){d=c[0].substring(-1).toUpperCase()===c[0].substring(-1)&&c[1].substring(0,1).toUpperCase()===c[1].substring(0,1)?"uppercase":"lowercase",h=l.alphabet[r];"uppercase"===d&&(h=h.toUpperCase()),o=c[0]+h+c[1],t&&!l.check(o)||(o in a?a[o]+=1:a[o]=1)}}return a}return l.alphabet="abcdefghijklmnopqrstuvwxyz",this.memoized[e]={suggestions:function(e){var n,r=s(e),i=s(r,!0);for(var o in r)l.check(o)&&(o in i?i[o]+=r[o]:i[o]=r[o]);var a=[];for(n in i)i.hasOwnProperty(n)&&a.push([n,i[n]]);a.sort((function(e,t){var n=e[1],r=t[1];return n<r?-1:n>r?1:t[0].localeCompare(e[0])})).reverse();var u=[],c="lowercase";e.toUpperCase()===e?c="uppercase":e.substr(0,1).toUpperCase()+e.substr(1).toLowerCase()===e&&(c="capitalized");var d=t;for(n=0;n<Math.min(d,a.length);n++)"uppercase"===c?a[n][0]=a[n][0].toUpperCase():"capitalized"===c&&(a[n][0]=a[n][0].substr(0,1).toUpperCase()+a[n][0].substr(1)),l.hasFlag(a[n][0],"NOSUGGEST")||-1!=u.indexOf(a[n][0])?d++:u.push(a[n][0]);return u}(e),limit:t},this.memoized[e].suggestions}}}(),void 0!==t&&(t.exports=r)}).call(this)}).call(this,"/node_modules/typo-js")},{fs:1}],17:[function(e,t,n){var r=e("codemirror");r.commands.tabAndIndentMarkdownList=function(e){var t=e.listSelections()[0].head;if(!1!==e.getStateAfter(t.line).list)e.execCommand("indentMore");else if(e.options.indentWithTabs)e.execCommand("insertTab");else{var n=Array(e.options.tabSize+1).join(" ");e.replaceSelection(n)}},r.commands.shiftTabAndUnindentMarkdownList=function(e){var t=e.listSelections()[0].head;if(!1!==e.getStateAfter(t.line).list)e.execCommand("indentLess");else if(e.options.indentWithTabs)e.execCommand("insertTab");else{var n=Array(e.options.tabSize+1).join(" ");e.replaceSelection(n)}}},{codemirror:10}],18:[function(e,t,n){"use strict";var r=e("codemirror");e("codemirror/addon/edit/continuelist.js"),e("./codemirror/tablist"),e("codemirror/addon/display/fullscreen.js"),e("codemirror/mode/markdown/markdown.js"),e("codemirror/addon/mode/overlay.js"),e("codemirror/addon/display/placeholder.js"),e("codemirror/addon/display/autorefresh.js"),e("codemirror/addon/selection/mark-selection.js"),e("codemirror/addon/search/searchcursor.js"),e("codemirror/mode/gfm/gfm.js"),e("codemirror/mode/xml/xml.js");var i=e("codemirror-spell-checker"),o=e("marked").marked,a=/Mac/.test(navigator.platform),l=new RegExp(/(<a.*?https?:\/\/.*?[^a]>)+?/g),s={toggleBold:C,toggleItalic:w,drawLink:I,toggleHeadingSmaller:A,toggleHeadingBigger:E,drawImage:z,toggleBlockquote:F,toggleOrderedList:N,toggleUnorderedList:B,toggleCodeBlock:S,togglePreview:U,toggleStrikethrough:k,toggleHeading1:T,toggleHeading2:L,toggleHeading3:M,cleanBlock:O,drawTable:P,drawHorizontalRule:_,undo:W,redo:j,toggleSideBySide:q,toggleFullScreen:D},u={toggleBold:"Cmd-B",toggleItalic:"Cmd-I",drawLink:"Cmd-K",toggleHeadingSmaller:"Cmd-H",toggleHeadingBigger:"Shift-Cmd-H",cleanBlock:"Cmd-E",drawImage:"Cmd-Alt-I",toggleBlockquote:"Cmd-'",toggleOrderedList:"Cmd-Alt-L",toggleUnorderedList:"Cmd-L",toggleCodeBlock:"Cmd-Alt-C",togglePreview:"Cmd-P",toggleSideBySide:"F9",toggleFullScreen:"F11"},c=function(){var e,t=!1;return e=navigator.userAgent||navigator.vendor||window.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(e.substr(0,4)))&&(t=!0),t};function d(e){return e=a?e.replace("Ctrl","Cmd"):e.replace("Cmd","Ctrl")}var h={};function f(e){return h[e]||(h[e]=new RegExp("\\s*"+e+"(\\s*)","g"))}function p(e,t){if(e&&t){var n=f(t);e.className.match(n)||(e.className+=" "+t)}}function m(e,t){if(e&&t){var n=f(t);e.className.match(n)&&(e.className=e.className.replace(n,"$1"))}}function g(e,t,n,r){var i=v(e,!1,t,n,"button",r);i.className+=" easymde-dropdown",i.onclick=function(){i.focus()};var o=document.createElement("div");o.className="easymde-dropdown-content";for(var a=0;a<e.children.length;a++){var l,s=e.children[a];(l=v("string"==typeof s&&s in J?J[s]:s,!0,t,n,"button",r)).addEventListener("click",(function(e){e.stopPropagation()}),!1),o.appendChild(l)}return i.appendChild(o),i}function v(e,t,n,r,i,o){e=e||{};var l=document.createElement(i);if(e.attributes)for(var u in e.attributes)Object.prototype.hasOwnProperty.call(e.attributes,u)&&l.setAttribute(u,e.attributes[u]);l.className=e.name,l.setAttribute("type",i),n=null==n||n,e.name&&e.name in r&&(s[e.name]=e.action),e.title&&n&&(l.title=function(e,t,n){var r,i=e;t&&n[r=function(e){for(var t in s)if(s[t]===e)return t;return null}(t)]&&(i+=" ("+d(n[r])+")");return i}(e.title,e.action,r),a&&(l.title=l.title.replace("Ctrl","⌘"),l.title=l.title.replace("Alt","⌥"))),e.noDisable&&l.classList.add("no-disable"),e.noMobile&&l.classList.add("no-mobile");var c=[];void 0!==e.className&&(c=e.className.split(" "));for(var h=[],f=0;f<c.length;f++){var p=c[f];p.match(/^fa([srlb]|(-[\w-]*)|$)/)?h.push(p):l.classList.add(p)}l.tabIndex=-1;for(var m=document.createElement("i"),g=0;g<h.length;g++){var v=h[g];m.classList.add(v)}return l.appendChild(m),void 0!==e.icon&&(l.innerHTML=e.icon),e.action&&t&&("function"==typeof e.action?l.onclick=function(t){t.preventDefault(),e.action(o)}:"string"==typeof e.action&&(l.onclick=function(t){t.preventDefault(),window.open(e.action,"_blank")})),l}function x(){var e=document.createElement("i");return e.className="separator",e.innerHTML="|",e}function y(e,t){t=t||e.getCursor("start");var n=e.getTokenAt(t);if(!n.type)return{};for(var r,i,o=n.type.split(" "),a={},l=0;l<o.length;l++)"strong"===(r=o[l])?a.bold=!0:"variable-2"===r?(i=e.getLine(t.line),/^\s*\d+\.\s/.test(i)?a["ordered-list"]=!0:a["unordered-list"]=!0):"atom"===r?a.quote=!0:"em"===r?a.italic=!0:"quote"===r?a.quote=!0:"strikethrough"===r?a.strikethrough=!0:"comment"===r?a.code=!0:"link"===r?a.link=!0:"tag"===r?a.image=!0:r.match(/^header(-[1-6])?$/)&&(a[r.replace("header","heading")]=!0);return a}var b="";function D(e){var t=e.codemirror;t.setOption("fullScreen",!t.getOption("fullScreen")),t.getOption("fullScreen")?(b=document.body.style.overflow,document.body.style.overflow="hidden"):document.body.style.overflow=b;var n=t.getWrapperElement(),r=n.nextSibling;if(/editor-preview-active-side/.test(r.className))if(!1===e.options.sideBySideFullscreen){var i=n.parentNode;t.getOption("fullScreen")?m(i,"sided--no-fullscreen"):p(i,"sided--no-fullscreen")}else q(e);if(e.options.onToggleFullScreen&&e.options.onToggleFullScreen(t.getOption("fullScreen")||!1),void 0!==e.options.maxHeight&&(t.getOption("fullScreen")?(t.getScrollerElement().style.removeProperty("height"),r.style.removeProperty("height")):(t.getScrollerElement().style.height=e.options.maxHeight,e.setPreviewMaxHeight())),/fullscreen/.test(e.toolbar_div.className)?e.toolbar_div.className=e.toolbar_div.className.replace(/\s*fullscreen\b/,""):e.toolbar_div.className+=" fullscreen",e.toolbarElements&&e.toolbarElements.fullscreen){var o=e.toolbarElements.fullscreen;/active/.test(o.className)?o.className=o.className.replace(/\s*active\s*/g,""):o.className+=" active"}}function C(e){X(e,"bold",e.options.blockStyles.bold)}function w(e){X(e,"italic",e.options.blockStyles.italic)}function k(e){X(e,"strikethrough","~~")}function S(e){var t=e.options.blockStyles.code;function n(e){if("object"!=typeof e)throw"fencing_line() takes a 'line' object (not a line number, or line text).  Got: "+typeof e+": "+e;return e.styles&&e.styles[2]&&-1!==e.styles[2].indexOf("formatting-code-block")}function r(e){return e.state.base.base||e.state.base}function i(e,t,i,o,a){i=i||e.getLineHandle(t),o=o||e.getTokenAt({line:t,ch:1}),a=a||!!i.text&&e.getTokenAt({line:t,ch:i.text.length-1});var l=o.type?o.type.split(" "):[];return a&&r(a).indentedCode?"indented":-1!==l.indexOf("comment")&&(r(o).fencedChars||r(a).fencedChars||n(i)?"fenced":"single")}var o,a,l,s=e.codemirror,u=s.getCursor("start"),c=s.getCursor("end"),d=s.getTokenAt({line:u.line,ch:u.ch||1}),h=s.getLineHandle(u.line),f=i(s,u.line,h,d);if("single"===f){var p=h.text.slice(0,u.ch).replace("`",""),m=h.text.slice(u.ch).replace("`","");s.replaceRange(p+m,{line:u.line,ch:0},{line:u.line,ch:99999999999999}),u.ch--,u!==c&&c.ch--,s.setSelection(u,c),s.focus()}else if("fenced"===f)if(u.line!==c.line||u.ch!==c.ch){for(o=u.line;o>=0&&!n(h=s.getLineHandle(o));o--);var g,v,x,y,b=r(s.getTokenAt({line:o,ch:1})).fencedChars;n(s.getLineHandle(u.line))?(g="",v=u.line):n(s.getLineHandle(u.line-1))?(g="",v=u.line-1):(g=b+"\n",v=u.line),n(s.getLineHandle(c.line))?(x="",y=c.line,0===c.ch&&(y+=1)):0!==c.ch&&n(s.getLineHandle(c.line+1))?(x="",y=c.line+1):(x=b+"\n",y=c.line+1),0===c.ch&&(y-=1),s.operation((function(){s.replaceRange(x,{line:y,ch:0},{line:y+(x?0:1),ch:0}),s.replaceRange(g,{line:v,ch:0},{line:v+(g?0:1),ch:0})})),s.setSelection({line:v+(g?1:0),ch:0},{line:y+(g?1:-1),ch:0}),s.focus()}else{var D=u.line;if(n(s.getLineHandle(u.line))&&("fenced"===i(s,u.line+1)?(o=u.line,D=u.line+1):(a=u.line,D=u.line-1)),void 0===o)for(o=D;o>=0&&!n(h=s.getLineHandle(o));o--);if(void 0===a)for(l=s.lineCount(),a=D;a<l&&!n(h=s.getLineHandle(a));a++);s.operation((function(){s.replaceRange("",{line:o,ch:0},{line:o+1,ch:0}),s.replaceRange("",{line:a-1,ch:0},{line:a,ch:0})})),s.focus()}else if("indented"===f){if(u.line!==c.line||u.ch!==c.ch)o=u.line,a=c.line,0===c.ch&&a--;else{for(o=u.line;o>=0;o--)if(!(h=s.getLineHandle(o)).text.match(/^\s*$/)&&"indented"!==i(s,o,h)){o+=1;break}for(l=s.lineCount(),a=u.line;a<l;a++)if(!(h=s.getLineHandle(a)).text.match(/^\s*$/)&&"indented"!==i(s,a,h)){a-=1;break}}var C=s.getLineHandle(a+1),w=C&&s.getTokenAt({line:a+1,ch:C.text.length-1});w&&r(w).indentedCode&&s.replaceRange("\n",{line:a+1,ch:0});for(var k=o;k<=a;k++)s.indentLine(k,"subtract");s.focus()}else{var S=u.line===c.line&&u.ch===c.ch&&0===u.ch,F=u.line!==c.line;S||F?function(e,t,n,r){var i=t.line+1,o=n.line+1,a=t.line!==n.line,l=r+"\n",s="\n"+r;a&&o++,a&&0===n.ch&&(s=r+"\n",o--),$(e,!1,[l,s]),e.setSelection({line:i,ch:0},{line:o,ch:0})}(s,u,c,t):$(s,!1,["`","`"])}}function F(e){V(e.codemirror,"quote")}function A(e){G(e.codemirror,"smaller")}function E(e){G(e.codemirror,"bigger")}function T(e){G(e.codemirror,void 0,1)}function L(e){G(e.codemirror,void 0,2)}function M(e){G(e.codemirror,void 0,3)}function B(e){var t=e.codemirror,n="*";["-","+","*"].includes(e.options.unorderedListStyle)&&(n=e.options.unorderedListStyle),V(t,"unordered-list",n)}function N(e){V(e.codemirror,"ordered-list")}function O(e){!function(e){if(/editor-preview-active/.test(e.getWrapperElement().lastChild.className))return;for(var t,n=e.getCursor("start"),r=e.getCursor("end"),i=n.line;i<=r.line;i++)t=(t=e.getLine(i)).replace(/^[ ]*([# ]+|\*|-|[> ]+|[0-9]+(.|\)))[ ]*/,""),e.replaceRange(t,{line:i,ch:0},{line:i,ch:99999999999999})}(e.codemirror)}function I(e){var t=e.codemirror,n=y(t),r=e.options,i="https://";if(r.promptURLs&&!(i=prompt(r.promptTexts.link,"https://")))return!1;$(t,n.link,r.insertTexts.link,i)}function z(e){var t=e.codemirror,n=y(t),r=e.options,i="https://";if(r.promptURLs&&!(i=prompt(r.promptTexts.image,"https://")))return!1;$(t,n.image,r.insertTexts.image,i)}function H(e){e.openBrowseFileWindow()}function R(e,t){var n=e.codemirror,r=y(n),i=e.options,o=t.substr(t.lastIndexOf("/")+1),a=o.substring(o.lastIndexOf(".")+1).replace(/\?.*$/,"").toLowerCase();if(["png","jpg","jpeg","gif","svg"].includes(a))$(n,r.image,i.insertTexts.uploadedImage,t);else{var l=i.insertTexts.link;l[0]="["+o,$(n,r.link,l,t)}e.updateStatusBar("upload-image",e.options.imageTexts.sbOnUploaded.replace("#image_name#",o)),setTimeout((function(){e.updateStatusBar("upload-image",e.options.imageTexts.sbInit)}),1e3)}function P(e){var t=e.codemirror,n=y(t),r=e.options;$(t,n.table,r.insertTexts.table)}function _(e){var t=e.codemirror,n=y(t),r=e.options;$(t,n.image,r.insertTexts.horizontalRule)}function W(e){var t=e.codemirror;t.undo(),t.focus()}function j(e){var t=e.codemirror;t.redo(),t.focus()}function q(e){var t=e.codemirror,n=t.getWrapperElement(),r=n.nextSibling,i=e.toolbarElements&&e.toolbarElements["side-by-side"],o=!1,a=n.parentNode;/editor-preview-active-side/.test(r.className)?(!1===e.options.sideBySideFullscreen&&m(a,"sided--no-fullscreen"),r.className=r.className.replace(/\s*editor-preview-active-side\s*/g,""),i&&(i.className=i.className.replace(/\s*active\s*/g,"")),n.className=n.className.replace(/\s*CodeMirror-sided\s*/g," ")):(setTimeout((function(){t.getOption("fullScreen")||(!1===e.options.sideBySideFullscreen?p(a,"sided--no-fullscreen"):D(e)),r.className+=" editor-preview-active-side"}),1),i&&(i.className+=" active"),n.className+=" CodeMirror-sided",o=!0);var l=n.lastChild;if(/editor-preview-active/.test(l.className)){l.className=l.className.replace(/\s*editor-preview-active\s*/g,"");var s=e.toolbarElements.preview,u=e.toolbar_div;s.className=s.className.replace(/\s*active\s*/g,""),u.className=u.className.replace(/\s*disabled-for-preview*/g,"")}if(t.sideBySideRenderingFunction||(t.sideBySideRenderingFunction=function(){var t=e.options.previewRender(e.value(),r);null!=t&&(r.innerHTML=t)}),o){var c=e.options.previewRender(e.value(),r);null!=c&&(r.innerHTML=c),t.on("update",t.sideBySideRenderingFunction)}else t.off("update",t.sideBySideRenderingFunction);t.refresh()}function U(e){var t=e.codemirror,n=t.getWrapperElement(),r=e.toolbar_div,i=!!e.options.toolbar&&e.toolbarElements.preview,o=n.lastChild,a=t.getWrapperElement().nextSibling;if(/editor-preview-active-side/.test(a.className)&&q(e),!o||!/editor-preview-full/.test(o.className)){if((o=document.createElement("div")).className="editor-preview-full",e.options.previewClass)if(Array.isArray(e.options.previewClass))for(var l=0;l<e.options.previewClass.length;l++)o.className+=" "+e.options.previewClass[l];else"string"==typeof e.options.previewClass&&(o.className+=" "+e.options.previewClass);n.appendChild(o)}/editor-preview-active/.test(o.className)?(o.className=o.className.replace(/\s*editor-preview-active\s*/g,""),i&&(i.className=i.className.replace(/\s*active\s*/g,""),r.className=r.className.replace(/\s*disabled-for-preview*/g,""))):(setTimeout((function(){o.className+=" editor-preview-active"}),1),i&&(i.className+=" active",r.className+=" disabled-for-preview")),o.innerHTML=e.options.previewRender(e.value(),o)}function $(e,t,n,r){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){var i,o=n[0],a=n[1],l={},s={};Object.assign(l,e.getCursor("start")),Object.assign(s,e.getCursor("end")),r&&(o=o.replace("#url#",r),a=a.replace("#url#",r)),t?(o=(i=e.getLine(l.line)).slice(0,l.ch),a=i.slice(l.ch),e.replaceRange(o+a,{line:l.line,ch:0})):(i=e.getSelection(),e.replaceSelection(o+i+a),l.ch+=o.length,l!==s&&(s.ch+=o.length)),e.setSelection(l,s),e.focus()}}function G(e,t,n){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var r=e.getCursor("start"),i=e.getCursor("end"),o=r.line;o<=i.line;o++)!function(r){var i=e.getLine(r),o=i.search(/[^#]/);i=void 0!==t?o<=0?"bigger"==t?"###### "+i:"# "+i:6==o&&"smaller"==t?i.substr(7):1==o&&"bigger"==t?i.substr(2):"bigger"==t?i.substr(1):"#"+i:1==n?o<=0?"# "+i:o==n?i.substr(o+1):"# "+i.substr(o+1):2==n?o<=0?"## "+i:o==n?i.substr(o+1):"## "+i.substr(o+1):o<=0?"### "+i:o==n?i.substr(o+1):"### "+i.substr(o+1),e.replaceRange(i,{line:r,ch:0},{line:r,ch:99999999999999})}(o);e.focus()}}function V(e,t,n){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var r=/^(\s*)(\*|-|\+|\d*\.)(\s+)/,i=/^\s*/,o=y(e),a=e.getCursor("start"),l=e.getCursor("end"),s={quote:/^(\s*)>\s+/,"unordered-list":r,"ordered-list":r},u=function(e,t,o){var a=r.exec(t),l=function(e,t){return{quote:">","unordered-list":n,"ordered-list":"%%i."}[e].replace("%%i",t)}(e,c);return null!==a?(function(e,t){var r=new RegExp({quote:">","unordered-list":"\\"+n,"ordered-list":"\\d+."}[e]);return t&&r.test(t)}(e,a[2])&&(l=""),t=a[1]+l+a[3]+t.replace(i,"").replace(s[e],"$1")):0==o&&(t=l+" "+t),t},c=1,d=a.line;d<=l.line;d++)!function(n){var r=e.getLine(n);o[t]?r=r.replace(s[t],"$1"):("unordered-list"==t&&(r=u("ordered-list",r,!0)),r=u(t,r,!1),c+=1),e.replaceRange(r,{line:n,ch:0},{line:n,ch:99999999999999})}(d);e.focus()}}function X(e,t,n,r){if(!/editor-preview-active/.test(e.codemirror.getWrapperElement().lastChild.className)){r=void 0===r?n:r;var i,o=e.codemirror,a=y(o),l=n,s=r,u=o.getCursor("start"),c=o.getCursor("end");a[t]?(l=(i=o.getLine(u.line)).slice(0,u.ch),s=i.slice(u.ch),"bold"==t?(l=l.replace(/(\*\*|__)(?![\s\S]*(\*\*|__))/,""),s=s.replace(/(\*\*|__)/,"")):"italic"==t?(l=l.replace(/(\*|_)(?![\s\S]*(\*|_))/,""),s=s.replace(/(\*|_)/,"")):"strikethrough"==t&&(l=l.replace(/(\*\*|~~)(?![\s\S]*(\*\*|~~))/,""),s=s.replace(/(\*\*|~~)/,"")),o.replaceRange(l+s,{line:u.line,ch:0},{line:u.line,ch:99999999999999}),"bold"==t||"strikethrough"==t?(u.ch-=2,u!==c&&(c.ch-=2)):"italic"==t&&(u.ch-=1,u!==c&&(c.ch-=1))):(i=o.getSelection(),"bold"==t?i=(i=i.split("**").join("")).split("__").join(""):"italic"==t?i=(i=i.split("*").join("")).split("_").join(""):"strikethrough"==t&&(i=i.split("~~").join("")),o.replaceSelection(l+i+s),u.ch+=n.length,c.ch=u.ch+i.length),o.setSelection(u,c),o.focus()}}function K(e,t){if(Math.abs(e)<1024)return""+e+t[0];var n=0;do{e/=1024,++n}while(Math.abs(e)>=1024&&n<t.length);return""+e.toFixed(1)+t[n]}function Z(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(t[n]instanceof Array?e[n]=t[n].concat(e[n]instanceof Array?e[n]:[]):null!==t[n]&&"object"==typeof t[n]&&t[n].constructor===Object?e[n]=Z(e[n]||{},t[n]):e[n]=t[n]);return e}function Y(e){for(var t=1;t<arguments.length;t++)e=Z(e,arguments[t]);return e}function Q(e){var t=e.match(/[a-zA-Z0-9_\u00A0-\u02AF\u0392-\u03c9\u0410-\u04F9]+|[\u4E00-\u9FFF\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\uac00-\ud7af]+/g),n=0;if(null===t)return n;for(var r=0;r<t.length;r++)t[r].charCodeAt(0)>=19968?n+=t[r].length:n+=1;return n}var J={bold:{name:"bold",action:C,className:"fa fa-bold",title:"Bold",default:!0},italic:{name:"italic",action:w,className:"fa fa-italic",title:"Italic",default:!0},strikethrough:{name:"strikethrough",action:k,className:"fa fa-strikethrough",title:"Strikethrough"},heading:{name:"heading",action:A,className:"fa fa-header fa-heading",title:"Heading",default:!0},"heading-smaller":{name:"heading-smaller",action:A,className:"fa fa-header fa-heading header-smaller",title:"Smaller Heading"},"heading-bigger":{name:"heading-bigger",action:E,className:"fa fa-header fa-heading header-bigger",title:"Bigger Heading"},"heading-1":{name:"heading-1",action:T,className:"fa fa-header fa-heading header-1",title:"Big Heading"},"heading-2":{name:"heading-2",action:L,className:"fa fa-header fa-heading header-2",title:"Medium Heading"},"heading-3":{name:"heading-3",action:M,className:"fa fa-header fa-heading header-3",title:"Small Heading"},"separator-1":{name:"separator-1"},code:{name:"code",action:S,className:"fa fa-code",title:"Code"},quote:{name:"quote",action:F,className:"fa fa-quote-left",title:"Quote",default:!0},"unordered-list":{name:"unordered-list",action:B,className:"fa fa-list-ul",title:"Generic List",default:!0},"ordered-list":{name:"ordered-list",action:N,className:"fa fa-list-ol",title:"Numbered List",default:!0},"clean-block":{name:"clean-block",action:O,className:"fa fa-eraser",title:"Clean block"},"separator-2":{name:"separator-2"},link:{name:"link",action:I,className:"fa fa-link",title:"Create Link",default:!0},image:{name:"image",action:z,className:"fa fa-image",title:"Insert Image",default:!0},"upload-image":{name:"upload-image",action:H,className:"fa fa-image",title:"Import an image"},table:{name:"table",action:P,className:"fa fa-table",title:"Insert Table"},"horizontal-rule":{name:"horizontal-rule",action:_,className:"fa fa-minus",title:"Insert Horizontal Line"},"separator-3":{name:"separator-3"},preview:{name:"preview",action:U,className:"fa fa-eye",noDisable:!0,title:"Toggle Preview",default:!0},"side-by-side":{name:"side-by-side",action:q,className:"fa fa-columns",noDisable:!0,noMobile:!0,title:"Toggle Side by Side",default:!0},fullscreen:{name:"fullscreen",action:D,className:"fa fa-arrows-alt",noDisable:!0,noMobile:!0,title:"Toggle Fullscreen",default:!0},"separator-4":{name:"separator-4"},guide:{name:"guide",action:"https://www.markdownguide.org/basic-syntax/",className:"fa fa-question-circle",noDisable:!0,title:"Markdown Guide",default:!0},"separator-5":{name:"separator-5"},undo:{name:"undo",action:W,className:"fa fa-undo",noDisable:!0,title:"Undo"},redo:{name:"redo",action:j,className:"fa fa-repeat fa-redo",noDisable:!0,title:"Redo"}},ee={link:["[","](#url#)"],image:["![](","#url#)"],uploadedImage:["![](#url#)",""],table:["","\n\n| Column 1 | Column 2 | Column 3 |\n| -------- | -------- | -------- |\n| Text     | Text     | Text     |\n\n"],horizontalRule:["","\n\n-----\n\n"]},te={link:"URL for the link:",image:"URL of the image:"},ne={locale:"en-US",format:{hour:"2-digit",minute:"2-digit"}},re={bold:"**",code:"```",italic:"*"},ie={sbInit:"Attach files by drag and dropping or pasting from clipboard.",sbOnDragEnter:"Drop image to upload it.",sbOnDrop:"Uploading image #images_names#...",sbProgress:"Uploading #file_name#: #progress#%",sbOnUploaded:"Uploaded #image_name#",sizeUnits:" B, KB, MB"},oe={noFileGiven:"You must select a file.",typeNotAllowed:"This image type is not allowed.",fileTooLarge:"Image #image_name# is too big (#image_size#).\nMaximum file size is #image_max_size#.",importError:"Something went wrong when uploading the image #image_name#."};function ae(e){(e=e||{}).parent=this;var t=!0;if(!1===e.autoDownloadFontAwesome&&(t=!1),!0!==e.autoDownloadFontAwesome)for(var n=document.styleSheets,r=0;r<n.length;r++)n[r].href&&n[r].href.indexOf("//maxcdn.bootstrapcdn.com/font-awesome/")>-1&&(t=!1);if(t){var i=document.createElement("link");i.rel="stylesheet",i.href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css",document.getElementsByTagName("head")[0].appendChild(i)}if(e.element)this.element=e.element;else if(null===e.element)return void console.log("EasyMDE: Error. No element was found.");if(void 0===e.toolbar)for(var o in e.toolbar=[],J)Object.prototype.hasOwnProperty.call(J,o)&&(-1!=o.indexOf("separator-")&&e.toolbar.push("|"),(!0===J[o].default||e.showIcons&&e.showIcons.constructor===Array&&-1!=e.showIcons.indexOf(o))&&e.toolbar.push(o));if(Object.prototype.hasOwnProperty.call(e,"previewClass")||(e.previewClass="editor-preview"),Object.prototype.hasOwnProperty.call(e,"status")||(e.status=["autosave","lines","words","cursor"],e.uploadImage&&e.status.unshift("upload-image")),e.previewRender||(e.previewRender=function(e){return this.parent.markdown(e)}),e.parsingConfig=Y({highlightFormatting:!0},e.parsingConfig||{}),e.insertTexts=Y({},ee,e.insertTexts||{}),e.promptTexts=Y({},te,e.promptTexts||{}),e.blockStyles=Y({},re,e.blockStyles||{}),null!=e.autosave&&(e.autosave.timeFormat=Y({},ne,e.autosave.timeFormat||{})),e.shortcuts=Y({},u,e.shortcuts||{}),e.maxHeight=e.maxHeight||void 0,e.direction=e.direction||"ltr",void 0!==e.maxHeight?e.minHeight=e.maxHeight:e.minHeight=e.minHeight||"300px",e.errorCallback=e.errorCallback||function(e){alert(e)},e.uploadImage=e.uploadImage||!1,e.imageMaxSize=e.imageMaxSize||2097152,e.imageAccept=e.imageAccept||"image/png, image/jpeg",e.imageTexts=Y({},ie,e.imageTexts||{}),e.errorMessages=Y({},oe,e.errorMessages||{}),null!=e.autosave&&null!=e.autosave.unique_id&&""!=e.autosave.unique_id&&(e.autosave.uniqueId=e.autosave.unique_id),e.overlayMode&&void 0===e.overlayMode.combine&&(e.overlayMode.combine=!0),this.options=e,this.render(),!e.initialValue||this.options.autosave&&!0===this.options.autosave.foundSavedValue||this.value(e.initialValue),e.uploadImage){var a=this;this.codemirror.on("dragenter",(function(e,t){a.updateStatusBar("upload-image",a.options.imageTexts.sbOnDragEnter),t.stopPropagation(),t.preventDefault()})),this.codemirror.on("dragend",(function(e,t){a.updateStatusBar("upload-image",a.options.imageTexts.sbInit),t.stopPropagation(),t.preventDefault()})),this.codemirror.on("dragleave",(function(e,t){a.updateStatusBar("upload-image",a.options.imageTexts.sbInit),t.stopPropagation(),t.preventDefault()})),this.codemirror.on("dragover",(function(e,t){a.updateStatusBar("upload-image",a.options.imageTexts.sbOnDragEnter),t.stopPropagation(),t.preventDefault()})),this.codemirror.on("drop",(function(t,n){n.stopPropagation(),n.preventDefault(),e.imageUploadFunction?a.uploadImagesUsingCustomFunction(e.imageUploadFunction,n.dataTransfer.files):a.uploadImages(n.dataTransfer.files)})),this.codemirror.on("paste",(function(t,n){e.imageUploadFunction?a.uploadImagesUsingCustomFunction(e.imageUploadFunction,n.clipboardData.files):a.uploadImages(n.clipboardData.files)}))}}function le(){if("object"!=typeof localStorage)return!1;try{localStorage.setItem("smde_localStorage",1),localStorage.removeItem("smde_localStorage")}catch(e){return!1}return!0}ae.prototype.uploadImages=function(e,t,n){if(0!==e.length){for(var r=[],i=0;i<e.length;i++)r.push(e[i].name),this.uploadImage(e[i],t,n);this.updateStatusBar("upload-image",this.options.imageTexts.sbOnDrop.replace("#images_names#",r.join(", ")))}},ae.prototype.uploadImagesUsingCustomFunction=function(e,t){if(0!==t.length){for(var n=[],r=0;r<t.length;r++)n.push(t[r].name),this.uploadImageUsingCustomFunction(e,t[r]);this.updateStatusBar("upload-image",this.options.imageTexts.sbOnDrop.replace("#images_names#",n.join(", ")))}},ae.prototype.updateStatusBar=function(e,t){if(this.gui.statusbar){var n=this.gui.statusbar.getElementsByClassName(e);1===n.length?this.gui.statusbar.getElementsByClassName(e)[0].textContent=t:0===n.length?console.log("EasyMDE: status bar item "+e+" was not found."):console.log("EasyMDE: Several status bar items named "+e+" was found.")}},ae.prototype.markdown=function(e){if(o){var t;if(t=this.options&&this.options.renderingConfig&&this.options.renderingConfig.markedOptions?this.options.renderingConfig.markedOptions:{},this.options&&this.options.renderingConfig&&!1===this.options.renderingConfig.singleLineBreaks?t.breaks=!1:t.breaks=!0,this.options&&this.options.renderingConfig&&!0===this.options.renderingConfig.codeSyntaxHighlighting){var n=this.options.renderingConfig.hljs||window.hljs;n&&(t.highlight=function(e,t){return t&&n.getLanguage(t)?n.highlight(t,e).value:n.highlightAuto(e).value})}o.setOptions(t);var r=o.parse(e);return this.options.renderingConfig&&"function"==typeof this.options.renderingConfig.sanitizerFunction&&(r=this.options.renderingConfig.sanitizerFunction.call(this,r)),r=function(e){for(var t=(new DOMParser).parseFromString(e,"text/html"),n=t.getElementsByTagName("li"),r=0;r<n.length;r++)for(var i=n[r],o=0;o<i.children.length;o++){var a=i.children[o];a instanceof HTMLInputElement&&"checkbox"===a.type&&(i.style.marginLeft="-1.5em",i.style.listStyleType="none")}return t.documentElement.innerHTML}(r=function(e){for(var t;null!==(t=l.exec(e));){var n=t[0];if(-1===n.indexOf("target=")){var r=n.replace(/>$/,' target="_blank">');e=e.replace(n,r)}}return e}(r))}},ae.prototype.render=function(e){if(e||(e=this.element||document.getElementsByTagName("textarea")[0]),!this._rendered||this._rendered!==e){this.element=e;var t,n,o=this.options,a=this,l={};for(var u in o.shortcuts)null!==o.shortcuts[u]&&null!==s[u]&&function(e){l[d(o.shortcuts[e])]=function(){var t=s[e];"function"==typeof t?t(a):"string"==typeof t&&window.open(t,"_blank")}}(u);if(l.Enter="newlineAndIndentContinueMarkdownList",l.Tab="tabAndIndentMarkdownList",l["Shift-Tab"]="shiftTabAndUnindentMarkdownList",l.Esc=function(e){e.getOption("fullScreen")&&D(a)},this.documentOnKeyDown=function(e){27==(e=e||window.event).keyCode&&a.codemirror.getOption("fullScreen")&&D(a)},document.addEventListener("keydown",this.documentOnKeyDown,!1),o.overlayMode?(r.defineMode("overlay-mode",(function(e){return r.overlayMode(r.getMode(e,!1!==o.spellChecker?"spell-checker":"gfm"),o.overlayMode.mode,o.overlayMode.combine)})),t="overlay-mode",(n=o.parsingConfig).gitHubSpice=!1):((t=o.parsingConfig).name="gfm",t.gitHubSpice=!1),!1!==o.spellChecker&&(t="spell-checker",(n=o.parsingConfig).name="gfm",n.gitHubSpice=!1,"function"==typeof o.spellChecker?o.spellChecker({codeMirrorInstance:r}):i({codeMirrorInstance:r})),this.codemirror=r.fromTextArea(e,{mode:t,backdrop:n,theme:null!=o.theme?o.theme:"easymde",tabSize:null!=o.tabSize?o.tabSize:2,indentUnit:null!=o.tabSize?o.tabSize:2,indentWithTabs:!1!==o.indentWithTabs,lineNumbers:!0===o.lineNumbers,autofocus:!0===o.autofocus,extraKeys:l,direction:o.direction,lineWrapping:!1!==o.lineWrapping,allowDropFileTypes:["text/plain"],placeholder:o.placeholder||e.getAttribute("placeholder")||"",styleSelectedText:null!=o.styleSelectedText?o.styleSelectedText:!c(),scrollbarStyle:null!=o.scrollbarStyle?o.scrollbarStyle:"native",configureMouse:function(e,t,n){return{addNew:!1}},inputStyle:null!=o.inputStyle?o.inputStyle:c()?"contenteditable":"textarea",spellcheck:null==o.nativeSpellcheck||o.nativeSpellcheck,autoRefresh:null!=o.autoRefresh&&o.autoRefresh}),this.codemirror.getScrollerElement().style.minHeight=o.minHeight,void 0!==o.maxHeight&&(this.codemirror.getScrollerElement().style.height=o.maxHeight),!0===o.forceSync){var h=this.codemirror;h.on("change",(function(){h.save()}))}this.gui={};var f=document.createElement("div");f.classList.add("EasyMDEContainer");var p=this.codemirror.getWrapperElement();p.parentNode.insertBefore(f,p),f.appendChild(p),!1!==o.toolbar&&(this.gui.toolbar=this.createToolbar()),!1!==o.status&&(this.gui.statusbar=this.createStatusbar()),null!=o.autosave&&!0===o.autosave.enabled&&(this.autosave(),this.codemirror.on("change",(function(){clearTimeout(a._autosave_timeout),a._autosave_timeout=setTimeout((function(){a.autosave()}),a.options.autosave.submit_delay||a.options.autosave.delay||1e3)})));var m=this;this.codemirror.on("update",(function(){o.previewImagesInEditor&&f.querySelectorAll(".cm-image-marker").forEach((function(e){var t=e.parentElement;if(t.innerText.match(/^!\[.*?\]\(.*\)/g)&&!t.hasAttribute("data-img-src")){var n=t.innerText.match("\\((.*)\\)");if(window.EMDEimagesCache||(window.EMDEimagesCache={}),n&&n.length>=2){var r=n[1];if(window.EMDEimagesCache[r])v(t,window.EMDEimagesCache[r]);else{var i=document.createElement("img");i.onload=function(){window.EMDEimagesCache[r]={naturalWidth:i.naturalWidth,naturalHeight:i.naturalHeight,url:r},v(t,window.EMDEimagesCache[r])},i.src=r}}}}))})),this.gui.sideBySide=this.createSideBySide(),this._rendered=this.element;var g=this.codemirror;setTimeout(function(){g.refresh()}.bind(g),0)}function v(e,t){var n,r;e.setAttribute("data-img-src",t.url),e.setAttribute("style","--bg-image:url("+t.url+");--width:"+t.naturalWidth+"px;--height:"+(n=t.naturalWidth,r=t.naturalHeight,n<window.getComputedStyle(document.querySelector(".CodeMirror-sizer")).width.replace("px","")?r+"px":r/n*100+"%")),m.codemirror.setSize()}},ae.prototype.cleanup=function(){document.removeEventListener("keydown",this.documentOnKeyDown)},ae.prototype.autosave=function(){if(le()){var e=this;if(null==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("EasyMDE: You must set a uniqueId to use the autosave feature");!0!==this.options.autosave.binded&&(null!=e.element.form&&null!=e.element.form&&e.element.form.addEventListener("submit",(function(){clearTimeout(e.autosaveTimeoutId),e.autosaveTimeoutId=void 0,localStorage.removeItem("smde_"+e.options.autosave.uniqueId)})),this.options.autosave.binded=!0),!0!==this.options.autosave.loaded&&("string"==typeof localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&""!=localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&(this.codemirror.setValue(localStorage.getItem("smde_"+this.options.autosave.uniqueId)),this.options.autosave.foundSavedValue=!0),this.options.autosave.loaded=!0);var t=e.value();""!==t?localStorage.setItem("smde_"+this.options.autosave.uniqueId,t):localStorage.removeItem("smde_"+this.options.autosave.uniqueId);var n=document.getElementById("autosaved");if(null!=n&&null!=n&&""!=n){var r=new Date,i=new Intl.DateTimeFormat([this.options.autosave.timeFormat.locale,"en-US"],this.options.autosave.timeFormat.format).format(r),o=null==this.options.autosave.text?"Autosaved: ":this.options.autosave.text;n.innerHTML=o+i}}else console.log("EasyMDE: localStorage not available, cannot autosave")},ae.prototype.clearAutosavedValue=function(){if(le()){if(null==this.options.autosave||null==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("EasyMDE: You must set a uniqueId to clear the autosave value");localStorage.removeItem("smde_"+this.options.autosave.uniqueId)}else console.log("EasyMDE: localStorage not available, cannot autosave")},ae.prototype.openBrowseFileWindow=function(e,t){var n=this,r=this.gui.toolbar.getElementsByClassName("imageInput")[0];r.click(),r.addEventListener("change",(function i(o){n.options.imageUploadFunction?n.uploadImagesUsingCustomFunction(n.options.imageUploadFunction,o.target.files):n.uploadImages(o.target.files,e,t),r.removeEventListener("change",i)}))},ae.prototype.uploadImage=function(e,t,n){var r=this;function i(e){r.updateStatusBar("upload-image",e),setTimeout((function(){r.updateStatusBar("upload-image",r.options.imageTexts.sbInit)}),1e4),n&&"function"==typeof n&&n(e),r.options.errorCallback(e)}function o(t){var n=r.options.imageTexts.sizeUnits.split(",");return t.replace("#image_name#",e.name).replace("#image_size#",K(e.size,n)).replace("#image_max_size#",K(r.options.imageMaxSize,n))}if(t=t||function(e){R(r,e)},e.size>this.options.imageMaxSize)i(o(this.options.errorMessages.fileTooLarge));else{var a=new FormData;a.append("image",e),r.options.imageCSRFToken&&a.append("csrfmiddlewaretoken",r.options.imageCSRFToken);var l=new XMLHttpRequest;l.upload.onprogress=function(t){if(t.lengthComputable){var n=""+Math.round(100*t.loaded/t.total);r.updateStatusBar("upload-image",r.options.imageTexts.sbProgress.replace("#file_name#",e.name).replace("#progress#",n))}},l.open("POST",this.options.imageUploadEndpoint),l.onload=function(){try{var e=JSON.parse(this.responseText)}catch(e){return console.error("EasyMDE: The server did not return a valid json."),void i(o(r.options.errorMessages.importError))}200===this.status&&e&&!e.error&&e.data&&e.data.filePath?t((r.options.imagePathAbsolute?"":window.location.origin+"/")+e.data.filePath):e.error&&e.error in r.options.errorMessages?i(o(r.options.errorMessages[e.error])):e.error?i(o(e.error)):(console.error("EasyMDE: Received an unexpected response after uploading the image."+this.status+" ("+this.statusText+")"),i(o(r.options.errorMessages.importError)))},l.onerror=function(e){console.error("EasyMDE: An unexpected error occurred when trying to upload the image."+e.target.status+" ("+e.target.statusText+")"),i(r.options.errorMessages.importError)},l.send(a)}},ae.prototype.uploadImageUsingCustomFunction=function(e,t){var n=this;e.apply(this,[t,function(e){R(n,e)},function(e){var r=function(e){var r=n.options.imageTexts.sizeUnits.split(",");return e.replace("#image_name#",t.name).replace("#image_size#",K(t.size,r)).replace("#image_max_size#",K(n.options.imageMaxSize,r))}(e);n.updateStatusBar("upload-image",r),setTimeout((function(){n.updateStatusBar("upload-image",n.options.imageTexts.sbInit)}),1e4),n.options.errorCallback(r)}])},ae.prototype.setPreviewMaxHeight=function(){var e=this.codemirror.getWrapperElement(),t=e.nextSibling,n=parseInt(window.getComputedStyle(e).paddingTop),r=parseInt(window.getComputedStyle(e).borderTopWidth),i=(parseInt(this.options.maxHeight)+2*n+2*r).toString()+"px";t.style.height=i},ae.prototype.createSideBySide=function(){var e=this.codemirror,t=e.getWrapperElement(),n=t.nextSibling;if(!n||!/editor-preview-side/.test(n.className)){if((n=document.createElement("div")).className="editor-preview-side",this.options.previewClass)if(Array.isArray(this.options.previewClass))for(var r=0;r<this.options.previewClass.length;r++)n.className+=" "+this.options.previewClass[r];else"string"==typeof this.options.previewClass&&(n.className+=" "+this.options.previewClass);t.parentNode.insertBefore(n,t.nextSibling)}if(void 0!==this.options.maxHeight&&this.setPreviewMaxHeight(),!1===this.options.syncSideBySidePreviewScroll)return n;var i=!1,o=!1;return e.on("scroll",(function(e){if(i)i=!1;else{o=!0;var t=e.getScrollInfo().height-e.getScrollInfo().clientHeight,r=parseFloat(e.getScrollInfo().top)/t,a=(n.scrollHeight-n.clientHeight)*r;n.scrollTop=a}})),n.onscroll=function(){if(o)o=!1;else{i=!0;var t=n.scrollHeight-n.clientHeight,r=parseFloat(n.scrollTop)/t,a=(e.getScrollInfo().height-e.getScrollInfo().clientHeight)*r;e.scrollTo(0,a)}},n},ae.prototype.createToolbar=function(e){if((e=e||this.options.toolbar)&&0!==e.length){var t;for(t=0;t<e.length;t++)null!=J[e[t]]&&(e[t]=J[e[t]]);var n=document.createElement("div");n.className="editor-toolbar";var r=this,i={};for(r.toolbar=e,t=0;t<e.length;t++)if(("guide"!=e[t].name||!1!==r.options.toolbarGuideIcon)&&!(r.options.hideIcons&&-1!=r.options.hideIcons.indexOf(e[t].name)||("fullscreen"==e[t].name||"side-by-side"==e[t].name)&&c())){if("|"===e[t]){for(var o=!1,a=t+1;a<e.length;a++)"|"===e[a]||r.options.hideIcons&&-1!=r.options.hideIcons.indexOf(e[a].name)||(o=!0);if(!o)continue}!function(e){var t;if(t="|"===e?x():e.children?g(e,r.options.toolbarTips,r.options.shortcuts,r):v(e,!0,r.options.toolbarTips,r.options.shortcuts,"button",r),i[e.name||e]=t,n.appendChild(t),"upload-image"===e.name){var o=document.createElement("input");o.className="imageInput",o.type="file",o.multiple=!0,o.name="image",o.accept=r.options.imageAccept,o.style.display="none",o.style.opacity=0,n.appendChild(o)}}(e[t])}r.toolbar_div=n,r.toolbarElements=i;var l=this.codemirror;l.on("cursorActivity",(function(){var e=y(l);for(var t in i)!function(t){var n=i[t];e[t]?n.className+=" active":"fullscreen"!=t&&"side-by-side"!=t&&(n.className=n.className.replace(/\s*active\s*/g,""))}(t)}));var s=l.getWrapperElement();return s.parentNode.insertBefore(n,s),n}},ae.prototype.createStatusbar=function(e){e=e||this.options.status;var t=this.options,n=this.codemirror;if(e&&0!==e.length){var r,i,o,a,l=[];for(r=0;r<e.length;r++)if(i=void 0,o=void 0,a=void 0,"object"==typeof e[r])l.push({className:e[r].className,defaultValue:e[r].defaultValue,onUpdate:e[r].onUpdate,onActivity:e[r].onActivity});else{var s=e[r];"words"===s?(a=function(e){e.innerHTML=Q(n.getValue())},i=function(e){e.innerHTML=Q(n.getValue())}):"lines"===s?(a=function(e){e.innerHTML=n.lineCount()},i=function(e){e.innerHTML=n.lineCount()}):"cursor"===s?(a=function(e){e.innerHTML="1:1"},o=function(e){var t=n.getCursor(),r=t.line+1,i=t.ch+1;e.innerHTML=r+":"+i}):"autosave"===s?a=function(e){null!=t.autosave&&!0===t.autosave.enabled&&e.setAttribute("id","autosaved")}:"upload-image"===s&&(a=function(e){e.innerHTML=t.imageTexts.sbInit}),l.push({className:s,defaultValue:a,onUpdate:i,onActivity:o})}var u=document.createElement("div");for(u.className="editor-statusbar",r=0;r<l.length;r++){var c=l[r],d=document.createElement("span");d.className=c.className,"function"==typeof c.defaultValue&&c.defaultValue(d),"function"==typeof c.onUpdate&&this.codemirror.on("update",function(e,t){return function(){t.onUpdate(e)}}(d,c)),"function"==typeof c.onActivity&&this.codemirror.on("cursorActivity",function(e,t){return function(){t.onActivity(e)}}(d,c)),u.appendChild(d)}var h=this.codemirror.getWrapperElement();return h.parentNode.insertBefore(u,h.nextSibling),u}},ae.prototype.value=function(e){var t=this.codemirror;if(void 0===e)return t.getValue();if(t.getDoc().setValue(e),this.isPreviewActive()){var n=t.getWrapperElement().lastChild;n.innerHTML=this.options.previewRender(e,n)}return this},ae.toggleBold=C,ae.toggleItalic=w,ae.toggleStrikethrough=k,ae.toggleBlockquote=F,ae.toggleHeadingSmaller=A,ae.toggleHeadingBigger=E,ae.toggleHeading1=T,ae.toggleHeading2=L,ae.toggleHeading3=M,ae.toggleCodeBlock=S,ae.toggleUnorderedList=B,ae.toggleOrderedList=N,ae.cleanBlock=O,ae.drawLink=I,ae.drawImage=z,ae.drawUploadedImage=H,ae.drawTable=P,ae.drawHorizontalRule=_,ae.undo=W,ae.redo=j,ae.togglePreview=U,ae.toggleSideBySide=q,ae.toggleFullScreen=D,ae.prototype.toggleBold=function(){C(this)},ae.prototype.toggleItalic=function(){w(this)},ae.prototype.toggleStrikethrough=function(){k(this)},ae.prototype.toggleBlockquote=function(){F(this)},ae.prototype.toggleHeadingSmaller=function(){A(this)},ae.prototype.toggleHeadingBigger=function(){E(this)},ae.prototype.toggleHeading1=function(){T(this)},ae.prototype.toggleHeading2=function(){L(this)},ae.prototype.toggleHeading3=function(){M(this)},ae.prototype.toggleCodeBlock=function(){S(this)},ae.prototype.toggleUnorderedList=function(){B(this)},ae.prototype.toggleOrderedList=function(){N(this)},ae.prototype.cleanBlock=function(){O(this)},ae.prototype.drawLink=function(){I(this)},ae.prototype.drawImage=function(){z(this)},ae.prototype.drawUploadedImage=function(){H(this)},ae.prototype.drawTable=function(){P(this)},ae.prototype.drawHorizontalRule=function(){_(this)},ae.prototype.undo=function(){W(this)},ae.prototype.redo=function(){j(this)},ae.prototype.togglePreview=function(){U(this)},ae.prototype.toggleSideBySide=function(){q(this)},ae.prototype.toggleFullScreen=function(){D(this)},ae.prototype.isPreviewActive=function(){var e=this.codemirror.getWrapperElement().lastChild;return/editor-preview-active/.test(e.className)},ae.prototype.isSideBySideActive=function(){var e=this.codemirror.getWrapperElement().nextSibling;return/editor-preview-active-side/.test(e.className)},ae.prototype.isFullscreenActive=function(){return this.codemirror.getOption("fullScreen")},ae.prototype.getState=function(){return y(this.codemirror)},ae.prototype.toTextArea=function(){var e=this.codemirror,t=e.getWrapperElement(),n=t.parentNode;n&&(this.gui.toolbar&&n.removeChild(this.gui.toolbar),this.gui.statusbar&&n.removeChild(this.gui.statusbar),this.gui.sideBySide&&n.removeChild(this.gui.sideBySide)),n.parentNode.insertBefore(t,n),n.remove(),e.toTextArea(),this.autosaveTimeoutId&&(clearTimeout(this.autosaveTimeoutId),this.autosaveTimeoutId=void 0,this.clearAutosavedValue())},t.exports=ae},{"./codemirror/tablist":17,codemirror:10,"codemirror-spell-checker":2,"codemirror/addon/display/autorefresh.js":3,"codemirror/addon/display/fullscreen.js":4,"codemirror/addon/display/placeholder.js":5,"codemirror/addon/edit/continuelist.js":6,"codemirror/addon/mode/overlay.js":7,"codemirror/addon/search/searchcursor.js":8,"codemirror/addon/selection/mark-selection.js":9,"codemirror/mode/gfm/gfm.js":11,"codemirror/mode/markdown/markdown.js":12,"codemirror/mode/xml/xml.js":14,marked:15}]},{},[18])(18)}));