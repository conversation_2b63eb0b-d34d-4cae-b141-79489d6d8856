<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Login - SubmitPressRelease.org</title>
    <meta name="description" content="Sign in to our platform to manage your existing press releases and publish new ones.">
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.png" />
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700;800&display=swap"
        rel="stylesheet" />
    <link rel="icon" type="image/png" href="<?php echo e(asset('admin/assets/images/faq/favicon_submit_press_release.png')); ?>">
    <link rel="stylesheet" type="text/css" media="screen" href="<?php echo e(asset('admin/assets/css/style.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('css/toastr.min.css')); ?>">
    <link href="<?php echo e(asset('css/sweetalert2.min.css')); ?>" rel="stylesheet">
    <style>
        /* --- Minimal necessary UI improvements --- */
        .btn-gradient {
            background: linear-gradient(90deg, #1968b3 0%, #1968b3 100%);
            color: #fff;
            transition: background .18s ease, transform .08s ease;
        }

        .btn-gradient:hover {
            /* lighter on hover */
            background: linear-gradient(90deg, #539bd8 0%, #539bd8 100%);
            transform: translateY(-1px);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.22);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .txt-red {
            color: #dc3545;
            font-weight: bold;
        }

        /* Use responsive max-width instead of forced fixed width */
        .login-card {
            max-width: 870px;
            width: 94%;
        }

        /* input icon spacing */
        .form-input {
            padding-left: 2.8rem; /* keep room for left icon */
        }

        /* small fix for password show/hide button */
        .password-toggle-btn {
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
        }

        /* responsive tweaks */
        @media (max-width: 640px) {
            .login-card {
                padding: 1rem;
            }
            .login-card img {
                max-width: 220px;
            }
        }
    </style>
</head>

<body x-data="main" class="relative overflow-x-hidden font-nunito text-sm font-normal antialiased"
    :class="[$store.app.sidebar ? 'toggle-sidebar' : '', $store.app.theme === 'dark' || $store.app.isDarkMode ? 'dark' : '',
        $store.app.menu, $store.app.layout, $store.app.rtlClass ]">

    <!-- screen loader -->
    <div class="screen_loader animate__animated fixed inset-0 z-[60] grid place-content-center bg-[#fafafa] dark:bg-[#060818]">
        <!-- loader svg -->
        <svg width="64" height="64" viewBox="0 0 135 135" xmlns="http://www.w3.org/2000/svg" fill="#4361ee">
            <path
                d="M67.447 58c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10zm9.448 9.447c0 5.523 4.477 10 10 10 5.522 0 10-4.477 10-10s-4.478-10-10-10c-5.523 0-10 4.477-10 10zm-9.448 9.448c-5.523 0-10 4.477-10 10 0 5.522 4.477 10 10 10s10-4.478 10-10c0-5.523-4.477-10-10-10zM58 67.447c0-5.523-4.477-10-10-10s-10 4.477-10 10 4.477 10 10 10 10-4.477 10-10z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="-360 67 67" dur="2.5s" repeatCount="indefinite" />
            </path>
            <path
                d="M28.19 40.31c6.627 0 12-5.374 12-12 0-6.628-5.373-12-12-12-6.628 0-12 5.372-12 12 0 6.626 5.372 12 12 12zm30.72-19.825c4.686 4.687 12.284 4.687 16.97 0 4.686-4.686 4.686-12.284 0-16.97-4.686-4.687-12.284-4.687-16.97 0-4.687 4.686-4.687 12.284 0 16.97zm35.74 7.705c0 6.627 5.37 12 12 12 6.626 0 12-5.373 12-12 0-6.628-5.374-12-12-12-6.63 0-12 5.372-12 12zm19.822 30.72c-4.686 4.686-4.686 12.284 0 16.97 4.687 4.686 12.285 4.686 16.97 0 4.687-4.686 4.687-12.284 0-16.97-4.685-4.687-12.283-4.687-16.97 0zm-7.704 35.74c-6.627 0-12 5.37-12 12 0 6.626 5.373 12 12 12s12-5.374 12-12c0-6.63-5.373-12-12-12zm-30.72 19.822c-4.686-4.686-12.284-4.686-16.97 0-4.686 4.687-4.686 12.285 0 16.97 4.686 4.687 12.284 4.687 16.97 0 4.687-4.685 4.687-12.283 0-16.97zm-35.74-7.704c0-6.627-5.372-12-12-12-6.626 0-12 5.373-12 12s5.374 12 12 12c6.628 0 12-5.373 12-12zm-19.823-30.72c4.687-4.686 4.687-12.284 0-16.97-4.686-4.686-12.284-4.686-16.97 0-4.687 4.686-4.687 12.284 0 16.97 4.686 4.687 12.284 4.687 16.97 0z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="360 67 67" dur="8s" repeatCount="indefinite" />
            </path>
        </svg>
    </div>

    <!-- scroll to top button -->
    <div class="fixed bottom-6 right-6 z-50" x-data="scrollToTop">
        <template x-if="showTopButton">
            <button type="button" class="btn btn-outline-primary animate-pulse rounded-full p-2" @click="goToTop" aria-label="Scroll to top">
                <svg width="24" height="24" class="h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M12 20.75C12.4142 20.75 12.75 20.4142 12.75 20L12.75 10.75L11.25 10.75L11.25 20C11.25 20.4142 11.5858 20.75 12 20.75Z" fill="currentColor" />
                    <path d="M6.00002 10.75C5.69667 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75L6.00002 10.75Z" fill="currentColor" />
                </svg>
            </button>
        </template>
    </div>

    <div class="main-container min-h-screen text-black dark:text-white-dark">
        <div x-data="auth">
            <div class="absolute inset-0" style="background-color: #1968B3;"></div>

            <div class="relative flex min-h-screen items-center justify-center bg-cover bg-center bg-no-repeat px-6 py-10 dark:bg-[#060818] sm:px-16">
                <div class="login-card relative w-full rounded-md bg-white p-2 dark:bg-[linear-gradient(52.22deg,#0E1726_0%,rgba(14,23,38,0)_18.66%,rgba(14,23,38,0)_51.04%,rgba(14,23,38,0)_80.07%,#0E1726_100%)] mx-auto">
                    <div class="relative flex flex-col justify-center rounded-md backdrop-blur-lg dark:bg-black/50 py-8 lg:min-h-[400px] glass-effect">
                        <div class="absolute top-6 end-6">
                            <div class="dropdown" x-data="dropdown" @click.outside="open = false">
                                <!-- language dropdown placeholder (kept as original) -->
                            </div>
                        </div>

                        <div class="flex items-center justify-center space-x-4">
                            <!-- Image Element (logo) -->
                            <img src="<?php echo e(asset('admin/assets/images/faq/submit press release logo.png')); ?>" alt="Submit Press Release Logo" style="max-width:300px; height:auto;" />
                        </div>

                        <div class="mx-auto w-full max-w-[440px]">
                            <div class="mb-6">
                                <h1 class="text-3xl font-extrabold uppercase !leading-snug md:text-4xl lg:text-5xl" style="color: #1968b3;">
                                    Sign in
                                </h1>
                                <p class="text-base font-bold leading-normal" style="color: #003b75;">
                                    Enter your email and password to login
                                </p>
                            </div>

                            <form action="<?php echo e(route('login')); ?>" method="POST" class="space-y-5 dark:text-white" novalidate>
                                <?php echo csrf_field(); ?>
                                <div>
                                    <label for="Email">Email</label>
                                    <div class="relative text-white-dark">
                                        <input id="Email" type="email" name="email" placeholder="Enter Email" value="<?php echo e(old('email')); ?>"
                                            class="form-input ps-10 placeholder:text-white-dark" required autocomplete="email" autofocus aria-required="true" />
                                        <span class="absolute start-4 top-1/2 -translate-y-1/2" aria-hidden="true">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                <path opacity="0.5" d="M10.65 2.25H7.35C4.23873 2.25 2.6831 2.25 1.71655 3.23851C0.75 4.22703 0.75 5.81802 0.75 9C0.75 12.182 0.75 13.773 1.71655 14.7615C2.6831 15.75 4.23873 15.75 7.35 15.75H10.65C13.7613 15.75 15.3169 15.75 16.2835 14.7615C17.25 13.773 17.25 12.182 17.25 9C17.25 5.81802 17.25 4.22703 16.2835 3.23851C15.3169 2.25 13.7613 2.25 10.65 2.25Z" fill="currentColor" />
                                                <path d="M14.3465 6.02574C14.609 5.80698 14.6445 5.41681 14.4257 5.15429C14.207 4.89177 13.8168 4.8563 13.5543 5.07507L11.7732 6.55931C11.0035 7.20072 10.4691 7.6446 10.018 7.93476C9.58125 8.21564 9.28509 8.30993 9.00041 8.30993C8.71572 8.30993 8.41956 8.21564 7.98284 7.93476C7.53168 7.6446 6.9973 7.20072 6.22761 6.55931L4.44652 5.07507C4.184 4.8563 3.79384 4.89177 3.57507 5.15429C3.3563 5.41681 3.39177 5.80698 3.65429 6.02574L5.4664 7.53583C6.19764 8.14522 6.79033 8.63914 7.31343 8.97558C7.85834 9.32604 8.38902 9.54743 9.00041 9.54743C9.6118 9.54743 10.1425 9.32604 10.6874 8.97558C11.2105 8.63914 11.8032 8.14522 12.5344 7.53582L14.3465 6.02574Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </div>
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="txt-red" role="alert"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="Password">Password</label>
                                    <div class="relative text-white-dark">
                                        <input id="Password" type="password" name="password" placeholder="Enter Password"
                                            class="form-input ps-10 placeholder:text-white-dark" required autocomplete="current-password" aria-required="true" />
                                        <span class="absolute start-4 top-1/2 -translate-y-1/2" aria-hidden="true">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                <path opacity="0.5" d="M1.5 12C1.5 9.87868 1.5 8.81802 2.15901 8.15901C2.81802 7.5 3.87868 7.5 6 7.5H12C14.1213 7.5 15.182 7.5 15.841 8.15901C16.5 8.81802 16.5 9.87868 16.5 12C16.5 14.1213 16.5 15.182 15.841 15.841C15.182 16.5 14.1213 16.5 12 16.5H6C3.87868 16.5 2.81802 16.5 2.15901 15.841C1.5 15.182 1.5 14.1213 1.5 12Z" fill="currentColor" />
                                                <path d="M6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 11.5858 6.41421 11.25 6 11.25C5.58579 11.25 5.25 11.5858 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75Z" fill="currentColor" />
                                                <path d="M9 12.75C9.41421 12.75 9.75 12.4142 9.75 12C9.75 11.5858 9.41421 11.25 9 11.25C8.58579 11.25 8.25 11.5858 8.25 12C8.25 12.4142 8.58579 12.75 9 12.75Z" fill="currentColor" />
                                            </svg>
                                        </span>

                                        <!-- password show/hide button -->
                                        <div class="absolute end-3 top-1/2 -translate-y-1/2 flex items-center">
                                            <button type="button" class="password-toggle-btn" id="togglePassword" aria-label="Show or hide password">
                                                <!-- simple eye icon -->
                                                <svg id="eyeOpen" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display:inline;">
                                                    <path d="M12 5C7 5 3.032 8.11 1 12c2.032 3.89 6 7 11 7s8.968-3.11 11-7c-2.032-3.89-6-7-11-7z" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <svg id="eyeClosed" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display:none;">
                                                    <path d="M17.94 17.94A10.94 10.94 0 0 1 12 19c-5 0-8.968-3.11-11-7a10.97 10.97 0 0 1 4.44-5.06" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M1 1l22 22" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <span class="flex justify-end mt-3">
                                        <a href="<?php echo e(route('password.request')); ?>" class="underline">Forgot your password?</a>
                                    </span>

                                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="txt-red" role="alert"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="flex items-center justify-between">
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox" name="remember" class="form-checkbox" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                        <span class="text-sm">Remember me</span>
                                    </label>
                                </div>

                                <!-- Cloudflare Turnstile -->
                                <div class="cf-turnstile" data-sitekey="<?php echo e(env('TURNSTILE_SITE_KEY')); ?>"></div>

                                <button type="submit" class="btn btn-gradient !mt-6 w-full border-0 uppercase shadow-[0_10px_20px_-10px_rgba(67,97,238,0.44)] mb-3" style="padding: .75rem 1rem;">
                                    Sign in
                                </button>

                                <div class="text-sm">
                                    <span>Don't have an account? </span>
                                    <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        href="<?php echo e(route('register')); ?>">
                                        <?php echo e(__('Sign Up')); ?>

                                    </a>
                                </div>
                            </form>
                        </div> <!-- /.form container -->
                    </div> <!-- /.card inner -->
                </div> <!-- /.login-card -->
            </div> <!-- /.outer -->
        </div>
    </div>

    <!-- Cloudflare Turnstile -->
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>

    <!-- Existing scripts (kept as original) -->
    <script src="<?php echo e(asset('admin/assets/js/alpine-collaspe.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/assets/js/alpine-persist.min.js')); ?>"></script>
    <script defer src="<?php echo e(asset('admin/assets/js/alpine-ui.min.js')); ?>"></script>
    <script defer src="<?php echo e(asset('admin/assets/js/alpine-focus.min.js')); ?>"></script>
    <script defer src="<?php echo e(asset('admin/assets/js/alpine.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/assets/js/custom.js')); ?>"></script>

    <script src="<?php echo e(asset('js/jquery/jquery-3.6.0.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/toastr.min.js')); ?>"></script>

    <script>
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
    </script>

    <script>
        $(document).ready(function() {
            <?php if(Session::has('success-toast')): ?>
                toastr.success("<?php echo e(Session::get('success-toast')); ?>");
            <?php endif; ?>

            <?php if(Session::has('error-toast')): ?>
                toastr.error("<?php echo e(Session::get('error-toast')); ?>");
            <?php endif; ?>

            <?php if(Session::has('info-toast')): ?>
                toastr.info("<?php echo e(Session::get('info-toast')); ?>");
            <?php endif; ?>

            <?php if(Session::has('warning-toast')): ?>
                toastr.warning("<?php echo e(Session::get('warning-toast')); ?>");
            <?php endif; ?>
        });
    </script>

    <script>
        // main section for Alpine bits (kept minimal - your original Alpine components can remain)
        document.addEventListener('alpine:init', () => {
            Alpine.data('scrollToTop', () => ({
                showTopButton: false,
                init() {
                    window.onscroll = () => {
                        this.scrollFunction();
                    };
                },
                scrollFunction() {
                    if (document.body.scrollTop > 50 || document.documentElement.scrollTop > 50) {
                        this.showTopButton = true;
                    } else {
                        this.showTopButton = false;
                    }
                },
                goToTop() {
                    document.body.scrollTop = 0;
                    document.documentElement.scrollTop = 0;
                },
            }));
        });
    </script>

    <!-- Small JS for password show/hide (minimal and safe) -->
    <script>
        (function() {
            const pwd = document.getElementById('Password');
            const toggle = document.getElementById('togglePassword');
            const eyeOpen = document.getElementById('eyeOpen');
            const eyeClosed = document.getElementById('eyeClosed');

            if (!pwd || !toggle) return;

            toggle.addEventListener('click', function() {
                const type = pwd.getAttribute('type') === 'password' ? 'text' : 'password';
                pwd.setAttribute('type', type);

                if (type === 'text') {
                    eyeOpen.style.display = 'none';
                    eyeClosed.style.display = 'inline';
                } else {
                    eyeOpen.style.display = 'inline';
                    eyeClosed.style.display = 'none';
                }
                // keep focus on password
                pwd.focus();
            });
        })();
    </script>

</body>

</html>
<?php /**PATH E:\VNProjects\Laravel\Press-Release\resources\views/auth/login.blade.php ENDPATH**/ ?>