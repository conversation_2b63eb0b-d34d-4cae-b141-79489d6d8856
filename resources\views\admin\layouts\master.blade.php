<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') | {{ env('APP_NAME','Press Release') }}</title>
<meta name="description" content="@yield('meta_description', 'Get instant access to our free app: login or signup to manage your press releases and easily submit press release in just a few clicks.')">


    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="{{asset('css/tailwind/tailwind.min.css')}}" rel="stylesheet">

    <link rel="icon" type="image/png" href="{{ asset('admin/assets/images/faq/favicon_submit_press_release.png') }}">
    <script src="{{asset('js/jquery/jquery-3.6.0.min.js')}}"></script>


    <link href="{{asset('css/simple-datatables.css')}}" rel="stylesheet" type="text/css">

    {{-- <script src="https://cdn.jsdelivr.net/npm/pusher-js@8.4.0/dist/pusher.min.js"></script> --}}
    <link rel="stylesheet" type="text/css" href="{{asset('admin/assets/css/quill.snow.css')}}" />
    <script src="{{asset('admin/assets/js/quill.js')}}"></script>
    <script src="{{asset('admin/assets/js/nice-select.js')}}"></script>

    <script src="{{asset('js/pdf-lib.min.js')}}"></script>




    <link rel="stylesheet" href="{{asset('flatpickr/flatpickr.min.css')}}">



    <script src="//unpkg.com/alpinejs" defer></script>
    @include('admin.includes.header')

    @yield('extra-css')

</head>

<body x-data="main" class="relative overflow-x-hidden font-nunito text-sm font-normal antialiased"
    :class="[$store.app.sidebar ? 'toggle-sidebar' : '', $store.app.theme === 'dark' || $store.app.isDarkMode ? 'dark' : '',
        $store.app.menu, $store.app.layout, $store.app.rtlClass
    ]">
    <!-- sidebar menu overlay -->
    <div x-cloak class="fixed inset-0 z-50 bg-[black]/60 lg:hidden" :class="{ 'hidden': !$store.app.sidebar }"
        @click="$store.app.toggleSidebar()"></div>

    <!-- screen loader -->
    @include('admin.includes.loader')

    <!-- scroll to top button -->
    @include('admin.includes.scroll')

    <!-- start theme customizer section -->
    <div x-data="customizer">
        <div class="fixed inset-0 z-[51] hidden bg-[black]/60 px-4 transition-[display]"
            :class="{ '!block': showCustomizer }" @click="showCustomizer = false"></div>

        {{-- @include('admin.includes.theme_customize') --}}
    </div>
    <!-- end theme customizer section -->

    <div class="main-container min-h-screen text-black" :class="[$store.app.navbar]">
        <!-- start sidebar section -->
        <div :class="{ 'dark text-white-dark': $store.app.semidark }">
            @include('admin.includes.sidebar')
        </div>

        <!-- end sidebar section -->

        <div class="main-content flex min-h-screen flex-col">
            <!-- start header section -->
            @include('admin.includes.navbar')
            <!-- end header section -->

            <div class="animate__animated p-6" :class="[$store.app.animation]">
                <!-- start main content section -->
                @yield('main-section')
                <!-- end main content section -->
            </div>

            <!-- start footer section -->
            @include('admin.includes.footer')
            <!-- end footer section -->
        </div>
    </div>

    @include('admin.includes.script')

    @include('admin.includes.toast')

@yield('extra-script')

</body>

</html>
