<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Free App – Signup & Submit Press Release</title>
    <meta name="description" content="Get instant access to our free app: signup to manage your press releases and easily submit press release in just a few clicks. 100% free!" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.png" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link rel="icon" type="image/png" href="{{ asset('admin/assets/images/faq/favicon_submit_press_release.png') }}">
    <link rel="stylesheet" type="text/css" media="screen" href="{{ asset('admin/assets/css/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('css/toastr.min.css') }}">
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('admin/assets/css/tailwind.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<style>
    :root {
        --primary-color: #28a745; /* Green */
        --primary-dark: #1e7e34; /* Dark Green */
        --primary-light: #34d058; /* Light Green */
        --accent-color: #5cb85c; /* Accent Green */
        --success-color: #28a745; /* Success Green */
        --danger-color: #dc3545; /* Danger Red */
        --text-primary: #212529; /* Dark Text */
        --text-secondary: #6c757d; /* Secondary Text */
        --background-gradient: linear-gradient(135deg, #28a745 0%, #34d058 100%); /* Green Gradient */
        --card-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        --input-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', 'Poppins', sans-serif;
        background: var(--background-gradient);
        min-height: 100vh;
        overflow-x: hidden;
    }

    .animated-background {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: -1;
        background: linear-gradient(-45deg, #28a745, #34d058, #1e7e34, #5cb85c);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }

    @keyframes gradientBG {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -1;
    }

    .shape {
        position: absolute;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
        top: 15%;
        left: 15%;
        width: 100px;
        height: 100px;
        background: white;
        border-radius: 50%;
        animation-delay: 0s;
    }

    .shape:nth-child(2) {
        top: 60%;
        left: 85%;
        width: 140px;
        height: 140px;
        background: white;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        animation-delay: 2s;
    }

    .shape:nth-child(3) {
        top: 30%;
        right: 5%;
        width: 80px;
        height: 80px;
        background: white;
        transform: rotate(45deg);
        animation-delay: 4s;
    }

    .shape:nth-child(4) {
        top: 75%;
        left: 5%;
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 20%;
        animation-delay: 1s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .register-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 2rem;
        position: relative;
    }

    .register-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: var(--card-shadow);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        max-width: 550px;
        width: 100%;
        position: relative;
        animation: slideInUp 0.8s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .register-header {
        text-align: center;
        padding: 3rem 2rem 1rem;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(92, 184, 92, 0.1) 100%);
    }

    .logo-container {
        margin-bottom: 2rem;
        position: relative;
    }

    .logo-container::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(circle, rgba(25, 104, 179, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        z-index: -1;
    }

    .logo {
        max-width: 180px;
        height: auto;
        transition: var(--transition);
        filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.1));
    }

    .logo:hover {
        transform: scale(1.05);
    }

    .register-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        letter-spacing: -0.02em;
    }

    .register-subtitle {
        font-size: 1rem;
        color: var(--text-secondary);
        font-weight: 500;
        margin-bottom: 2rem;
    }

    .register-form {
        padding: 0 2rem 3rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.9rem;
        letter-spacing: 0.025em;
    }

    .form-input-container {
        position: relative;
    }

    .form-input {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e1e8ed;
        border-radius: 12px;
        font-size: 1rem;
        transition: var(--transition);
        background: white;
        font-family: inherit;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: var(--input-shadow);
        transform: translateY(-1px);
    }

    .form-input::placeholder {
        color: #a0a7b4;
        font-weight: 400;
    }

    .form-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        transition: var(--transition);
        z-index: 2;
    }

    .form-input:focus + .form-icon {
        color: var(--primary-color);
    }

    .error-message {
        color: var(--danger-color);
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .password-feedback {
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
    }

    .password-feedback.success {
        color: var(--success-color);
    }

    .password-feedback.danger {
        color: var(--danger-color);
    }

    .turnstile-container {
        margin: 1.5rem 0;
        display: flex;
        justify-content: center;
    }

    .register-button {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.025em;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        margin-top: 1rem;
        text-transform: uppercase;
    }

    .register-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    }

    .register-button:active {
        transform: translateY(0);
    }

    .login-link {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e1e8ed;
        color: var(--text-secondary);
        font-size: 0.95rem;
    }

    .login-link a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: var(--transition);
    }

    .login-link a:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    .scroll-to-top {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .scroll-to-top:hover {
        background: var(--primary-dark);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .register-container {
            padding: 1rem;
        }

        .register-card {
            margin: 1rem;
            border-radius: 16px;
        }

        .register-header {
            padding: 2rem 1.5rem 1rem;
        }

        .register-title {
            font-size: 2rem;
        }

        .register-form {
            padding: 0 1.5rem 2rem;
        }

        .logo {
            max-width: 140px;
        }
    }

    @media (max-width: 480px) {
        .register-title {
            font-size: 1.75rem;
        }

        .register-subtitle {
            font-size: 0.9rem;
        }

        .form-input {
            padding: 0.875rem 0.875rem 0.875rem 2.75rem;
            font-size: 0.95rem;
        }

        .form-icon {
            left: 0.875rem;
        }
    }

    /* Loading Animation */
    .screen_loader {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .screen_loader svg {
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }
</style>

<body x-data="main" class="relative overflow-x-hidden font-nunito text-sm font-normal antialiased"
    :class="[$store.app.sidebar ? 'toggle-sidebar' : '', $store.app.theme === 'dark' || $store.app.isDarkMode ? 'dark' : '',
        $store.app.menu, $store.app.layout, $store.app.rtlClass
    ]">

    <!-- Animated Background -->
    <div class="animated-background"></div>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Screen Loader -->
    <div class="screen_loader animate__animated fixed inset-0 z-[60] grid place-content-center bg-[#fafafa] dark:bg-[#060818]">
        <svg width="64" height="64" viewBox="0 0 135 135" xmlns="http://www.w3.org/2000/svg" fill="#4361ee">
            <path d="M67.447 58c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10zm9.448 9.447c0 5.523 4.477 10 10 10 5.522 0 10-4.477 10-10s-4.478-10-10-10c-5.523 0-10 4.477-10 10zm-9.448 9.448c-5.523 0-10 4.477-10 10 0 5.522 4.477 10 10 10s10-4.478 10-10c0-5.523-4.477-10-10-10zM58 67.447c0-5.523-4.477-10-10-10s-10 4.477-10 10 4.477 10 10 10 10-4.477 10-10z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="-360 67 67" dur="2.5s" repeatCount="indefinite" />
            </path>
            <path d="M28.19 40.31c6.627 0 12-5.374 12-12 0-6.628-5.373-12-12-12-6.628 0-12 5.372-12 12 0 6.626 5.372 12 12 12zm30.72-19.825c4.686 4.687 12.284 4.687 16.97 0 4.686-4.686 4.686-12.284 0-16.97-4.686-4.687-12.284-4.687-16.97 0-4.687 4.686-4.687 12.284 0 16.97zm35.74 7.705c0 6.627 5.37 12 12 12 6.626 0 12-5.373 12-12 0-6.628-5.374-12-12-12-6.63 0-12 5.372-12 12zm19.822 30.72c-4.686 4.686-4.686 12.284 0 16.97 4.687 4.686 12.285 4.686 16.97 0 4.687-4.686 4.687-12.284 0-16.97-4.685-4.687-12.283-4.687-16.97 0zm-7.704 35.74c-6.627 0-12 5.37-12 12 0 6.626 5.373 12 12 12s12-5.374 12-12c0-6.63-5.373-12-12-12zm-30.72 19.822c-4.686-4.686-12.284-4.686-16.97 0-4.686 4.687-4.686 12.285 0 16.97 4.686 4.687 12.284 4.687 16.97 0 4.687-4.685 4.687-12.283 0-16.97zm-35.74-7.704c0-6.627-5.372-12-12-12-6.626 0-12 5.373-12 12s5.374 12 12 12c6.628 0 12-5.373 12-12zm-19.823-30.72c4.687-4.686 4.687-12.284 0-16.97-4.686-4.686-12.284-4.686-16.97 0-4.687 4.686-4.687 12.284 0 16.97 4.686 4.687 12.284 4.687 16.97 0z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="360 67 67" dur="8s" repeatCount="indefinite" />
            </path>
        </svg>
    </div>

    <!-- Scroll to Top Button -->
    <div x-data="scrollToTop">
        <template x-if="showTopButton">
            <button type="button" class="scroll-to-top" @click="goToTop">
                <i class="fas fa-chevron-up"></i>
            </button>
        </template>
    </div>

    <!-- Main Container -->
    <div class="main-container min-h-screen text-black dark:text-white-dark">
        <div x-data="auth">
            <div class="register-container">
                <div class="register-card">
                    <!-- Register Header -->
                    <div class="register-header">
                        <div class="logo-container">
                            <img src="{{ asset('admin/assets/images/faq/submit press release logo.png') }}"
                                 alt="SubmitPressRelease.org Logo"
                                 class="logo" />
                        </div>
                        <h1 class="register-title">Join Us Today</h1>
                        <p class="register-subtitle">Create your free account and start managing press releases</p>
                    </div>

                    <!-- Register Form -->
                    <div class="register-form">
                        <form action="{{ route('register') }}" method="POST">
                            @csrf

                            <!-- Email Field -->
                            <div class="form-group">
                                <label for="Email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email Address
                                </label>
                                <div class="form-input-container">
                                    <input id="Email"
                                           type="email"
                                           name="email"
                                           placeholder="Enter your email address"
                                           value="{{ old('email') }}"
                                           class="form-input"
                                           required />
                                    <div class="form-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                </div>
                                @error('email')
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Password Field -->
                            <div class="form-group">
                                <label for="Password" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <div class="form-input-container">
                                    <input id="Password"
                                           type="password"
                                           name="password"
                                           placeholder="Create a strong password"
                                           class="form-input"
                                           oninput="checkPassword()"
                                           required />
                                    <div class="form-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                                <div id="password-feedback" class="password-feedback danger">
                                    <i class="fas fa-info-circle"></i>
                                    Password must be at least 8 characters, include 1 number, 1 special character
                                </div>
                                @error('password')
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Confirm Password Field -->
                            <div class="form-group">
                                <label for="password_confirmation" class="form-label">
                                    <i class="fas fa-lock"></i> Confirm Password
                                </label>
                                <div class="form-input-container">
                                    <input id="password_confirmation"
                                           type="password"
                                           name="password_confirmation"
                                           placeholder="Confirm your password"
                                           class="form-input"
                                           oninput="checkPasswordMatch()"
                                           required />
                                    <div class="form-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                                <div id="confirm-password-feedback" class="password-feedback danger" style="display: none;">
                                    <i class="fas fa-times-circle"></i>
                                    Passwords do not match
                                </div>
                                @error('password_confirmation')
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Turnstile -->
                            <div class="turnstile-container">
                                <div class="cf-turnstile" data-sitekey="{{ env('TURNSTILE_SITE_KEY') }}"></div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="register-button">
                                <i class="fas fa-user-plus"></i> Create Account
                            </button>

                            <!-- Login Link -->
                            <div class="login-link">
                                Already have an account?
                                <a href="{{ route('login') }}">
                                    <i class="fas fa-sign-in-alt"></i> Sign In
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>

    <script src="{{ asset('admin/assets/js/alpine-collaspe.min.js') }}"></script>
    <script src="{{ asset('admin/assets/js/alpine-persist.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine-ui.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine-focus.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine.min.js') }}"></script>
    <script src="{{ asset('admin/assets/js/custom.js') }}"></script>

    <script src="{{ asset('js/jquery/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ asset('js/toastr.min.js') }}"></script>
    <script>
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
    </script>
    <script>
        $(document).ready(function() {
            @if (Session::has('success-toast'))
                toastr.success("{{ Session::get('success-toast') }}");
            @endif

            @if (Session::has('error-toast'))
                toastr.error("{{ Session::get('error-toast') }}");
            @endif

            @if (Session::has('info-toast'))
                toastr.info("{{ Session::get('info-toast') }}");
            @endif

            @if (Session::has('warning-toast'))
                toastr.warning("{{ Session::get('warning-toast') }}");
            @endif
        });
    </script>


    <script>
        // main section
        document.addEventListener('alpine:init', () => {
            Alpine.data('scrollToTop', () => ({
                showTopButton: false,
                init() {
                    window.onscroll = () => {
                        this.scrollFunction();
                    };
                },

                scrollFunction() {
                    if (document.body.scrollTop > 50 || document.documentElement.scrollTop > 50) {
                        this.showTopButton = true;
                    } else {
                        this.showTopButton = false;
                    }
                },

                goToTop() {
                    document.body.scrollTop = 0;
                    document.documentElement.scrollTop = 0;
                },
            }));

        });
    </script>




    <script>
        function checkPassword() {
            const password = document.getElementById("Password").value;
            const feedback = document.getElementById("password-feedback");

            const isStrong = /^(?=.*[0-9])(?=.*[\W_]).{8,}$/.test(password);

            if (isStrong) {
                feedback.innerHTML = '<i class="fas fa-check-circle"></i> Strong password ✅';
                feedback.className = 'password-feedback success';
            } else {
                feedback.innerHTML = '<i class="fas fa-info-circle"></i> Password must be at least 8 characters, include 1 number, 1 special character';
                feedback.className = 'password-feedback danger';
            }
        }

        function checkPasswordMatch() {
            const password = document.getElementById("Password").value;
            const confirmPassword = document.getElementById("password_confirmation").value;
            const feedback = document.getElementById("confirm-password-feedback");

            if (confirmPassword.length === 0) {
                feedback.style.display = 'none';
                return;
            }

            if (password === confirmPassword) {
                feedback.innerHTML = '<i class="fas fa-check-circle"></i> Passwords match ✅';
                feedback.className = 'password-feedback success';
                feedback.style.display = 'flex';
            } else {
                feedback.innerHTML = '<i class="fas fa-times-circle"></i> Passwords do not match';
                feedback.className = 'password-feedback danger';
                feedback.style.display = 'flex';
            }
        }
    </script>


</body>

</html>
