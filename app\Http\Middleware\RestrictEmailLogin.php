<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class RestrictEmailLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    protected $popularDomains = [
        'gmail.com',
        'yahoo.com',
        'outlook.com',
        'hotmail.com',
        'live.com'
    ];

    public function handle(Request $request, Closure $next)
    {
        $email = $request->input('email');
        if (!$email) {
            return $next($request);
        }

        $domain = strtolower(substr(strrchr($email, "@"), 1));

        // 1. Block temp emails
        $tempDomains = file(storage_path('temp_email_domains.txt'), FILE_IGNORE_NEW_LINES);
        if (in_array($domain, $tempDomains)) {
            return back()->withErrors(['email' => 'Temporary email addresses are not allowed.']);
        }

        // 2. Allow if it's a known popular domain (gmail, yahoo, etc.)
        // if (in_array($domain, $this->popularDomains)) {
        //     return $next($request);
        // }

        // 3. For custom domains: only allow if no other user exists with that domain
        $existingUserWithDomain = User::whereRaw("SUBSTRING_INDEX(email, '@', -1) = ?", [$domain])->exists();
        $currentEmailUser = User::where('email', $email)->exists();

        if ($existingUserWithDomain && !$currentEmailUser) {
            return back()->withErrors(['email' => 'Only one user is allowed with this domain.']);
        }

        return $next($request);
    }
}
