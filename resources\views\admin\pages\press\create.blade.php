@extends('admin.layouts.master')
@section('title', 'Create Press Release')
@section('main-section')
    <link rel="stylesheet" href="{{ asset('flatepicker/flatpickr.min.css') }}">
    <script src="{{ asset('js/flatpickr.min.js') }}"></script>

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>
                        <a href="{{ route('press-releases.index') }}" class="clr hover:underline">Press Release</a>
                    </li>
                    <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
                        <span>Create</span>
                    </li>
                </ul>

            </div>
            <div class="mt-3" x-data="form">


                <div class="mb-5">

                    <div class="panel">
                        <div class="mb-4">
                            <label class="block text-base font-semibold mb-1">
                                Please ensure that the topic of your press release does not appear on our list of restricted
                                topics and that it follows our content requirements; otherwise, it will be rejected, and
                                will still count as a submission.
                                <br>
                                <button type="button" onclick="openGuidelinesModal()"
                                    class="font-bold text-blue-600 underline focus:outline-none">View Submission
                                    Guidelines</button>
                            </label>
                        </div>
                        <!-- Modal Markup -->
                       <div id="guidelinesModal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-60"></div>
    <div class="panel bg-white rounded-lg shadow-lg w-full max-w-3xl p-6 relative z-10 border border-gray-200 overflow-y-auto max-h-screen">
        <h2 class="text-2xl font-bold mb-4">Press Release Submission Guidelines</h2>

        <h3 class="text-lg font-semibold mb-2">Content Requirements</h3>
        <ul class="list-disc pl-6 mb-4 text-gray-700 text-sm space-y-1">
            <li>Must read as newsworthy and objective.</li>
            <li>Should neither sound overly promotional nor negative.</li>
            <li>May not contain “reviews.”</li>
            <li>Must not allege illegal or unethical conduct, breach of contract, or threaten litigation.</li>
        </ul>

        <h3 class="text-lg font-semibold mb-2">Prohibited Topics</h3>
        <p class="text-sm text-gray-700 mb-2">Submissions that touch on any of these topics beyond a brief mention will be rejected.</p>
        <ul class="list-disc pl-6 text-gray-700 text-sm space-y-1">
            <li><strong>Drugs & Substances</strong> – Recreational or psychotropic compounds (e.g., cannabis, kratom, THC).</li>
            <li><strong>Finance & Trading Schemes</strong> – Unverified or unregulated offerings (e.g., crypto tokens, NFTs, DeFi platforms, FOREX signals, cloud-mining services, “get-rich-quick” investment programs).</li>
            <li><strong>Weapons & Violence</strong> – Explosives, firearms, weapons tutorials, graphic violence or gore (e.g., bomb-making instructions, firearm assembly guides).</li>
            <li><strong>Gambling & Betting</strong> – Games of chance or wagering (e.g., online casinos, sports betting platforms, poker sites).</li>
            <li><strong>Adult Content</strong> – Nudity, sexual or erotic material, content involving minors (e.g., pornography sites, escort services).</li>
            <li><strong>Hate & Extremism</strong> – Hate speech, extremist ideology, or targeted discrimination (e.g., white-supremacist manifestos, neo-Nazi propaganda).</li>
            <li><strong>Defamation & Privacy Violations</strong> – Unverified damaging allegations or sharing personal/private information (e.g., publishing someone’s address, false claims of misconduct).</li>
            <li><strong>Self-Harm & Suicide</strong> – Encouragement or instruction for self-injury (e.g., methods of self-harm, tips for suicide).</li>
            <li><strong>Medical & Legal Advice</strong> – Diagnosis, treatment recommendations, or contractual guidance (e.g., prescribing medication, drafting legal agreements).</li>
            <li><strong>Scams & Fraud</strong> – Payday loans, bail bonds, pyramid schemes, identity-theft services, credit-repair scams (e.g., debt-consolidation “secret” strategies).</li>
            <li><strong>Affiliate/Spam Links</strong> – Affiliate marketing “hoplinks,” social-media like-buying services, malware-flagged URLs (e.g., pay-per-click link shorteners).</li>
            <li><strong>Cosmetic & Body Modification</strong> – Cosmetic procedures, ED treatments, breast enhancement, massage parlors (e.g., lip-fillers, non-surgical nose jobs).</li>
            <li><strong>Pseudo-Religious & Occult</strong> – Amulets, charms, magic rituals (e.g., love-spell kits, tarot-reading services).</li>
            <li><strong>Software Piracy & Cybercrime</strong> – Cracking/keygens, hacking guides, reverse-engineering, malware/ransomware instructions, phishing or social-engineering tutorials (e.g., distributing Windows activation cracks).</li>
            <li><strong>Inauthentic Social-Media Services</strong> – Paid likes, followers, views, engagements, or “pods” (e.g., Instagram follower-boosting services).</li>
            <li><strong>Copyright Infringement & Pirated Content</strong> – Unauthorized distribution of copyrighted works (e.g., torrent sites for movies, e-book piracy portals).</li>
        </ul>

        <button type="button" onclick="closeGuidelinesModal()"
            class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl font-bold">&times;</button>

        <div class="flex justify-end mt-6">
            <button type="button" onclick="closeGuidelinesModal()" class="btn ifl">Close</button>
        </div>
    </div>
</div>

                        <div class="mb-5">
                            <form action="{{ route('press-releases.store') }}" method="POST" class="space-y-5"
                                enctype="multipart/form-data">
                                @csrf
                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <div>
                                        <label class="required-field" for="browserFname">Name</label>
                                        <input id="browserFname" type="text" name="name" value="{{ old('name') }}"
                                            placeholder="Enter name" class="form-input" />
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">Email</label>
                                        <input id="browserFname" type="email" name="email" value="{{ old('email') }}"
                                            placeholder="Enter email" class="form-input" />
                                        @error('email')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="" for="browserFname">PR Agency</label>
                                        <input id="browserFname" type="text" name="pr_agency"
                                            value="{{ old('pr_agency') }}" placeholder="Enter PR Agency"
                                            class="form-input" />
                                        @error('pr_agency')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="" for="browserFname">Telephone</label>
                                        <input id="browserFname" type="number" name="telephone"
                                            value="{{ old('telephone') }}" placeholder="Enter Telephone"
                                            class="form-input" />
                                        @error('telephone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div>
                                        <label class="required-field" for="browserFname">Company Name</label>
                                        <input id="browserFname" type="text" name="company_name"
                                            value="{{ old('company_name') }}" placeholder="Enter Company Name"
                                            class="form-input" />
                                        @error('company_name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>


                                    <div>
                                        <label class="required-field" for="category">Category</label>
                                        <select id="category" name="category" class="form-input dropdown">
                                            <option value="">Select category</option>
                                            <option value="News" {{ old('category') == 'News' ? 'selected' : '' }}>News
                                            </option>
                                            <option value="Events" {{ old('category') == 'Events' ? 'selected' : '' }}>
                                                Events</option>
                                            <option value="Announcements"
                                                {{ old('category') == 'Announcements' ? 'selected' : '' }}>
                                                Announcements</option>
                                        </select>
                                        @error('category')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field" for="browserFname">Press Release Title </label>
                                        <input id="browserFname" type="text" name="press_release_title"
                                            value="{{ old('press_release_title') }}"
                                            placeholder="Enter Press Release Title" class="form-input" />
                                        @error('press_release_title')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field block text-sm font-medium "
                                            for="image">Image</label>

                                        <input type="file" name="image" id="image" accept="image/*"
                                            class="rtl:file-ml-5 form-input p-0 file:border-0 file:/90 file:py-2 file:px-4 file:font-semibold file: file:hover: ltr:file:mr-5 form-input my-image-field @error('image') border-red-500 @enderror"
                                            onchange="previewImage(event)">


                                        @error('image')
                                            <span class="text-red-500 text-sm">{{ $message }}</span>
                                        @enderror

                                        <!-- Image Preview -->
                                        <div class="mt-2">
                                            <img id="imagePreview" src="" alt="Image Preview"
                                                class="hidden w-32 h-32 object-cover">
                                        </div>
                                    </div>

                                </div>
                                <div class="form-group">
                                    <label class="required-field" for="editor">Press Release Description </label>
                                    <textarea id="editor" class="tinymce" name="press_release_description">{{ old('press_release_description') }}</textarea>
                                </div>
                                <span id="editor-error" class="text-danger"></span>
                                @error('press_release_description')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                                <div class="flex justify-end mt-4">
                                    <a href="{{ route('press-releases.index') }}" class="ifl-cancel btn !mt-6">Cancel</a>

                                    <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>

                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

        </div>

    @endsection

    @section('extra-script')
        <!-- Select2 CSS -->
        <link href="{{ asset('js/select2/select2.min.css') }}" rel="stylesheet" />
        <!-- jQuery (Select2 requires jQuery) -->
        <script src="{{ asset('js/jquery/jquery.min.js') }}"></script>
        <script>
            $(document).ready(function() {
                $('.dropdown').select2();
            });
        </script>

        <!-- Modal open/close logic -->
        <script>
            function openGuidelinesModal() {
                document.getElementById('guidelinesModal').classList.remove('hidden');
            }

            function closeGuidelinesModal() {
                document.getElementById('guidelinesModal').classList.add('hidden');
            }
        </script>

        <script>
            function previewImage(event) {
                const file = event.target.files[0];
                const preview = document.getElementById('imagePreview');

                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('hidden');
                    }

                    reader.readAsDataURL(file);
                } else {
                    preview.src = '';
                    preview.classList.add('hidden');
                }
            }
        </script>





        <script>
            tinymce.init({
                selector: 'textarea.tinymce',
                height: 300,
                menubar: false,
                plugins: 'link lists paste',
                toolbar: 'undo redo | bold italic | blocks | bullist numlist | link',
                block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6',

                paste_as_text: false, // ✅ allow rich content with formatting

                // ✅ Allow only safe and useful tags
                valid_elements: 'p,h1,h2,h3,strong,em,ul,ol,li,a[href],b,i,u,blockquote',

                branding: false,
                forced_root_block: 'p',

                // ✅ Remove paste_preprocess that strips all HTML
                // paste_preprocess: function(plugin, args) {
                //     args.content = args.content.replace(/<[^>]*>?/gm, '');
                // },

                setup: function(editor) {
                    // Prevent inserting more than 2 links manually
                    editor.on('BeforeExecCommand', function(e) {
                        if (e.command === 'mceInsertLink') {
                            const links = editor.getDoc().querySelectorAll('a');
                            if (links.length >= 2) {
                                e.preventDefault();
                                alert("Only 2 links are allowed.");
                            }
                        }
                    });

                    // Prevent pasting more than 2 links
                    editor.on('PastePreProcess', function(e) {
                        const pasted = e.content;
                        const existingLinks = editor.getDoc().querySelectorAll('a').length;
                        const newLinks = (pasted.match(/<a\s+(.*?)>/g) || []).length;

                        if (existingLinks + newLinks > 2) {
                            e.preventDefault();
                            alert("You can't paste content that adds more than 2 links.");
                        }
                    });
                }
            });

            // Sync TinyMCE content before form submit
            document.querySelector('form').addEventListener('submit', function() {
                tinymce.triggerSave();
            });
        </script>



        <script>
            const compressImage = async (file, {
                quality = 1,
                type = file.type
            }) => {
                const imageBitmap = await createImageBitmap(file);

                const canvas = document.createElement('canvas');
                canvas.width = imageBitmap.width;
                canvas.height = imageBitmap.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(imageBitmap, 0, 0);

                const blob = await new Promise((resolve) =>
                    canvas.toBlob(resolve, type, quality)
                );

                return new File([blob], file.name, {
                    type: blob.type,
                });
            };

            const input = document.querySelector('.my-image-field');
            input.addEventListener('change', async (e) => {
                const {
                    files
                } = e.target;

                if (!files.length) return;

                const dataTransfer = new DataTransfer();

                for (const file of files) {
                    if (!file.type.startsWith('image')) {
                        dataTransfer.items.add(file);
                        continue;
                    }

                    const compressedFile = await compressImage(file, {
                        quality: 0.5,
                        type: 'image/jpeg',
                    });

                    dataTransfer.items.add(compressedFile);
                }

                e.target.files = dataTransfer.files;
            });
        </script>

    @endsection
