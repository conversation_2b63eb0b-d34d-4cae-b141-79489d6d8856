table.dataTable-table thead tr th,
table.dataTable-table tfoot tr th {
  @apply font-bold;
}

table.table-bordered thead tr th,
table.table-bordered tbody tr td {
  @apply !border !border-solid !border-[#f6f7fa] dark:!border-[#191e3a];
}

table.table-compact thead tr th,
table.table-compact tbody tr td {
  @apply !py-2 !px-3;
}

.sticky-header table thead {
  @apply sticky top-0 z-10;
}

.sticky-header .dataTable-container {
  @apply h-[500px];
}

.sticky-column table thead tr th:first-child,
.sticky-column table tbody tr td:first-child {
  @apply sticky z-[8] bg-[#f6f8fa] ltr:left-0 rtl:right-0 dark:bg-[#1a2941];
}
.sticky-column table thead tr th:nth-child(2),
.sticky-column table tbody tr td:nth-child(2) {
  @apply sticky z-[8] bg-[#f6f8fa] ltr:left-[58px] rtl:right-[58px] dark:bg-[#1a2941];
}

.sticky-header.sticky-column table thead tr th:first-child,
.sticky-header.sticky-column table thead tr th:nth-child(2) {
  @apply top-0;
}

.dataTable-container {
  @apply overflow-auto;
}
.dataTable-top,
.dataTable-container {
  @apply mb-5;
}
.dataTable-top,
.dataTable-bottom {
  @apply flex flex-wrap items-center gap-4 font-medium;
}

/* pagination */
.dataTable-pagination {
  @apply ltr:ml-auto rtl:mr-auto;
}

.dataTable-pagination ul {
  @apply flex items-center;
}

.dataTable-pagination a {
  @apply grid h-9 w-9 place-content-center rounded-full bg-white-light text-dark hover:!bg-primary hover:!text-white ltr:ml-1 rtl:mr-1 dark:bg-[#191e3a] dark:text-white-light;
}

.dataTable-pagination .active a {
  @apply cursor-default bg-primary text-white;
}
/* sorting */

.dataTable-sorter {
  @apply relative inline-block h-full w-max ltr:pr-7 ltr:before:right-1 ltr:after:right-1 rtl:pl-7 rtl:before:left-1 rtl:after:left-1;
}

.dataTable-sorter::before,
.dataTable-sorter::after {
  content: '';
  height: 0;
  width: 0;
  position: absolute;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  opacity: 0.2;
}

.dataTable-sorter::before {
  border-top: 5px solid #0e1726;
  bottom: 4px;
}
.dataTable-sorter::after {
  border-bottom: 5px solid #0e1726;
  border-top: 5px solid transparent;
  top: -2px;
}

.dark .dataTable-sorter::after {
  border-bottom-color: #ffffff;
}
.dark .dataTable-sorter::before {
  border-top-color: #ffffff;
}

.asc .dataTable-sorter::after {
  @apply border-b-primary opacity-100;
}

.desc .dataTable-sorter::before {
  @apply border-t-primary opacity-100;
}

/* search, info, pagesize */
/* .dataTable-info,
.dataTable-dropdown,
.dataTable-pagination {
  padding: 6px 0;
} */

.ltr .dataTable-search {
  margin-left: auto;
}

.rtl .dataTable-search {
  margin-right: auto;
}

.dataTable-dropdown .dataTable-selector,
.dataTable-search .dataTable-input {
  width: auto;
  background-color: #fff;
  color: #0e1726;
  border-color: #e0e6ed;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  border-width: 1px;
  outline: none !important;
}

.dataTable-dropdown .dataTable-selector:focus,
.dataTable-search .dataTable-input:focus {
  border-color: #4361ee;
}

.dataTable-search .dataTable-input {
  box-shadow: none !important;
}

.dataTable-dropdown .dataTable-selector {
  @apply py-1.5 ltr:pl-2 ltr:pr-4 rtl:pr-2 rtl:pl-4;
}

.dark .dataTable-dropdown .dataTable-selector,
.dark .dataTable-search .dataTable-input {
  background-color: #121e32;
  color: #888ea8;
  border-color: #17263c;
}
.dataTable-dropdown label {
  margin-bottom: 0;
}

@media screen and (max-width: 767px) {
  .dataTable-bottom {
    flex-direction: column;
  }
  .ltr .dataTable-pagination {
    margin-left: 0;
  }
  .rtl .dataTable-pagination {
    margin-right: 0;
  }
}

/* invoice list datatable */
.invoice-table .dataTable-wrapper .dataTable-top,
.invoice-table .dataTable-wrapper .dataTable-bottom {
  @apply px-5;
}

.no-number-pagination ul.dataTable-pagination-list li {
  @apply hidden;
}

.no-number-pagination ul.dataTable-pagination-list li.pager {
  @apply !block;
}

.next-prev-pagination .dataTable-pagination a {
  @apply w-max rounded-md border border-primary bg-transparent px-5 py-2 text-primary dark:border-primary dark:bg-transparent dark:text-primary;
}

.next-prev-pagination .dataTable-bottom {
  @apply flex-col justify-center;
}

.next-prev-pagination .dataTable-pagination {
  @apply ltr:ml-0 rtl:mr-0;
}
