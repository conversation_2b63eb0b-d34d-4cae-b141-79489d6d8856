<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Login - SubmitPressRelease.org</title>
<meta name="description" content="Sign in to our platform to manage your existing press releases and publish new ones.">
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.png" />
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700;800&display=swap"
        rel="stylesheet" />
    <link rel="icon" type="image/png" href="{{ asset('admin/assets/images/faq/favicon_submit_press_release.png') }}">

    <link rel="stylesheet" type="text/css" media="screen" href="{{ asset('admin/assets/css/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('css/toastr.min.css') }}">
    {{-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> --}}
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">





</head>
<style>
    .btn-gradient:hover {
        background: #1968b3;
    }

    .glass-effect {
        background: rgba(255, 255, 255, 0.22);
        border-radius: 16px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .txt-red {
        color: #dc3545;
        /* Bootstrap danger color for errors */
        /* background-color: rgba(220, 53, 69, 0.2); */
        font-weight: bold;
        /* Make the text bold */
    }

    .login-card {
        width: 650px !important;
    }
</style>

<body x-data="main" class="relative overflow-x-hidden font-nunito text-sm font-normal antialiased"
    :class="[$store.app.sidebar ? 'toggle-sidebar' : '', $store.app.theme === 'dark' || $store.app.isDarkMode ? 'dark' : '',
        $store.app.menu, $store.app.layout, $store.app.rtlClass
    ]">
    <!-- screen loader -->
    <div
        class="screen_loader animate__animated fixed inset-0 z-[60] grid place-content-center bg-[#fafafa] dark:bg-[#060818]">
        <svg width="64" height="64" viewBox="0 0 135 135" xmlns="http://www.w3.org/2000/svg" fill="#4361ee">
            <path
                d="M67.447 58c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10zm9.448 9.447c0 5.523 4.477 10 10 10 5.522 0 10-4.477 10-10s-4.478-10-10-10c-5.523 0-10 4.477-10 10zm-9.448 9.448c-5.523 0-10 4.477-10 10 0 5.522 4.477 10 10 10s10-4.478 10-10c0-5.523-4.477-10-10-10zM58 67.447c0-5.523-4.477-10-10-10s-10 4.477-10 10 4.477 10 10 10 10-4.477 10-10z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="-360 67 67" dur="2.5s"
                    repeatCount="indefinite" />
            </path>
            <path
                d="M28.19 40.31c6.627 0 12-5.374 12-12 0-6.628-5.373-12-12-12-6.628 0-12 5.372-12 12 0 6.626 5.372 12 12 12zm30.72-19.825c4.686 4.687 12.284 4.687 16.97 0 4.686-4.686 4.686-12.284 0-16.97-4.686-4.687-12.284-4.687-16.97 0-4.687 4.686-4.687 12.284 0 16.97zm35.74 7.705c0 6.627 5.37 12 12 12 6.626 0 12-5.373 12-12 0-6.628-5.374-12-12-12-6.63 0-12 5.372-12 12zm19.822 30.72c-4.686 4.686-4.686 12.284 0 16.97 4.687 4.686 12.285 4.686 16.97 0 4.687-4.686 4.687-12.284 0-16.97-4.685-4.687-12.283-4.687-16.97 0zm-7.704 35.74c-6.627 0-12 5.37-12 12 0 6.626 5.373 12 12 12s12-5.374 12-12c0-6.63-5.373-12-12-12zm-30.72 19.822c-4.686-4.686-12.284-4.686-16.97 0-4.686 4.687-4.686 12.285 0 16.97 4.686 4.687 12.284 4.687 16.97 0 4.687-4.685 4.687-12.283 0-16.97zm-35.74-7.704c0-6.627-5.372-12-12-12-6.626 0-12 5.373-12 12s5.374 12 12 12c6.628 0 12-5.373 12-12zm-19.823-30.72c4.687-4.686 4.687-12.284 0-16.97-4.686-4.686-12.284-4.686-16.97 0-4.687 4.686-4.687 12.284 0 16.97 4.686 4.687 12.284 4.687 16.97 0z">
                <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="360 67 67" dur="8s"
                    repeatCount="indefinite" />
            </path>
        </svg>
    </div>


    <!-- scroll to top button -->
    <div class="fixed bottom-6 right-6 z-50" x-data="scrollToTop">
        <template x-if="showTopButton">
            <button type="button" class="btn btn-outline-primary animate-pulse rounded-full p-2" @click="goToTop">
                <svg width="24" height="24" class="h-4 w-4" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd"
                        d="M12 20.75C12.4142 20.75 12.75 20.4142 12.75 20L12.75 10.75L11.25 10.75L11.25 20C11.25 20.4142 11.5858 20.75 12 20.75Z"
                        fill="currentColor" />
                    <path
                        d="M6.00002 10.75C5.69667 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75L6.00002 10.75Z"
                        fill="currentColor" />
                </svg>
            </button>
        </template>
    </div>

    <div class="main-container min-h-screen text-black dark:text-white-dark">
        <div x-data="auth">
            <div class="absolute inset-0" style="background-color: #1968B3;">
                <!-- The image has been removed -->
            </div>



            <div
                class="relative flex min-h-screen items-center justify-center  bg-cover bg-center bg-no-repeat px-6 py-10 dark:bg-[#060818] sm:px-16">
                {{-- <img src="{{ asset('admin/assets/images/auth/coming-soon-object1.png') }}" alt="image"
                    class="absolute left-0 top-1/2 h-full max-h-[893px] -translate-y-1/2" />
                <img src="{{ asset('admin/assets/images/auth/coming-soon-object2.png') }}" alt="image"
                    class="absolute left-24 top-0 h-40 md:left-[30%]" />
                <img src="{{ asset('admin/assets/images/auth/coming-soon-object3.png') }}" alt="image"
                    class="absolute right-0 top-0 h-[300px]" />
                <img src="{{ asset('admin/assets/images/auth/polygon-object.svg') }}" alt="image"
                    class="absolute bottom-0 end-[28%]" /> --}}
                <div
                    class="login-card relative w-full max-w-[870px] rounded-md bg-white p-2 dark:bg-[linear-gradient(52.22deg,#0E1726_0%,rgba(14,23,38,0)_18.66%,rgba(14,23,38,0)_51.04%,rgba(14,23,38,0)_80.07%,#0E1726_100%)]">
                    <div
                        class="relative flex flex-col justify-center rounded-md backdrop-blur-lg dark:bg-black/50 py-8 lg:min-h-[400px] mx-auto glass-effect">
                        <div class="absolute top-6 end-6">
                            <div class="dropdown" x-data="dropdown" @click.outside="open = false">
                                {{-- <a href="javascript:;"
                                    class="flex items-center gap-2.5 rounded-lg border border-white-dark/30 bg-white px-2 py-1.5 text-white-dark hover:border-primary hover:text-primary dark:bg-black"
                                    :class="{ '!border-primary !text-primary': open }" @click="toggle">
                                    <div>
                                        <img :src="{{ asset('admin/assets/images/flags/${$store.app.locale.toUpperCase()}.svg') }}"
                                            alt="image" class="h-5 w-5 rounded-full object-cover" />
                                    </div>
                                    <div x-text="$store.app.locale" class="text-base font-bold uppercase"></div>
                                    <span class="shrink-0" :class="{ 'rotate-180': open }">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.99989 9.79988C6.59156 9.79988 6.18322 9.64238 5.87406 9.33321L2.07072 5.52988C1.90156 5.36071 1.90156 5.08071 2.07072 4.91154C2.23989 4.74238 2.51989 4.74238 2.68906 4.91154L6.49239 8.71488C6.77239 8.99488 7.22739 8.99488 7.50739 8.71488L11.3107 4.91154C11.4799 4.74238 11.7599 4.74238 11.9291 4.91154C12.0982 5.08071 12.0982 5.36071 11.9291 5.52988L8.12572 9.33321C7.81656 9.64238 7.40822 9.79988 6.99989 9.79988Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                </a> --}}
                                <ul x-cloak x-show="open" x-transition x-transition.duration.300ms
                                    class="top-11 grid w-[280px] grid-cols-2 gap-y-2 !px-2 font-semibold text-dark ltr:-right-14 rtl:-left-14 dark:text-white-dark dark:text-white-light/90 sm:ltr:-right-2 sm:rtl:-left-2">
                                    <template x-for="item in languages">
                                        <li>
                                            <a href="javascript:;" class="hover:text-primary"
                                                @click="$store.app.toggleLocale(item.value),toggle()"
                                                :class="{ 'bg-primary/10 text-primary': $store.app.locale == item.value }">
                                                <img class="h-5 w-5 rounded-full object-cover"
                                                    :src="assets / images / flags / $ { item.value.toUpperCase() }.svg"
                                                    alt="image" />
                                                <span class="ltr:ml-3 rtl:mr-3" x-text="item.key"></span>
                                            </a>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>

                        <div class="flex items-center justify-center space-x-4">
                            <!-- Image Element -->
                            <img src="{{ asset('admin/assets/images/faq/submit press release logo.png') }}" alt="Logo"
                                width="300px" height="300px" />
                        </div>


                        <div class="mx-auto w-full max-w-[440px]">
                            <div class="mb-10">
                                <h1 class="text-3xl font-extrabold uppercase !leading-snug md:text-4xl lg:text-5xl"
                                    style="color: #1968b3;">
                                    Sign in
                                </h1>



                                <p class="text-base font-bold leading-normal style="color: #003b75;">Enter your email
                                    and
                                    password to login</p>
                            </div>
                            <form action="{{ route('login') }}" method="POST" class="space-y-5 dark:text-white">
                                @csrf
                                <div>
                                    <label for="Email">Email</label>
                                    <div class="relative text-white-dark">
                                        <input id="Email" type="email" name="email" placeholder="Enter Email"
                                            value="{{ old('email') }}"
                                            class="form-input ps-10 placeholder:text-white-dark" />

                                        <span class="absolute start-4 top-1/2 -translate-y-1/2">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                <path opacity="0.5"
                                                    d="M10.65 2.25H7.35C4.23873 2.25 2.6831 2.25 1.71655 3.23851C0.75 4.22703 0.75 5.81802 0.75 9C0.75 12.182 0.75 13.773 1.71655 14.7615C2.6831 15.75 4.23873 15.75 7.35 15.75H10.65C13.7613 15.75 15.3169 15.75 16.2835 14.7615C17.25 13.773 17.25 12.182 17.25 9C17.25 5.81802 17.25 4.22703 16.2835 3.23851C15.3169 2.25 13.7613 2.25 10.65 2.25Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M14.3465 6.02574C14.609 5.80698 14.6445 5.41681 14.4257 5.15429C14.207 4.89177 13.8168 4.8563 13.5543 5.07507L11.7732 6.55931C11.0035 7.20072 10.4691 7.6446 10.018 7.93476C9.58125 8.21564 9.28509 8.30993 9.00041 8.30993C8.71572 8.30993 8.41956 8.21564 7.98284 7.93476C7.53168 7.6446 6.9973 7.20072 6.22761 6.55931L4.44652 5.07507C4.184 4.8563 3.79384 4.89177 3.57507 5.15429C3.3563 5.41681 3.39177 5.80698 3.65429 6.02574L5.4664 7.53583C6.19764 8.14522 6.79033 8.63914 7.31343 8.97558C7.85834 9.32604 8.38902 9.54743 9.00041 9.54743C9.6118 9.54743 10.1425 9.32604 10.6874 8.97558C11.2105 8.63914 11.8032 8.14522 12.5344 7.53582L14.3465 6.02574Z"
                                                    fill="currentColor" />
                                            </svg>
                                        </span>

                                    </div>
                                    @error('email')
                                        <span class="txt-red">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div>
                                    <label for="Password">Password</label>
                                    <div class="relative text-white-dark">
                                        <input id="Password" type="password" name="password"
                                            placeholder="Enter Password"
                                            class="form-input ps-10 placeholder:text-white-dark" />
                                        <span class="absolute start-4 top-1/2 -translate-y-1/2">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                <path opacity="0.5"
                                                    d="M1.5 12C1.5 9.87868 1.5 8.81802 2.15901 8.15901C2.81802 7.5 3.87868 7.5 6 7.5H12C14.1213 7.5 15.182 7.5 15.841 8.15901C16.5 8.81802 16.5 9.87868 16.5 12C16.5 14.1213 16.5 15.182 15.841 15.841C15.182 16.5 14.1213 16.5 12 16.5H6C3.87868 16.5 2.81802 16.5 2.15901 15.841C1.5 15.182 1.5 14.1213 1.5 12Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 11.5858 6.41421 11.25 6 11.25C5.58579 11.25 5.25 11.5858 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M9 12.75C9.41421 12.75 9.75 12.4142 9.75 12C9.75 11.5858 9.41421 11.25 9 11.25C8.58579 11.25 8.25 11.5858 8.25 12C8.25 12.4142 8.58579 12.75 9 12.75Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M12.75 12C12.75 12.4142 12.4142 12.75 12 12.75C11.5858 12.75 11.25 12.4142 11.25 12C11.25 11.5858 11.5858 11.25 12 11.25C12.4142 11.25 12.75 11.5858 12.75 12Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M5.0625 6C5.0625 3.82538 6.82538 2.0625 9 2.0625C11.1746 2.0625 12.9375 3.82538 12.9375 6V7.50268C13.363 7.50665 13.7351 7.51651 14.0625 7.54096V6C14.0625 3.20406 11.7959 0.9375 9 0.9375C6.20406 0.9375 3.9375 3.20406 3.9375 6V7.54096C4.26488 7.51651 4.63698 7.50665 5.0625 7.50268V6Z"
                                                    fill="currentColor" />
                                            </svg>
                                        </span>
                                    </div>
                                    <span class="flex justify-end mt-3">
                                        <a href="{{ route('password.request') }}" class="underline">Forgot your
                                            password?</a>
                                    </span>

                                    @error('password')
                                        <span class="txt-red">{{ $message }}</span>
                                    @enderror
                                </div>
                                   <div class="cf-turnstile" data-sitekey="{{ env('TURNSTILE_SITE_KEY') }}"></div>

                                <button type="submit"
                                    class="btn btn-gradient !mt-6 w-full border-0 uppercase shadow-[0_10px_20px_-10px_rgba(67,97,238,0.44)] mb-3"
                                    style="background: linear-gradient(90deg, #1968b3 0%, #1968b3 50%, #1968b3 100%);"
                                    onmouseover="this.style.background='linear-gradient(90deg, #539bd8 0%, #539bd8 50%, #539bd8 100%)'"
                                    onmouseout="this.style.background='linear-gradient(90deg, #1968b3 0%, #1968b3 50%, #1968b3 100%)'">
                                    Sign in
                                </button>
                                <span>Don't have an account? </span>
                                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    href="{{ route('register') }}">
                                    {{ __('Sign Up') }}
                                </a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>


    <script src="{{ asset('admin/assets/js/alpine-collaspe.min.js') }}"></script>
    <script src="{{ asset('admin/assets/js/alpine-persist.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine-ui.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine-focus.min.js') }}"></script>
    <script defer src="{{ asset('admin/assets/js/alpine.min.js') }}"></script>
    <script src="{{ asset('admin/assets/js/custom.js') }}"></script>

    <script src="{{ asset('js/jquery/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ asset('js/toastr.min.js') }}"></script>
    <script>
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
    </script>
    <script>
        $(document).ready(function() {
            @if (Session::has('success-toast'))
                toastr.success("{{ Session::get('success-toast') }}");
            @endif

            @if (Session::has('error-toast'))
                toastr.error("{{ Session::get('error-toast') }}");
            @endif

            @if (Session::has('info-toast'))
                toastr.info("{{ Session::get('info-toast') }}");
            @endif

            @if (Session::has('warning-toast'))
                toastr.warning("{{ Session::get('warning-toast') }}");
            @endif
        });
    </script>


    <script>
        // main section
        document.addEventListener('alpine:init', () => {
            Alpine.data('scrollToTop', () => ({
                showTopButton: false,
                init() {
                    window.onscroll = () => {
                        this.scrollFunction();
                    };
                },

                scrollFunction() {
                    if (document.body.scrollTop > 50 || document.documentElement.scrollTop > 50) {
                        this.showTopButton = true;
                    } else {
                        this.showTopButton = false;
                    }
                },

                goToTop() {
                    document.body.scrollTop = 0;
                    document.documentElement.scrollTop = 0;
                },
            }));

        });
    </script>




</body>

</html>
