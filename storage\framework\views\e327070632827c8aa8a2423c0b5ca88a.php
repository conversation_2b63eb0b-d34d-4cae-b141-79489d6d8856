<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e($title ?? config('app.name', 'Press Release Pro')); ?></title>
        <meta name="description" content="<?php echo e($description ?? 'Professional press release distribution platform for businesses and organizations worldwide.'); ?>">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="<?php echo e(asset('admin/assets/images/faq/favicon_submit_press_release.png')); ?>">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

        <!-- Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Cloudflare Turnstile -->
        <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>

        <!-- Additional Styles -->
        <style>
            .auth-bg {
                background: linear-gradient(135deg, #1968b3 0%, #003b75 100%);
                position: relative;
                overflow: hidden;
            }

            .auth-bg::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
                pointer-events: none;
            }

            .floating-shapes {
                position: absolute;
                width: 100%;
                height: 100%;
                overflow: hidden;
                pointer-events: none;
            }

            .floating-shape {
                position: absolute;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
            }

            .floating-shape:nth-child(1) {
                width: 80px;
                height: 80px;
                top: 20%;
                left: 10%;
                animation-delay: 0s;
            }

            .floating-shape:nth-child(2) {
                width: 120px;
                height: 120px;
                top: 60%;
                right: 15%;
                animation-delay: 2s;
            }

            .floating-shape:nth-child(3) {
                width: 60px;
                height: 60px;
                bottom: 20%;
                left: 20%;
                animation-delay: 4s;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                33% { transform: translateY(-20px) rotate(120deg); }
                66% { transform: translateY(10px) rotate(240deg); }
            }

            .auth-card {
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            }

            .logo-glow {
                filter: drop-shadow(0 0 20px rgba(25, 104, 179, 0.3));
            }
        </style>
    </head>
    <body class="font-inter antialiased">
        <div class="min-h-screen auth-bg flex items-center justify-center p-4">
            <!-- Floating Background Shapes -->
            <div class="floating-shapes">
                <div class="floating-shape"></div>
                <div class="floating-shape"></div>
                <div class="floating-shape"></div>
            </div>

            <!-- Main Content -->
            <div class="relative w-full max-w-md">
                <!-- Logo Section -->
                <div class="text-center mb-8">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white rounded-2xl shadow-brand mb-4 logo-glow">
                        <img src="<?php echo e(asset('admin/assets/images/faq/submit press release logo.png')); ?>"
                             alt="Press Release Pro"
                             class="w-12 h-12 object-contain">
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2 font-playfair">Press Release Pro</h1>
                    <p class="text-white/80 text-sm">Professional Press Release Distribution</p>
                </div>

                <!-- Auth Card -->
                <div class="auth-card rounded-2xl p-8 animate-fade-in-up">
                    <?php echo e($slot); ?>

                </div>

                <!-- Footer Links -->
                <div class="text-center mt-6 space-y-2">
                    <div class="flex justify-center space-x-6 text-sm text-white/70">
                        <a href="#" class="hover:text-white transition-colors duration-200">Privacy Policy</a>
                        <a href="#" class="hover:text-white transition-colors duration-200">Terms of Service</a>
                        <a href="#" class="hover:text-white transition-colors duration-200">Help</a>
                    </div>
                    <p class="text-xs text-white/50">© <?php echo e(date('Y')); ?> Press Release Pro. All rights reserved.</p>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

        <!-- Scripts -->
        <script src="<?php echo e(asset('js/jquery/jquery-3.6.0.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/toastr.min.js')); ?>"></script>
        <link rel="stylesheet" href="<?php echo e(asset('css/toastr.min.css')); ?>">

        <script>
            // Toast Configuration
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": false,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };

            // Display Laravel session messages
            $(document).ready(function() {
                <?php if(Session::has('success-toast')): ?>
                    toastr.success("<?php echo e(Session::get('success-toast')); ?>");
                <?php endif; ?>

                <?php if(Session::has('error-toast')): ?>
                    toastr.error("<?php echo e(Session::get('error-toast')); ?>");
                <?php endif; ?>

                <?php if(Session::has('info-toast')): ?>
                    toastr.info("<?php echo e(Session::get('info-toast')); ?>");
                <?php endif; ?>

                <?php if(Session::has('warning-toast')): ?>
                    toastr.warning("<?php echo e(Session::get('warning-toast')); ?>");
                <?php endif; ?>
            });

            // Form validation helpers
            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                const errorElement = field.parentNode.querySelector('.form-error') || document.createElement('span');
                errorElement.className = 'form-error';
                errorElement.textContent = message;

                if (!field.parentNode.querySelector('.form-error')) {
                    field.parentNode.appendChild(errorElement);
                }

                field.classList.add('form-input-error');
            }

            function clearFieldError(fieldId) {
                const field = document.getElementById(fieldId);
                const errorElement = field.parentNode.querySelector('.form-error');

                if (errorElement) {
                    errorElement.remove();
                }

                field.classList.remove('form-input-error');
            }

            // Real-time validation
            document.addEventListener('DOMContentLoaded', function() {
                const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');

                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.type === 'email' && this.value) {
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test(this.value)) {
                                showFieldError(this.id, 'Please enter a valid email address');
                            } else {
                                clearFieldError(this.id);
                            }
                        }
                    });

                    input.addEventListener('input', function() {
                        if (this.classList.contains('form-input-error')) {
                            clearFieldError(this.id);
                        }
                    });
                });
            });
        </script>

        <?php echo $__env->yieldPushContent('scripts'); ?>
    </body>
</html>
<?php /**PATH E:\VNProjects\Laravel\Press-Release\resources\views/layouts/guest.blade.php ENDPATH**/ ?>