@extends('admin.layouts.master')
@section('title', 'Create Contact Us')
@section('main-section')
    <link rel="stylesheet" href="{{ asset('flatepicker/flatpickr.min.css') }}">
    <script src="{{ asset('js/flatpickr.min.js') }}"></script>

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>
                        <a href="" class="clr hover:underline">Contact Us</a>
                    </li>
                    <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
                        <span>Create</span>
                    </li>
                </ul>

            </div>
            <div class="mt-3" x-data="form">


                <div class="mb-5">

                    <div class="panel">

                        <div class="mb-5">
                            <form action="{{ route('contact.store') }}" method="POST" class="space-y-5"
                                enctype="multipart/form-data">
                                @csrf
                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <div>
                                        <label class="required-field" for="browserFname">Name</label>
                                        <input id="browserFname" type="text" name="name" value="{{ $contacts->name }}"
                                            placeholder="Enter name" class="form-input" />
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field" for="browserFemail">Email</label>
                                        <input id="browserFemail" type="email" name="email"
                                            value="{{ $contacts->email }}" placeholder="Enter email" class="form-input" />
                                        @error('email')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="address">Address</label>
                                        <input id="address" type="text" name="address"
                                            value="{{ $contacts->address }}" placeholder="Enter address" class="form-input" />
                                        @error('address')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="phone">Phone</label>
                                        <input id="phone" type="number" name="phone"
                                            value="{{ $contacts->phone }}" placeholder="Enter phone" class="form-input" />
                                        @error('phone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="whatsapp">WhatsApp</label>
                                        <input id="whatsapp" type="text" name="whatsapp"
                                            value="{{ $contacts->whatsapp }}" placeholder="Enter whatsapp" class="form-input" />
                                        @error('whatsapp')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="facebook">Facebook</label>
                                        <input id="facebook" type="text" name="facebook"
                                            value="{{ $contacts->facebook }}" placeholder="Enter facebook" class="form-input" />
                                        @error('facebook')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="instagram">Instagram</label>
                                        <input id="instagram" type="text" name="instagram"
                                            value="{{ $contacts->instagram }}" placeholder="Enter instagram" class="form-input" />
                                        @error('instagram')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                     <div>
                                        <label class="required-field" for="linkedin">Linkedin</label>
                                        <input id="linkedin" type="text" name="linkedin"
                                            value="{{ $contacts->linkedin }}" placeholder="Enter linkedin" class="form-input" />
                                        @error('linkedin')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                    <div class="flex justify-end mt-4">
                                        {{-- <a href="{{ route('contact.index') }}" class="ifl-cancel btn !mt-6">Cancel</a> --}}

                                        <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>

                                    </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    @endsection

    @section('extra-script')
        <!-- Select2 CSS -->
        <link href="{{ asset('js/select2/select2.min.css') }}" rel="stylesheet" />
        <!-- jQuery (Select2 requires jQuery) -->
        <script src="{{ asset('js/jquery/jquery.min.js') }}"></script>
        <script>
            $(document).ready(function() {
                $('.dropdown').select2();
            });
        </script>

        <script>
            function previewImage(event) {
                const file = event.target.files[0];
                const preview = document.getElementById('imagePreview');

                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('hidden');
                    }

                    reader.readAsDataURL(file);
                } else {
                    preview.src = '';
                    preview.classList.add('hidden');
                }
            }
        </script>




        <script type="text/javascript">
            tinymce.init({
                selector: 'textarea.tinymce',
                height: 300,
                menubar: false,
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat |',
                content_css: '//www.tiny.cloud/css/codepen.min.css'
            });
        </script>
        <script>
            const compressImage = async (file, {
                quality = 1,
                type = file.type
            }) => {
                const imageBitmap = await createImageBitmap(file);

                const canvas = document.createElement('canvas');
                canvas.width = imageBitmap.width;
                canvas.height = imageBitmap.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(imageBitmap, 0, 0);

                const blob = await new Promise((resolve) =>
                    canvas.toBlob(resolve, type, quality)
                );

                return new File([blob], file.name, {
                    type: blob.type,
                });
            };

            const input = document.querySelector('.my-image-field');
            input.addEventListener('change', async (e) => {
                const {
                    files
                } = e.target;

                if (!files.length) return;

                const dataTransfer = new DataTransfer();

                for (const file of files) {
                    if (!file.type.startsWith('image')) {
                        dataTransfer.items.add(file);
                        continue;
                    }

                    const compressedFile = await compressImage(file, {
                        quality: 0.5,
                        type: 'image/jpeg',
                    });

                    dataTransfer.items.add(compressedFile);
                }

                e.target.files = dataTransfer.files;
            });
        </script>

    @endsection
