@extends('admin.layouts.master')
@section('title', 'Dashboard')
@section('main-section')
@section('extra-css')
    <style>
        .ifl {
            color: white !important;
            background-color: #1968b3 !important;
        }

        .active-category {
            background-color: #1968b3 !important;
            color: white !important;
            border-color: #1968b3 !important;
        }
    </style>
@endsection

<div class="animate__animated p-6" :class="[$store.app.animation]">
    <!-- start main content section -->
    <div x-data="sales">
        <ul class="flex space-x-2 rtl:space-x-reverse">
            <li>
                <a href="{{ route('dashboard') }}" class="text-primary hover:underline">Dashboard</a>
            </li>

        </ul>
        <div class="pt-5">
            <div class="mb-6 grid gap-6">
                <div class="panel h-full xl:col-span-2" style="">
                    <div class="mb-5 flex items-center dark:text-white-light">
  @php
    $isAdmin = auth()->user()->role === 'admin'; // ya role_id === 1
@endphp

<div class="mb-6 grid grid-cols-1 gap-6 text-white sm:grid-cols-2 xl:{{ $isAdmin ? 'grid-cols-5' : 'grid-cols-4' }} xl:w-full">

                            {{-- users  --}}
                            @if(auth()->user()->role == 'admin')

                            <div class="panel bg-gradient-to-r from-indigo-500 to-indigo-400">
                                <div class="flex justify-between">
                                    <div class="text-md font-semibold ltr:mr-1 rtl:ml-1">Total Users</div>

                                </div>
                                <div class="mt-5 flex items-center">
                                    <div class="text-3xl font-bold ltr:mr-3 rtl:ml-3">{{ $totalUsers }}</div>
                                    {{-- <div class="badge bg-white/30">{{$userPercentage}}%</div> --}}
                                </div>
                                <div class="mt-5 flex items-center font-semibold">

                                    {{-- Last Week {{ $lastWeekUsers }} --}}
                                </div>
                            </div>
                            @endif

                            <div class="panel bg-gradient-to-r from-cyan-500 to-cyan-400">
                                <div class="flex justify-between">
                                    <div class="text-md font-semibold ltr:mr-1 rtl:ml-1">Total PR</div>

                                </div>
                                <div class="mt-5 flex items-center">
                                    <div class="text-3xl font-bold ltr:mr-3 rtl:ml-3">{{ $totalPR }}</div>
                                    {{-- <div class="badge bg-white/30">{{$userPercentage}}%</div> --}}
                                </div>
                                <div class="mt-5 flex items-center font-semibold">

                                    {{-- Last Week {{ $lastWeekUsers }} --}}
                                </div>
                            </div>


                            <!-- Product -->
                            <div class="panel bg-gradient-to-r from-violet-500 to-violet-400">
                                <div class="flex justify-between">
                                    <div class="text-md font-semibold ltr:mr-1 rtl:ml-1">Pending PR</div>

                                </div>
                                <div class="mt-5 flex items-center">
                                    <div class="text-3xl font-bold ltr:mr-3 rtl:ml-3">{{ $pendingPR }}</div>
                                    {{-- <div class="badge bg-white/30">{{$productPercentage}}%</div> --}}
                                </div>
                                <div class="mt-5 flex items-center font-semibold">
                                    {{-- Last Week {{ $lastWeekFeedbacks }} --}}
                                </div>
                            </div>

                            <!-- Orders -->
                            <div class="panel bg-gradient-to-r from-blue-500 to-blue-400">
                                <div class="flex justify-between">
                                    <div class="text-md font-semibold ltr:mr-1 rtl:ml-1">Approved PR</div>

                                </div>
                                <div class="mt-5 flex items-center">
                                    <div class="text-3xl font-bold ltr:mr-3 rtl:ml-3">{{ $approvedPR }}</div>
                                    {{-- <div class="badge bg-white/30">{{$orderPercentage}}%</div> --}}
                                </div>
                                <div class="mt-5 flex items-center font-semibold">

                                    {{-- Last Week {{ $lastWeekInquiries }} --}}
                                </div>
                            </div>
                            {{-- Survey --}}
                            <div class="panel bg-gradient-to-r from-fuchsia-500 to-fuchsia-400">
                                <div class="flex justify-between">
                                    <div class="text-md font-semibold ltr:mr-1 rtl:ml-1">Rejetcted PR</div>

                                </div>
                                <div class="mt-5 flex items-center">
                                    <div class="text-3xl font-bold ltr:mr-3 rtl:ml-3">{{ $rejectedPR }}</div>
                                    {{-- <div class="badge bg-white/30">{{$surveyPercentage}}%</div> --}}
                                </div>
                                <div class="mt-5 flex items-center font-semibold">

                                    {{-- Last Week {{ $lastWeekSurvey }} --}}
                                </div>
                            </div>
                            @if (auth()->check() && auth()->user()->role == 'user')

                                <div class="panel h-full p-0 lg:col-span-5">

                                    <div class="bg-white shadow-md rounded-lg p-6">
                                        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Latest PR</h2>

                                        <div x-data="exportTable" class="overflow-x-auto">
                                            <table id="myTable"
                                                class="min-w-full divide-y divide-gray-200 text-sm text-gray-700">
                                                <thead class="bg-gray-100 text-gray-700">
                                                    <tr>
                                                        <th class="px-4 py-2 text-left">Image</th>
                                                        <th class="px-4 py-2 text-left">Name</th>
                                                        <th class="px-4 py-2 text-left">Email</th>
                                                        <th class="px-4 py-2 text-left">PR Agency</th>
                                                        <th class="px-4 py-2 text-left">Company Name</th>
                                                        <th class="px-4 py-2 text-left">Category</th>
                                                        <th class="px-4 py-2 text-center">Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-100">
                                                    @foreach ($latestPR as $press)
                                                        <tr>
                                                            <td class="px-4 py-2">
                                                                @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                                                    <img src="{{ asset('storage/' . $press->image) }}"
                                                                        class="rounded-full object-contain w-10 h-10"
                                                                        alt="Press Release Image">
                                                                @else
                                                                    <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                                                        class="rounded-full object-cover w-10 h-10"
                                                                        alt="Dummy Image">
                                                                @endif
                                                            </td>
                                                            <td class="px-4 py-2">
                                                                {{ \Illuminate\Support\Str::limit($press->name, 25) }}
                                                            </td>
                                                            <td class="px-4 py-2">{{ $press->email }}</td>
                                                            <td class="px-4 py-2">{{ $press->pr_agency }}</td>
                                                            <td class="px-4 py-2">
                                                                {{ \Illuminate\Support\Str::limit($press->company_name, 25) }}
                                                            </td>
                                                            <td class="px-4 py-2">
                                                                @if ($press->category)
                                                                    {{ \Illuminate\Support\Str::limit($press->category, 15) }}
                                                                @else
                                                                    <span class="text-gray-500">No Category</span>
                                                                @endif
                                                            </td>
                                                            <td>
                                                                @if ($press->status == 'pending')
                                                                    <span
                                                                        class="badge bg-warning text-dark">Pending</span>
                                                                @elseif ($press->status == 'approved')
                                                                    <span class="badge bg-success">Approved</span>
                                                                @elseif ($press->status == 'rejected')
                                                                    <span class="badge bg-danger">Rejected</span>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                            @endif


                        </div>


                    </div>

                </div>


            </div>


        </div>


    </div>
</div>
@if (auth()->check() && auth()->user()->role == 'admin')
    {{-- latest PR for Admin  --}}
    <div class="panel h-full p-0 lg:col-span-5">

        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Latest PR</h2>

            <div x-data="exportTable2" class="overflow-x-auto">
                <table id="myTable2" class="min-w-full divide-y divide-gray-200 text-sm text-gray-700">
                    <thead class="bg-gray-100 text-gray-700">
                        <tr>
                            <th class="px-4 py-2 text-left">Image</th>
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Email</th>
                            <th class="px-4 py-2 text-left">PR Agency</th>
                            <th class="px-4 py-2 text-left">Company Name</th>
                            <th class="px-4 py-2 text-left">Category</th>
                            <th class="px-4 py-2 text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                        @foreach ($allPR as $press)
                            <tr>
                                <td class="px-4 py-2">
                                    @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                        <img src="{{ asset('storage/' . $press->image) }}"
                                            class="rounded-full object-contain w-10 h-10" alt="Press Release Image">
                                    @else
                                        <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                            class="rounded-full object-cover w-10 h-10" alt="Dummy Image">
                                    @endif
                                </td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->name, 25) }}</td>
                                <td class="px-4 py-2">{{ $press->email }}</td>
                                <td class="px-4 py-2">{{ $press->pr_agency }}</td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->company_name, 25) }}
                                </td>
                                <td class="px-4 py-2">
                                    @if ($press->category)
                                        {{ \Illuminate\Support\Str::limit($press->category, 15) }}
                                    @else
                                        <span class="text-gray-500">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    @if ($press->status == 'pending')
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    @elseif ($press->status == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif ($press->status == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>


    </div>

    {{-- pending PR for Admin   --}}
    <div class="panel h-full p-0 lg:col-span-5 mt-5">

        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Latest Pending PR</h2>

            <div x-data="exportTable3" class="overflow-x-auto">
                <table id="myTable3" class="min-w-full divide-y divide-gray-200 text-sm text-gray-700">
                    <thead class="bg-gray-100 text-gray-700">
                        <tr>
                            <th class="px-4 py-2 text-left">Image</th>
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Email</th>
                            <th class="px-4 py-2 text-left">PR Agency</th>
                            <th class="px-4 py-2 text-left">Company Name</th>
                            <th class="px-4 py-2 text-left">Category</th>
                            <th class="px-4 py-2 text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                        @foreach ($pendingPRAdmin as $press)
                            <tr>
                                <td class="px-4 py-2">
                                    @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                        <img src="{{ asset('storage/' . $press->image) }}"
                                            class="rounded-full object-contain w-10 h-10" alt="Press Release Image">
                                    @else
                                        <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                            class="rounded-full object-cover w-10 h-10" alt="Dummy Image">
                                    @endif
                                </td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->name, 25) }}</td>
                                <td class="px-4 py-2">{{ $press->email }}</td>
                                <td class="px-4 py-2">{{ $press->pr_agency }}</td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->company_name, 25) }}
                                </td>
                                <td class="px-4 py-2">
                                    @if ($press->category)
                                        {{ \Illuminate\Support\Str::limit($press->category, 15) }}
                                    @else
                                        <span class="text-gray-500">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    @if ($press->status == 'pending')
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    @elseif ($press->status == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif ($press->status == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>


    </div>
    {{-- latest approved PR dor admin --}}
    <div class="panel h-full p-0 lg:col-span-5 mt-5">

        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Latest Approved PR</h2>

            <div x-data="exportTable4" class="overflow-x-auto">
                <table id="myTable4" class="min-w-full divide-y divide-gray-200 text-sm text-gray-700">
                    <thead class="bg-gray-100 text-gray-700">
                        <tr>
                            <th class="px-4 py-2 text-left">Image</th>
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Email</th>
                            <th class="px-4 py-2 text-left">PR Agency</th>
                            <th class="px-4 py-2 text-left">Company Name</th>
                            <th class="px-4 py-2 text-left">Category</th>
                            <th class="px-4 py-2 text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                        @foreach ($approvedPRAdmin as $press)
                            <tr>
                                <td class="px-4 py-2">
                                    @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                        <img src="{{ asset('storage/' . $press->image) }}"
                                            class="rounded-full object-contain w-10 h-10" alt="Press Release Image">
                                    @else
                                        <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                            class="rounded-full object-cover w-10 h-10" alt="Dummy Image">
                                    @endif
                                </td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->name, 25) }}</td>
                                <td class="px-4 py-2">{{ $press->email }}</td>
                                <td class="px-4 py-2">{{ $press->pr_agency }}</td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->company_name, 25) }}
                                </td>
                                <td class="px-4 py-2">
                                    @if ($press->category)
                                        {{ \Illuminate\Support\Str::limit($press->category, 15) }}
                                    @else
                                        <span class="text-gray-500">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    @if ($press->status == 'pending')
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    @elseif ($press->status == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif ($press->status == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>


    </div>

    {{-- latest rejected PR for admin  --}}
    <div class="panel h-full p-0 lg:col-span-5 mt-5">

        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Latest Rejected PR</h2>

            <div x-data="exportTable5" class="overflow-x-auto">
                <table id="myTable5" class="min-w-full divide-y divide-gray-200 text-sm text-gray-700">
                    <thead class="bg-gray-100 text-gray-700">
                        <tr>
                            <th class="px-4 py-2 text-left">Image</th>
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Email</th>
                            <th class="px-4 py-2 text-left">PR Agency</th>
                            <th class="px-4 py-2 text-left">Company Name</th>
                            <th class="px-4 py-2 text-left">Category</th>
                            <th class="px-4 py-2 text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                        @foreach ($rejectedPRAdmin as $press)
                            <tr>
                                <td class="px-4 py-2">
                                    @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                        <img src="{{ asset('storage/' . $press->image) }}"
                                            class="rounded-full object-contain w-10 h-10" alt="Press Release Image">
                                    @else
                                        <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                            class="rounded-full object-cover w-10 h-10" alt="Dummy Image">
                                    @endif
                                </td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->name, 25) }}</td>
                                <td class="px-4 py-2">{{ $press->email }}</td>
                                <td class="px-4 py-2">{{ $press->pr_agency }}</td>
                                <td class="px-4 py-2">
                                    {{ \Illuminate\Support\Str::limit($press->company_name, 25) }}
                                </td>
                                <td class="px-4 py-2">
                                    @if ($press->category)
                                        {{ \Illuminate\Support\Str::limit($press->category, 15) }}
                                    @else
                                        <span class="text-gray-500">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    @if ($press->status == 'pending')
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    @elseif ($press->status == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif ($press->status == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>


    </div>
@endif
<!-- end main content section -->
</div>


<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exportTable', () => ({
            datatable: null,
            init() {
                this.datatable = new simpleDatatables.DataTable('#myTable', {
                    data: {
                        headings: ['Image', 'Name', 'Email', 'PR Agency', 'Company Name',
                            'Category', 'Status'
                        ],
                        data: []
                    },
                    perPage: 10,
                    perPageSelect: [10, 20, 30, 50, 100],
                    columns: [{
                            select: 0,
                            sort: false
                        },
                        {
                            select: 1,
                            sort: false
                        },
                        {
                            select: 2,
                            sort: false
                        },
                        {
                            select: 3,
                            sort: false
                        },
                        {
                            select: 4,
                            sort: false
                        },
                        {
                            select: 5,
                            sort: false
                        },
                        {
                            select: 6,
                            sort: false
                        },


                    ],
                    firstLast: true,
                });
            }
        }));

        // Initialize DataTables for Global News



    });
</script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exportTable2', () => ({
            datatable: null,
            init() {
                this.datatable = new simpleDatatables.DataTable('#myTable2', {
                    data: {
                        headings: ['Image', 'Name', 'Email', 'PR Agency', 'Company Name',
                            'Category', 'Status'
                        ],
                        data: []
                    },
                    perPage: 10,
                    perPageSelect: [10, 20, 30, 50, 100],
                    columns: [{
                            select: 0,
                            sort: false
                        },
                        {
                            select: 1,
                            sort: false
                        },
                        {
                            select: 2,
                            sort: false
                        },
                        {
                            select: 3,
                            sort: false
                        },
                        {
                            select: 4,
                            sort: false
                        },
                        {
                            select: 5,
                            sort: false
                        },
                        {
                            select: 6,
                            sort: false
                        },


                    ],
                    firstLast: true,
                });
            }
        }));

        // Initialize DataTables for Global News



    });
</script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exportTable3', () => ({
            datatable: null,
            init() {
                this.datatable = new simpleDatatables.DataTable('#myTable3', {
                    data: {
                        headings: ['Image', 'Name', 'Email', 'PR Agency', 'Company Name',
                            'Category', 'Status'
                        ],
                        data: []
                    },
                    perPage: 10,
                    perPageSelect: [10, 20, 30, 50, 100],
                    columns: [{
                            select: 0,
                            sort: false
                        },
                        {
                            select: 1,
                            sort: false
                        },
                        {
                            select: 2,
                            sort: false
                        },
                        {
                            select: 3,
                            sort: false
                        },
                        {
                            select: 4,
                            sort: false
                        },
                        {
                            select: 5,
                            sort: false
                        },
                        {
                            select: 6,
                            sort: false
                        },


                    ],
                    firstLast: true,
                });
            }
        }));

        // Initialize DataTables for Global News



    });
</script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exportTable4', () => ({
            datatable: null,
            init() {
                this.datatable = new simpleDatatables.DataTable('#myTable4', {
                    data: {
                        headings: ['Image', 'Name', 'Email', 'PR Agency', 'Company Name',
                            'Category', 'Status'
                        ],
                        data: []
                    },
                    perPage: 10,
                    perPageSelect: [10, 20, 30, 50, 100],
                    columns: [{
                            select: 0,
                            sort: false
                        },
                        {
                            select: 1,
                            sort: false
                        },
                        {
                            select: 2,
                            sort: false
                        },
                        {
                            select: 3,
                            sort: false
                        },
                        {
                            select: 4,
                            sort: false
                        },
                        {
                            select: 5,
                            sort: false
                        },
                        {
                            select: 6,
                            sort: false
                        },


                    ],
                    firstLast: true,
                });
            }
        }));

        // Initialize DataTables for Global News



    });
</script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exportTable5', () => ({
            datatable: null,
            init() {
                this.datatable = new simpleDatatables.DataTable('#myTable5', {
                    data: {
                        headings: ['Image', 'Name', 'Email', 'PR Agency', 'Company Name',
                            'Category', 'Status'
                        ],
                        data: []
                    },
                    perPage: 10,
                    perPageSelect: [10, 20, 30, 50, 100],
                    columns: [{
                            select: 0,
                            sort: false
                        },
                        {
                            select: 1,
                            sort: false
                        },
                        {
                            select: 2,
                            sort: false
                        },
                        {
                            select: 3,
                            sort: false
                        },
                        {
                            select: 4,
                            sort: false
                        },
                        {
                            select: 5,
                            sort: false
                        },
                        {
                            select: 6,
                            sort: false
                        },


                    ],
                    firstLast: true,
                });
            }
        }));

        // Initialize DataTables for Global News



    });
</script>


@endsection
