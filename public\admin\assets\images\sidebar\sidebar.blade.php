<nav x-data="sidebar"
    class="sidebar fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px] shadow-[5px_0_25px_0_rgba(94,92,154,0.1)] transition-all duration-300">
    <div class="h-full bg-white dark:bg-[#0e1726]">
        <div class="flex items-center justify-between px-4 py-3">
            <a href="{{ route('dashboard') }}" class="main-logo flex shrink-0 items-center">
                <img class="ml-[5px] w-20 flex-none" src="{{ asset('admin/assets/images/sidebar/IFL_LOGO_1.png') }}"
                    alt="image" />
            </a>
            <a href="javascript:;"
                class="collapse-icon flex h-8 w-8 items-center rounded-full transition duration-300 hover:bg-gray-500/10 rtl:rotate-180 dark:text-white-light dark:hover:bg-dark-light/10"
                @click="$store.app.toggleSidebar()">
                <svg class="m-auto h-5 w-5" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                    <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </a>
        </div>
        <ul class="perfect-scrollbar relative h-[calc(100vh-80px)] space-y-0.5 overflow-y-auto overflow-x-hidden p-4 py-0 font-semibold"
            x-data="{ activeDropdown: 'dashboard' }">
            {{-- <li class="nav-item">
                <a href="{{ route('dashboard') }}"
                    class="nav-link group {{ Route::currentRouteName() === 'dashboard' || Str::startsWith(Route::currentRouteName(), 'dashboard') ? 'bg-blue-700 text-white dark:bg-blue-700 dark:text-white' : 'bg-white-700 text-black dark:bg-blue-700 dark:text-white' }}">
                    <div class="flex items-center">
                        <svg class="shrink-0 group-hover:!text-primary" width="20" height="20"
                        viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.5"
                            d="M2 12.2039C2 9.91549 2 8.77128 2.5192 7.82274C3.0384 6.87421 3.98695 6.28551 5.88403 5.10813L7.88403 3.86687C9.88939 2.62229 10.8921 2 12 2C13.1079 2 14.1106 2.62229 16.116 3.86687L18.116 5.10812C20.0131 6.28551 20.9616 6.87421 21.4808 7.82274C22 8.77128 22 9.91549 22 12.2039V13.725C22 17.6258 22 19.5763 20.8284 20.7881C19.6569 22 17.7712 22 14 22H10C6.22876 22 4.34315 22 3.17157 20.7881C2 19.5763 2 17.6258 2 13.725V12.2039Z"
                            fill="currentColor" />
                        <path
                            d="M9 17.25C8.58579 17.25 8.25 17.5858 8.25 18C8.25 18.4142 8.58579 18.75 9 18.75H15C15.4142 18.75 15.75 18.4142 15.75 18C15.75 17.5858 15.4142 17.25 15 17.25H9Z"
                            fill="currentColor" />
                    </svg>
                        <span class="text-black ltr:pl-3 rtl:pr-3 dark:text-[#506690] dark:group-hover:text-white-dark f-5" >Dashboard</span>
                    </div>
                </a>
            </li> --}}
            <li class="nav-item">
                <a href="{{ route('dashboard') }}" class="nav-link group"
                    style="background-color: {{ Route::currentRouteName() === 'dashboard' ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'dashboard' ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <img src="{{ Route::currentRouteName() === 'dashboard' ? asset('admin/assets/images/sidebar/icons8-home-48 (1).png') : asset('admin/assets/images/sidebar/icons8-home-48.png') }}"
                            width="20" height="20" alt="Dashboard Icon">
                        <span class="ltr:pl-3 rtl:pr-3 f-5"
                            style="color: {{ Route::currentRouteName() === 'dashboard' ? 'white' : 'black' }};">
                            Dashboard
                        </span>
                    </div>
                </a>
            </li>

            </li>



            <h2
                class="-mx-4 mb-1 flex items-center bg-white-light/30 px-7 py-3 font-extrabold uppercase dark:bg-dark dark:bg-opacity-[0.08]">
                <svg class="hidden h-5 w-4 flex-none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"
                    fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                {{-- <span>Apps</span> --}}
            </h2>
            <li class="nav-item">
                <ul>
                    <li class="nav-item">
                        <a href="{{ route('user.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'user.index' || Str::startsWith(Route::currentRouteName(), 'user.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'user.index' || Str::startsWith(Route::currentRouteName(), 'user.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'user.index' || Str::startsWith(Route::currentRouteName(), 'user.') ? asset('admin/assets/images/sidebar/active.png') : asset('admin/assets/images/sidebar/inactive.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'user.index' || Str::startsWith(Route::currentRouteName(), 'user.') ? 'white' : 'black' }};">
                                    User
                                </span>
                            </div>
                        </a>
                    </li>


                    <li class="nav-item">
                        <a href="{{ route('category.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'category.index' || Str::startsWith(Route::currentRouteName(), 'category.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'category.index' || Str::startsWith(Route::currentRouteName(), 'category.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'category.index' || Str::startsWith(Route::currentRouteName(), 'category.') ? asset('admin/assets/images/sidebar/active-categories-icon.png') : asset('admin/assets/images/sidebar/inactive-category.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'category.index' || Str::startsWith(Route::currentRouteName(), 'category.') ? 'white' : 'black' }};">
                                    Category
                                </span>
                            </div>
                        </a>
                    </li>


                    <li class="nav-item">
                        <a href="{{ route('product.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'product.index' || Str::startsWith(Route::currentRouteName(), 'product.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'product.index' || Str::startsWith(Route::currentRouteName(), 'product.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'product.index' || Str::startsWith(Route::currentRouteName(), 'product.') ? asset('admin/assets/images/sidebar/icons8-product-30.png') : asset('admin/assets/images/sidebar/inactive-sddd.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'product.index' || Str::startsWith(Route::currentRouteName(), 'product.') ? 'white' : 'black' }};">
                                    Product
                                </span>
                            </div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('testimonial.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'testimonial.index' || Str::startsWith(Route::currentRouteName(), 'testimonial.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'testimonial.index' || Str::startsWith(Route::currentRouteName(), 'testimonial.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'testimonial.index' || Str::startsWith(Route::currentRouteName(), 'testimonial.') ? asset('admin/assets/images/sidebar/icons8-testimonial-64 (2).png') : asset('admin/assets/images/sidebar/icons8-testimonial-64.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'testimonial.index' || Str::startsWith(Route::currentRouteName(), 'testimonial.') ? 'white' : 'black' }};">
                                    Testimonial
                                </span>
                            </div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('article.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'article.index' || Str::startsWith(Route::currentRouteName(), 'article.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'article.index' || Str::startsWith(Route::currentRouteName(), 'article.') ? 'white' : 'black' }};">
                            <div class="flex items-center">

                                <img src="{{ Route::currentRouteName() === 'article.index' || Str::startsWith(Route::currentRouteName(), 'article.') ? asset('admin/assets/images/sidebar/icons8-news-50.png') : asset('admin/assets/images/sidebar/inactivenewws.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'article.index' || Str::startsWith(Route::currentRouteName(), 'article.') ? 'white' : 'black' }};">
                                    News and Article
                                </span>
                            </div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('certificate.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'certificate.index' || Str::startsWith(Route::currentRouteName(), 'certificate.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'certificate.index' || Str::startsWith(Route::currentRouteName(), 'certificate.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'certificate.index' || Str::startsWith(Route::currentRouteName(), 'certificate.') ? asset('admin/assets/images/sidebar/icons8-certificate-100.png') : asset('admin/assets/images/sidebar/inactivecertificate-50.png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'certificate.index' || Str::startsWith(Route::currentRouteName(), 'certificate.') ? 'white' : 'black' }};">
                                    Certificate
                                </span>
                            </div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('notifications.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'notifications.index' || Str::startsWith(Route::currentRouteName(), 'notifications.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'notifications.index' || Str::startsWith(Route::currentRouteName(), 'notifications.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'notifications.index' || Str::startsWith(Route::currentRouteName(), 'notifications.') ? asset('admin/assets/images/sidebar/icons8-notification-50.png') : asset('admin/assets/images/sidebar/inactive-notification-50 (1).png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'notifications.index' || Str::startsWith(Route::currentRouteName(), 'notifications.') ? 'white' : 'black' }};">
                                    Notifications
                                </span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('order.index') }}" class="nav-link group"
                            style="background-color: {{ Route::currentRouteName() === 'order.index' || Str::startsWith(Route::currentRouteName(), 'order.') ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'order.index' || Str::startsWith(Route::currentRouteName(), 'order.') ? 'white' : 'black' }};">
                            <div class="flex items-center">
                                <img src="{{ Route::currentRouteName() === 'order.index' || Str::startsWith(Route::currentRouteName(), 'order.') ? asset('admin/assets/images/sidebar/icons8-notification-50.png') : asset('admin/assets/images/sidebar/inactive-notification-50 (1).png') }}"
                                    width="20px" height="20px" alt="">
                                <span class="ltr:pl-3 rtl:pr-3"
                                    style="color: {{ Route::currentRouteName() === 'order.index' || Str::startsWith(Route::currentRouteName(), 'order.') ? 'white' : 'black' }};">
                                    Orders
                                </span>
                            </div>
                        </a>
                    </li>

            </li>

            <h2
                class="-mx-4 mb-1 flex items-center bg-white-light/30 px-7 py-3 font-extrabold uppercase dark:bg-dark dark:bg-opacity-[0.08]">
                <svg class="hidden h-5 w-4 flex-none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"
                    fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>

        </ul>
    </div>
</nav>
