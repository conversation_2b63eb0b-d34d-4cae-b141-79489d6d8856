<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('press_releases', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->after('id');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending')->after('press_release_description');
            $table->text('reject_reason')->nullable()->after('status');
            $table->unsignedBigInteger('approved_by')->nullable()->after('reject_reason');
            $table->timestamp('approved_at')->nullable()->after('approved_by');

            // Foreign key constraints (optional but recommended)
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('press_releases', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['approved_by']);
            $table->dropColumn(['user_id', 'status', 'reject_reason', 'approved_by', 'approved_at']);
        });
    }
};
