<style>
    .nav-link {
        position: relative;
        /* Ensure the link is positioned for absolute positioning of elements */
    }

    .nav-link .svg1 {
        display: block;
        /* Show the default icon */
    }

    .nav-link .svg2 {
        display: none;
        /* Hide the hover icon by default */
    }

    .nav-link:hover .svg1,
    .nav-link.active .svg1 {
        display: none !important;
        /* Hide the default icon on hover or when active */
    }

    .nav-link:hover .svg2,
    .nav-link.active .svg2 {
        display: block !important;
        /* Show the hover icon on hover or when active */
    }

    .nav-link:hover {
        background-color: #1968b3 !important;
        /* Set hover background color */
    }

    .nav-link:hover span,
    .nav-link.active span {
        color: white !important;
        /* Change text color to white on hover or when active */
    }
</style>

<nav x-data="sidebar"
    class="sidebar fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px] shadow-[5px_0_25px_0_rgba(94,92,154,0.1)] transition-all duration-300">
    <div class="h-full bg-white">
        <div class="flex items-center justify-between px-4 py-3">
            <a href="{{ route('dashboard') }}" class="main-logo flex shrink-0 items-center">
                <img class="ml-[5px] w-20 flex-none rounded"
                    src="{{ asset('admin/assets/images/faq/submit press release logo.png') }}" style="width: 200px"
                    alt="image" />
            </a>

            <a href="javascript:;"
                class="collapse-icon flex h-8 w-8 items-center rounded-full transition duration-300 hover:bg-gray-500/10 rtl:rotate-180"
                @click="$store.app.toggleSidebar()">
                <svg class="m-auto h-5 w-5" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                    <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </a>
        </div>
        <ul class="perfect-scrollbar relative h-[calc(100vh-80px)] space-y-0.5 overflow-y-auto overflow-x-hidden p-4 py-7 font-semibold"
            x-data="{ activeDropdown: 'dashboard' }">


            <li class="nav-item">
                <a href="{{ route('dashboard') }}" class="nav-link group"
                    style="background-color: {{ Route::currentRouteName() === 'dashboard' ? '#1968b3' : 'transparent' }}; color: {{ Route::currentRouteName() === 'dashboard' ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <!-- Default SVG Icon (inactive state) -->
                        <svg class="svg1" width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            style="display: {{ Route::currentRouteName() === 'dashboard' ? 'none' : 'block' }};">
                            <g id="Dashboard">
                                <path id="Vector"
                                    d="M4.375 2.5C3.34687 2.5 2.5 3.34687 2.5 4.375V5.625C2.5 6.65313 3.34687 7.5 4.375 7.5H7.70833C8.73646 7.5 9.58333 6.65313 9.58333 5.625V4.375C9.58333 3.34687 8.73646 2.5 7.70833 2.5H4.375ZM12.2917 2.5C11.2635 2.5 10.4167 3.34687 10.4167 4.375V9.79167C10.4167 10.8198 11.2635 11.6667 12.2917 11.6667H15.625C16.6531 11.6667 17.5 10.8198 17.5 9.79167V4.375C17.5 3.34687 16.6531 2.5 15.625 2.5H12.2917ZM4.375 3.75H7.70833C8.06104 3.75 8.33333 4.02229 8.33333 4.375V5.625C8.33333 5.97771 8.06104 6.25 7.70833 6.25H4.375C4.02229 6.25 3.75 5.97771 3.75 5.625V4.375C3.75 4.02229 4.02229 3.75 4.375 3.75ZM12.2917 3.75H15.625C15.9777 3.75 16.25 4.02229 16.25 4.375V9.79167C16.25 10.1444 15.9777 10.4167 15.625 10.4167H12.2917C11.939 10.4167 11.6667 10.1444 11.6667 9.79167V4.375C11.6667 4.02229 11.939 3.75 12.2917 3.75ZM4.375 8.33333C3.34687 8.33333 2.5 9.18021 2.5 10.2083V15.625C2.5 16.6531 3.34687 17.5 4.375 17.5H7.70833C8.73646 17.5 9.58333 16.6531 9.58333 15.625V10.2083C9.58333 9.18021 8.73646 8.33333 7.70833 8.33333H4.375ZM4.375 9.58333H7.70833C8.06104 9.58333 8.33333 9.85563 8.33333 10.2083V15.625C8.33333 15.9777 8.06104 16.25 7.70833 16.25H4.375C4.02229 16.25 3.75 15.9777 3.75 15.625V10.2083C3.75 9.85563 4.02229 9.58333 4.375 9.58333ZM12.2917 12.5C11.2635 12.5 10.4167 13.3469 10.4167 14.375V15.625C10.4167 16.6531 11.2635 17.5 12.2917 17.5H15.625C16.6531 17.5 17.5 16.6531 17.5 15.625V14.375C17.5 13.3469 16.6531 12.5 15.625 12.5H12.2917ZM12.2917 13.75H15.625C15.9777 13.75 16.25 14.0223 16.25 14.375V15.625C16.25 15.9777 15.9777 16.25 15.625 16.25H12.2917C11.939 16.25 11.6667 15.9777 11.6667 15.625V14.375C11.6667 14.0223 11.939 13.75 12.2917 13.75Z"
                                    fill="black" />
                            </g>
                        </svg>

                        <!-- Active SVG Icon (when active) -->
                        <svg class="svg2" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                            viewBox="0 0 20 20" fill="none"
                            style="display: {{ Route::currentRouteName() === 'dashboard' ? 'block' : 'none' }};">
                            <path
                                d="M7.70833 7.5H4.375C3.34125 7.5 2.5 6.65875 2.5 5.625V4.375C2.5 3.34125 3.34125 2.5 4.375 2.5H7.70833C8.74208 2.5 9.58333 3.34125 9.58333 4.375V5.625C9.58333 6.65875 8.74208 7.5 7.70833 7.5ZM7.70833 17.5H4.375C3.34125 17.5 2.5 16.6587 2.5 15.625V10.2083C2.5 9.17458 3.34125 8.33333 4.375 8.33333H7.70833C8.74208 8.33333 9.58333 9.17458 9.58333 10.2083V15.625C9.58333 16.6587 8.74208 17.5 7.70833 17.5ZM15.625 17.5H12.2917C11.2579 17.5 10.4167 16.6587 10.4167 15.625V14.375C10.4167 13.3412 11.2579 12.5 12.2917 12.5H15.625C16.6587 12.5 17.5 13.3412 17.5 14.375V15.625C17.5 16.6587 16.6587 17.5 15.625 17.5ZM15.625 11.6667H12.2917C11.2579 11.6667 10.4167 10.8254 10.4167 9.79167V4.375C10.4167 3.34125 11.2579 2.5 12.2917 2.5H15.625C16.6587 2.5 17.5 3.34125 17.5 4.375V9.79167C17.5 10.8254 16.6587 11.6667 15.625 11.6667Z"
                                fill="white" />
                        </svg>

                        <span class="ltr:pl-3 rtl:pr-3 f-5"
                            style="color: {{ Route::currentRouteName() === 'dashboard' ? 'white' : 'black' }};">
                            Dashboard
                        </span>
                    </div>
                </a>
            </li>
            <li class="nav-item mt-3">
                <a href="{{ route('press-releases.index') }}"
                    class="nav-link group {{ Route::currentRouteName() === 'press-releases.index' || Str::startsWith(Route::currentRouteName(), 'press-releases.') ? 'active' : '' }}"
                    style="background-color: {{ Route::currentRouteName() === 'press-releases.index' || Str::startsWith(Route::currentRouteName(), 'press-releases.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'press-releases.index' || Str::startsWith(Route::currentRouteName(), 'press-releases.') ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <!-- Default SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                            fill="none " class="svg1">
                            <path
                                d="M10 2.5C9.21875 2.5 8.55367 2.81547 8.12663 3.2959C7.69958 3.77632 7.5 4.39236 7.5 5C7.5 5.60764 7.69958 6.22368 8.12663 6.7041C8.55367 7.18453 9.21875 7.5 10 7.5C10.7812 7.5 11.4463 7.18453 11.8734 6.7041C12.3004 6.22368 12.5 5.60764 12.5 5C12.5 4.39236 12.3004 3.77632 11.8734 3.2959C11.4463 2.81547 10.7812 2.5 10 2.5ZM4.58333 3.33333C3.94097 3.33333 3.38006 3.59672 3.02246 3.99902C2.66486 4.40132 2.5 4.91319 2.5 5.41667C2.5 5.92014 2.66486 6.43201 3.02246 6.83431C3.38006 7.23661 3.94097 7.5 4.58333 7.5C5.22569 7.5 5.7866 7.23661 6.14421 6.83431C6.50181 6.43201 6.66667 5.92014 6.66667 5.41667C6.66667 4.91319 6.50181 4.40132 6.14421 3.99902C5.7866 3.59672 5.22569 3.33333 4.58333 3.33333ZM15.4167 3.33333C14.7743 3.33333 14.2134 3.59672 13.8558 3.99902C13.4982 4.40132 13.3333 4.91319 13.3333 5.41667C13.3333 5.92014 13.4982 6.43201 13.8558 6.83431C14.2134 7.23661 14.7743 7.5 15.4167 7.5C16.059 7.5 16.6199 7.23661 16.9775 6.83431C17.3351 6.43201 17.5 5.92014 17.5 5.41667C17.5 4.91319 17.3351 4.40132 16.9775 3.99902C16.6199 3.59672 16.059 3.33333 15.4167 3.33333ZM10 3.75C10.4687 3.75 10.7412 3.90328 10.9391 4.12598C11.1371 4.34868 11.25 4.67014 11.25 5C11.25 5.32986 11.1371 5.65132 10.9391 5.87402C10.7412 6.09672 10.4687 6.25 10 6.25C9.53125 6.25 9.25883 6.09672 9.06087 5.87402C8.86292 5.65132 8.75 5.32986 8.75 5C8.75 4.67014 8.86292 4.34868 9.06087 4.12598C9.25883 3.90328 9.53125 3.75 10 3.75ZM4.58333 4.58333C4.91319 4.58333 5.08145 4.68453 5.20996 4.8291C5.33847 4.97368 5.41667 5.19097 5.41667 5.41667C5.41667 5.64236 5.33847 5.85966 5.20996 6.00423C5.08145 6.14881 4.91319 6.25 4.58333 6.25C4.25347 6.25 4.08521 6.14881 3.95671 6.00423C3.8282 5.85966 3.75 5.64236 3.75 5.41667C3.75 5.19097 3.8282 4.97368 3.95671 4.8291C4.08521 4.68453 4.25347 4.58333 4.58333 4.58333ZM15.4167 4.58333C15.7465 4.58333 15.9148 4.68453 16.0433 4.8291C16.1718 4.97368 16.25 5.19097 16.25 5.41667C16.25 5.64236 16.1718 5.85966 16.0433 6.00423C15.9148 6.14881 15.7465 6.25 15.4167 6.25C15.0868 6.25 14.9185 6.14881 14.79 6.00423C14.6615 5.85966 14.5833 5.64236 14.5833 5.41667C14.5833 5.19097 14.6615 4.97368 14.79 4.8291C14.9185 4.68453 15.0868 4.58333 15.4167 4.58333ZM3.125 8.33333C2.33333 8.33333 1.66667 9 1.66667 9.79167V12.5C1.66667 14.3333 3.16667 15.8333 5 15.8333C5.29167 15.8333 5.58317 15.7913 5.87484 15.708C5.70817 15.333 5.58301 14.917 5.49967 14.5003C5.33301 14.5837 5.16667 14.5833 5 14.5833C3.83333 14.5833 2.91667 13.6667 2.91667 12.5V9.79167C2.91667 9.66667 3 9.58333 3.125 9.58333H5.41667C5.45833 9.125 5.62467 8.66667 5.91634 8.33333H3.125ZM7.70833 8.33333C6.90909 8.33333 6.25 8.99242 6.25 9.79167V13.75C6.25 15.8039 7.94614 17.5 10 17.5C12.0539 17.5 13.75 15.8039 13.75 13.75V9.79167C13.75 8.99242 13.0909 8.33333 12.2917 8.33333H7.70833ZM14.0413 8.33333C14.333 8.66667 14.5002 9.125 14.5418 9.58333H16.8335C16.9585 9.58333 17.0418 9.66667 17.0418 9.79167V12.5C17.0418 13.6667 16.1252 14.5833 14.9585 14.5833C14.7918 14.5833 14.6247 14.5835 14.458 14.5418C14.3747 14.9585 14.2503 15.3753 14.0837 15.7503C14.3753 15.8337 14.6668 15.8748 14.9585 15.8748C16.7918 15.8748 18.2918 14.3748 18.2918 12.5415V9.83317C18.3335 8.99984 17.6667 8.33333 16.875 8.33333H14.0413ZM7.70833 9.58333H12.2917C12.4091 9.58333 12.5 9.67424 12.5 9.79167V13.75C12.5 15.1128 11.3628 16.25 10 16.25C8.63719 16.25 7.5 15.1128 7.5 13.75V9.79167C7.5 9.67424 7.59091 9.58333 7.70833 9.58333Z"
                                fill="black" />
                        </svg>

                        <!-- Hover SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                            fill="none" class="svg2">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M10 2.5C9.33696 2.5 8.70107 2.76339 8.23223 3.23223C7.76339 3.70107 7.5 4.33696 7.5 5C7.5 5.66304 7.76339 6.29893 8.23223 6.76777C8.70107 7.23661 9.33696 7.5 10 7.5C10.663 7.5 11.2989 7.23661 11.7678 6.76777C12.2366 6.29893 12.5 5.66304 12.5 5C12.5 4.33696 12.2366 3.70107 11.7678 3.23223C11.2989 2.76339 10.663 2.5 10 2.5ZM4.58333 3.33333C4.0308 3.33333 3.50089 3.55283 3.11019 3.94353C2.71949 4.33423 2.5 4.86413 2.5 5.41667C2.5 5.9692 2.71949 6.4991 3.11019 6.88981C3.50089 7.28051 4.0308 7.5 4.58333 7.5C5.13587 7.5 5.66577 7.28051 6.05647 6.88981C6.44717 6.4991 6.66667 5.9692 6.66667 5.41667C6.66667 4.86413 6.44717 4.33423 6.05647 3.94353C5.66577 3.55283 5.13587 3.33333 4.58333 3.33333ZM15.4167 3.33333C14.8641 3.33333 14.3342 3.55283 13.9435 3.94353C13.5528 4.33423 13.3333 4.86413 13.3333 5.41667C13.3333 5.9692 13.5528 6.4991 13.9435 6.88981C14.3342 7.28051 14.8641 7.5 15.4167 7.5C15.9692 7.5 16.4991 7.28051 16.8898 6.88981C17.2805 6.4991 17.5 5.9692 17.5 5.41667C17.5 4.86413 17.2805 4.33423 16.8898 3.94353C16.4991 3.55283 15.9692 3.33333 15.4167 3.33333ZM10 17.5C7.9325 17.5 6.25 15.8175 6.25 13.75V9.79167C6.25 8.9875 6.90417 8.33333 7.70833 8.33333H12.2917C13.0958 8.33333 13.75 8.9875 13.75 9.79167V13.75C13.75 15.8175 12.0675 17.5 10 17.5ZM5.41667 13.75V9.79167C5.41667 9.23833 5.61417 8.73 5.94208 8.33333H3.125C2.32083 8.33333 1.66667 8.9875 1.66667 9.79167V12.5C1.66667 14.3375 3.1625 15.8333 5 15.8333C5.29583 15.8333 5.58333 15.7958 5.85833 15.7208C5.85833 15.7208 5.86292 15.7188 5.86667 15.7175C5.58125 15.1204 5.41667 14.4546 5.41667 13.75ZM14.5833 13.75V9.79167C14.5833 9.23833 14.3858 8.73 14.0579 8.33333H16.875C17.6792 8.33333 18.3333 8.9875 18.3333 9.79167V12.5C18.3333 14.3375 16.8375 15.8333 15 15.8333C14.7042 15.8333 14.4167 15.7958 14.1417 15.7208C14.1417 15.7208 14.1371 15.7188 14.1333 15.7175C14.4187 15.1204 14.5833 14.4546 14.5833 13.75Z"
                                fill="white" />
                        </svg>

                        <span class="ms-3"> Press Releases</span>
                    </div>
                </a>
            </li>

            {{-- Press --}}
            {{-- pending PR  --}}
            <li class="nav-item mt-3">
                <a href="{{ route('press.pending') }}"
                    class="nav-link group {{ Route::currentRouteName() === 'press.pending' || Str::startsWith(Route::currentRouteName(), 'press.') ? 'active' : '' }}"
                    style="background-color: {{ Route::currentRouteName() === 'press.pending' || Str::startsWith(Route::currentRouteName(), 'press.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'press.pending' || Str::startsWith(Route::currentRouteName(), 'press.') ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <!-- Default SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path
                                d="M12 1.75a10.25 10.25 0 1 0 10.25 10.25A10.262 10.262 0 0 0 12 1.75zm0 18.5A8.25 8.25 0 1 1 20.25 12 8.26 8.26 0 0 1 12 20.25zm.75-13h-1.5v5.25l4.5 2.625.75-1.23-3.75-2.22z" />
                        </svg>


                        <!-- Hover SVG icon -->
                        {{-- <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                            fill="none" class="svg2">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M10 2.5C9.33696 2.5 8.70107 2.76339 8.23223 3.23223C7.76339 3.70107 7.5 4.33696 7.5 5C7.5 5.66304 7.76339 6.29893 8.23223 6.76777C8.70107 7.23661 9.33696 7.5 10 7.5C10.663 7.5 11.2989 7.23661 11.7678 6.76777C12.2366 6.29893 12.5 5.66304 12.5 5C12.5 4.33696 12.2366 3.70107 11.7678 3.23223C11.2989 2.76339 10.663 2.5 10 2.5ZM4.58333 3.33333C4.0308 3.33333 3.50089 3.55283 3.11019 3.94353C2.71949 4.33423 2.5 4.86413 2.5 5.41667C2.5 5.9692 2.71949 6.4991 3.11019 6.88981C3.50089 7.28051 4.0308 7.5 4.58333 7.5C5.13587 7.5 5.66577 7.28051 6.05647 6.88981C6.44717 6.4991 6.66667 5.9692 6.66667 5.41667C6.66667 4.86413 6.44717 4.33423 6.05647 3.94353C5.66577 3.55283 5.13587 3.33333 4.58333 3.33333ZM15.4167 3.33333C14.8641 3.33333 14.3342 3.55283 13.9435 3.94353C13.5528 4.33423 13.3333 4.86413 13.3333 5.41667C13.3333 5.9692 13.5528 6.4991 13.9435 6.88981C14.3342 7.28051 14.8641 7.5 15.4167 7.5C15.9692 7.5 16.4991 7.28051 16.8898 6.88981C17.2805 6.4991 17.5 5.9692 17.5 5.41667C17.5 4.86413 17.2805 4.33423 16.8898 3.94353C16.4991 3.55283 15.9692 3.33333 15.4167 3.33333ZM10 17.5C7.9325 17.5 6.25 15.8175 6.25 13.75V9.79167C6.25 8.9875 6.90417 8.33333 7.70833 8.33333H12.2917C13.0958 8.33333 13.75 8.9875 13.75 9.79167V13.75C13.75 15.8175 12.0675 17.5 10 17.5ZM5.41667 13.75V9.79167C5.41667 9.23833 5.61417 8.73 5.94208 8.33333H3.125C2.32083 8.33333 1.66667 8.9875 1.66667 9.79167V12.5C1.66667 14.3375 3.1625 15.8333 5 15.8333C5.29583 15.8333 5.58333 15.7958 5.85833 15.7208C5.85833 15.7208 5.86292 15.7188 5.86667 15.7175C5.58125 15.1204 5.41667 14.4546 5.41667 13.75ZM14.5833 13.75V9.79167C14.5833 9.23833 14.3858 8.73 14.0579 8.33333H16.875C17.6792 8.33333 18.3333 8.9875 18.3333 9.79167V12.5C18.3333 14.3375 16.8375 15.8333 15 15.8333C14.7042 15.8333 14.4167 15.7958 14.1417 15.7208C14.1417 15.7208 14.1371 15.7188 14.1333 15.7175C14.4187 15.1204 14.5833 14.4546 14.5833 13.75Z"
                                fill="white" />
                        </svg> --}}

                        <span class="ms-3"> Pending PR</span>
                    </div>
                </a>
            </li>

            {{-- approved PR  --}}
            <li class="nav-item mt-3">
                <a href="{{ route('presses.approved') }}"
                    class="nav-link group {{ Route::currentRouteName() === 'presses.approved' || Str::startsWith(Route::currentRouteName(), 'presses.') ? 'active' : '' }}"
                    style="background-color: {{ Route::currentRouteName() === 'presses.approved' || Str::startsWith(Route::currentRouteName(), 'presses.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'presses.approved' || Str::startsWith(Route::currentRouteName(), 'presses.') ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <!-- Default SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                            viewBox="0 0 24 24" class="svg1">
                            <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1.41 14.59L7 13l1.41-1.41L10.59 14l4.59-4.59L16.59 11 10.59 17z" />
                        </svg>

                        <!-- Hover SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="svg2" width="20" height="20"
                            fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6l4 2" />
                            <circle cx="12" cy="12" r="9" stroke="white" stroke-width="1.5" />
                        </svg>

                        <span class="ms-3"> Approved PR</span>
                    </div>
                </a>
            </li>

            {{-- rejected PR  --}}
            <li class="nav-item mt-3">
                <a href="{{ route('pressing.rejected') }}"
                    class="nav-link group {{ Route::currentRouteName() === 'pressing.rejected' || Str::startsWith(Route::currentRouteName(), 'pressing.') ? 'active' : '' }}"
                    style="background-color: {{ Route::currentRouteName() === 'pressing.rejected' || Str::startsWith(Route::currentRouteName(), 'pressing.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'pressing.rejected' || Str::startsWith(Route::currentRouteName(), 'pressing.') ? 'white' : 'black' }};">
                    <div class="flex items-center">
                        <!-- Default SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="black"
                            viewBox="0 0 24 24" class="svg1">
                            <path
                                d="M12 2C6.477 2 2 6.478 2 12s4.477 10 10 10 10-4.478 10-10S17.523 2 12 2zm3.535 13.536l-1.414 1.414L12 13.414l-2.121 2.122-1.414-1.414L10.586 12 8.464 9.879l1.414-1.414L12 10.586l2.121-2.121 1.414 1.414L13.414 12l2.121 2.121z" />
                        </svg>

                        <!-- Hover SVG icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="white"
                            viewBox="0 0 24 24" class="svg2">
                            <path
                                d="M12 2C6.477 2 2 6.478 2 12s4.477 10 10 10 10-4.478 10-10S17.523 2 12 2zm3.535 13.536l-1.414 1.414L12 13.414l-2.121 2.122-1.414-1.414L10.586 12 8.464 9.879l1.414-1.414L12 10.586l2.121-2.121 1.414 1.414L13.414 12l2.121 2.121z" />
                        </svg>

                        <span class="ms-3"> Rejected PR</span>
                    </div>
                </a>
            </li>

            {{-- terms and condition  --}}

            @php
                $user = Auth::user();
            @endphp
            @if ($user->role == 'admin')
                <li class="nav-item mt-3">
                    <a href="{{ route('terms.create') }}"
                        class="nav-link group {{ Route::currentRouteName() === 'terms.create' || Str::startsWith(Route::currentRouteName(), 'terms.') ? 'active' : '' }}"
                        style="background-color: {{ Route::currentRouteName() === 'terms.create' || Str::startsWith(Route::currentRouteName(), 'terms.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'terms.create' || Str::startsWith(Route::currentRouteName(), 'terms.') ? 'white' : 'black' }};">
                        <div class="flex items-center">
                            <!-- Default SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg1" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M4 4v16c0 .6.4 1 1 1h14c.6 0 1-.4 1-1V8.6c0-.3-.1-.5-.3-.7L15.1 3.3c-.2-.2-.5-.3-.7-.3H5c-.6 0-1 .4-1 1z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M14 3v5h5" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 13h6M9 17h6" />
                            </svg>

                            <!-- Hover SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg2" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M17 3H7a2 2 0 00-2 2v13a2 2 0 002 2h10M17 3v2a2 2 0 002 2h2M17 3v2a2 2 0 01-2 2H7" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6M9 16h3" />
                            </svg>


                            <span class="ms-3">Terms and Condition</span>
                        </div>
                    </a>
                </li>
            @else
                <li class="nav-item mt-3">
                    <a href="{{ route('terms.show') }}"
                        class="nav-link group {{ Route::currentRouteName() === 'terms.show' || Str::startsWith(Route::currentRouteName(), 'terms.') ? 'active' : '' }}"
                        style="background-color: {{ Route::currentRouteName() === 'terms.show' || Str::startsWith(Route::currentRouteName(), 'terms.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'terms.show' || Str::startsWith(Route::currentRouteName(), 'terms.') ? 'white' : 'black' }};">
                        <div class="flex items-center">
                            <!-- Default SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg1" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M4 4v16c0 .6.4 1 1 1h14c.6 0 1-.4 1-1V8.6c0-.3-.1-.5-.3-.7L15.1 3.3c-.2-.2-.5-.3-.7-.3H5c-.6 0-1 .4-1 1z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M14 3v5h5" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 13h6M9 17h6" />
                            </svg>

                            <!-- Hover SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg2" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M17 3H7a2 2 0 00-2 2v13a2 2 0 002 2h10M17 3v2a2 2 0 002 2h2M17 3v2a2 2 0 01-2 2H7" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6M9 16h3" />
                            </svg>


                            <span class="ms-3">Terms and Condition</span>
                        </div>
                    </a>
                </li>
            @endif



            {{-- Contact us  --}}
            @if ($user->role == 'admin')
                <li class="nav-item mt-3">
                    <a href="{{ route('contact.create') }}"
                        class="nav-link group {{ Route::currentRouteName() === 'contact.create' || Str::startsWith(Route::currentRouteName(), 'contact.') ? 'active' : '' }}"
                        style="background-color: {{ Route::currentRouteName() === 'contact.create' || Str::startsWith(Route::currentRouteName(), 'contact.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'contact.create' || Str::startsWith(Route::currentRouteName(), 'contact.') ? 'white' : 'black' }};">
                        <div class="flex items-center">
                            <!-- Default SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg1" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21 10c0 7.5-9 11-9 11s-9-3.5-9-11a9 9 0 1118 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 12a2 2 0 100-4 2 2 0 000 4z" />
                            </svg>


                            <!-- Hover SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg2" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21 10c0 7.5-9 11-9 11s-9-3.5-9-11a9 9 0 1118 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 12a2 2 0 100-4 2 2 0 000 4z" />
                            </svg>


                            <span class="ms-3">Contact Us</span>
                        </div>
                    </a>
                </li>
            @else
              <li class="nav-item mt-3">
                    <a href="{{ route('contact.show') }}"
                        class="nav-link group {{ Route::currentRouteName() === 'contact.show' || Str::startsWith(Route::currentRouteName(), 'contact.') ? 'active' : '' }}"
                        style="background-color: {{ Route::currentRouteName() === 'contact.show' || Str::startsWith(Route::currentRouteName(), 'contact.') ? '#1968b3' : 'transparent' }};
                               color: {{ Route::currentRouteName() === 'contact.show' || Str::startsWith(Route::currentRouteName(), 'contact.') ? 'white' : 'black' }};">
                        <div class="flex items-center">
                            <!-- Default SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg1" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21 10c0 7.5-9 11-9 11s-9-3.5-9-11a9 9 0 1118 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 12a2 2 0 100-4 2 2 0 000 4z" />
                            </svg>


                            <!-- Hover SVG icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg2" width="20" height="20"
                                fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21 10c0 7.5-9 11-9 11s-9-3.5-9-11a9 9 0 1118 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 12a2 2 0 100-4 2 2 0 000 4z" />
                            </svg>


                            <span class="ms-3">Contact Us</span>
                        </div>
                    </a>
                </li>
            @endif




            </li>

        </ul>
    </div>
</nav>
