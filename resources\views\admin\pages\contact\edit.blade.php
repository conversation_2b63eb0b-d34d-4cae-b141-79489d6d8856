@extends('admin.layouts.master')
@section('title', 'Edit Contact Us')
@section('main-section')
    <link rel="stylesheet" href="{{ asset('flatepicker/flatpickr.min.css') }}">
    <script src="{{ asset('js/flatpickr.min.js') }}"></script>

    <div class="animate__animated p-6" :class="[$store.app.animation]">
        <div>

            <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <ul class="flex space-x-2 rtl:space-x-reverse">
                    <li>
                        <a href="{{ route('contact.index') }}" class="clr hover:underline">Contact Us</a>
                    </li>
                    <li class="before:content-['/'] ltr:before:mr-1 rtl:before:ml-1">
                        <span>Edit</span>
                    </li>
                </ul>

            </div>
            <div class="mt-3" x-data="form">


                <div class="mb-5">

                    <div class="panel">

                        <div class="mb-5">
                            <form action="{{ route('contact.update', $contact->id) }}" method="POST"
                                class="space-y-5" enctype="multipart/form-data">
                                @csrf
                                <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                                    <div>
                                        <label class="required-field" for="browserFname">Name</label>
                                        <input id="browserFname" type="text" name="name"
                                            value="{{ old('name', $contact->name) }}" placeholder="Enter name"
                                            class="form-input" />
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="required-field" for="browserFemail">Email</label>
                                        <input id="browserFemail" type="email" name="email"
                                            value="{{ old('email', $contact->email) }}" placeholder="Enter email"
                                            class="form-input" />
                                        @error('email')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                      <div>
                                        <label class="required-field" for="browserFemail">Subject</label>
                                        <input id="browserFemail" type="text" name="subject"
                                            value="{{ old('subject', $contact->subject) }}" placeholder="Enter subject"
                                            class="form-input" />
                                        @error('subject')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>












                                </div>
                                <div class="form-group">
                                    <label class="required-field" for="editor"> Message </label>
                                    <textarea id="editor" class="tinymce" name="message">{{ old('message', $contact->message) }}</textarea>
                                </div>
                                <span id="editor-error" class="text-danger"></span>
                                @error('message')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                                <div class="flex justify-end mt-4">
                                    <a href="{{ route('contact.index') }}" class="ifl-cancel btn !mt-6">Cancel</a>

                                    <button type="submit" class="ifl btn !mt-6 ms-3">Save</button>

                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    @endsection

    @section('extra-script')
        <!-- Select2 CSS -->
        <link href="{{ asset('js/select2/select2.min.css') }}" rel="stylesheet" />
        <!-- jQuery (Select2 requires jQuery) -->
        <script src="{{ asset('js/jquery/jquery.min.js') }}"></script>
        <script>
            $(document).ready(function() {
                $('.dropdown').select2();
            });
        </script>

        <script>
            function previewImage(event) {
                const file = event.target.files[0];
                const preview = document.getElementById('imagePreview');

                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('hidden');
                    }

                    reader.readAsDataURL(file);
                } else {
                    preview.src = '';
                    preview.classList.add('hidden');
                }
            }
        </script>




        <script type="text/javascript">
            tinymce.init({
                selector: 'textarea.tinymce',
                height: 300,
                menubar: false,
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat |',
                content_css: '//www.tiny.cloud/css/codepen.min.css'
            });
        </script>
        <script>
            const compressImage = async (file, {
                quality = 1,
                type = file.type
            }) => {
                const imageBitmap = await createImageBitmap(file);

                const canvas = document.createElement('canvas');
                canvas.width = imageBitmap.width;
                canvas.height = imageBitmap.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(imageBitmap, 0, 0);

                const blob = await new Promise((resolve) =>
                    canvas.toBlob(resolve, type, quality)
                );

                return new File([blob], file.name, {
                    type: blob.type,
                });
            };

            const input = document.querySelector('.my-image-field');
            input.addEventListener('change', async (e) => {
                const {
                    files
                } = e.target;

                if (!files.length) return;

                const dataTransfer = new DataTransfer();

                for (const file of files) {
                    if (!file.type.startsWith('image')) {
                        dataTransfer.items.add(file);
                        continue;
                    }

                    const compressedFile = await compressImage(file, {
                        quality: 0.5,
                        type: 'image/jpeg',
                    });

                    dataTransfer.items.add(compressedFile);
                }

                e.target.files = dataTransfer.files;
            });
        </script>

    @endsection
