@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Global Styles */
@layer base {
    /* CSS Custom Properties for Design System */
    :root {
        /* Brand Colors */
        --color-brand-50: 240 249 255;
        --color-brand-100: 224 242 254;
        --color-brand-200: 186 230 253;
        --color-brand-300: 125 211 252;
        --color-brand-400: 56 189 248;
        --color-brand-500: 25 104 179;
        --color-brand-600: 15 76 117;
        --color-brand-700: 41 128 209;
        --color-brand-800: 7 89 133;
        --color-brand-900: 12 74 110;

        /* Typography Scale */
        --font-size-xs: 0.75rem;
        --font-size-sm: 0.875rem;
        --font-size-base: 1rem;
        --font-size-lg: 1.125rem;
        --font-size-xl: 1.25rem;
        --font-size-2xl: 1.5rem;
        --font-size-3xl: 1.875rem;
        --font-size-4xl: 2.25rem;
        --font-size-5xl: 3rem;

        /* Spacing Scale */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;
        --spacing-3xl: 4rem;

        /* Border Radius */
        --radius-sm: 0.25rem;
        --radius-md: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-xl: 0.75rem;
        --radius-2xl: 1rem;
        --radius-3xl: 1.5rem;

        /* Shadows */
        --shadow-soft: 0 2px 15px rgba(0, 0, 0, 0.08);
        --shadow-modern: 0 10px 40px rgba(0, 0, 0, 0.1);
        --shadow-modern-lg: 0 20px 60px rgba(0, 0, 0, 0.15);
        --shadow-glow: 0 0 20px rgba(25, 104, 179, 0.3);

        /* Transitions */
        --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* Enhanced Typography */
    html {
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        font-variant-ligatures: common-ligatures;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }

    body {
        @apply font-inter text-gray-900 bg-gray-50;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        line-height: 1.6;
        letter-spacing: -0.01em;
    }

    /* Enhanced Headings */
    h1, h2, h3, h4, h5, h6 {
        @apply font-semibold text-gray-900;
        line-height: 1.2;
        letter-spacing: -0.025em;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    h1 {
        @apply text-4xl lg:text-5xl;
        font-weight: 700;
        letter-spacing: -0.04em;
    }

    h2 {
        @apply text-3xl lg:text-4xl;
        font-weight: 600;
        letter-spacing: -0.03em;
    }

    h3 {
        @apply text-2xl lg:text-3xl;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    h4 {
        @apply text-xl lg:text-2xl;
        font-weight: 600;
    }

    h5 {
        @apply text-lg lg:text-xl;
        font-weight: 600;
    }

    h6 {
        @apply text-base lg:text-lg;
        font-weight: 600;
    }

    /* Enhanced Links */
    a {
        @apply transition-colors duration-200;
        text-decoration: none;
    }

    a:hover {
        text-decoration: none;
    }

    /* Enhanced Form Elements */
    input, textarea, select {
        @apply transition-all duration-200;
    }

    input:focus, textarea:focus, select:focus {
        @apply outline-none ring-2 ring-brand-500 ring-opacity-50;
        box-shadow: 0 0 0 3px rgba(25, 104, 179, 0.1);
    }

    /* Enhanced Buttons */
    button {
        @apply transition-all duration-200;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }

    /* Selection Styling */
    ::selection {
        @apply bg-brand-500 text-white;
    }

    ::-moz-selection {
        @apply bg-brand-500 text-white;
    }
}

@layer components {
    /* Enhanced Card Components */
    .card {
        @apply bg-white rounded-xl shadow-soft border border-gray-100;
        transition: all var(--transition-normal);
    }

    .card-hover {
        @apply card;
        transition: all var(--transition-normal);
    }

    .card-hover:hover {
        @apply shadow-modern-lg transform -translate-y-1;
        border-color: rgba(25, 104, 179, 0.1);
    }

    .card-interactive {
        @apply card-hover cursor-pointer;
    }

    .card-interactive:hover {
        @apply shadow-modern-lg;
        transform: translateY(-4px) scale(1.02);
    }

    /* Enhanced Button Components */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg;
        @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        letter-spacing: 0.025em;
    }

    .btn-primary {
        @apply btn bg-brand-500 text-white hover:bg-brand-600 focus:ring-brand-500;
        box-shadow: 0 4px 14px rgba(25, 104, 179, 0.25);
    }

    .btn-primary:hover {
        @apply transform -translate-y-0.5;
        box-shadow: 0 6px 20px rgba(25, 104, 179, 0.35);
    }

    .btn-secondary {
        @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
    }

    .btn-outline {
        @apply btn border-2 border-brand-500 text-brand-500 hover:bg-brand-500 hover:text-white;
        @apply focus:ring-brand-500;
    }

    .btn-ghost {
        @apply btn text-gray-600 hover:bg-gray-100 hover:text-gray-900;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    .btn-xl {
        @apply px-8 py-4 text-lg;
    }

    /* Enhanced Form Components */
    .form-input {
        @apply w-full px-4 py-3 text-gray-900 bg-white border border-gray-200 rounded-lg;
        @apply focus:border-brand-500 focus:ring-2 focus:ring-brand-500 focus:ring-opacity-20;
        @apply transition-all duration-200 placeholder-gray-400;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    .form-input:focus {
        @apply transform -translate-y-0.5;
        box-shadow: 0 4px 15px rgba(25, 104, 179, 0.1);
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        letter-spacing: 0.025em;
    }

    .form-error {
        @apply text-sm text-red-600 mt-1;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    /* Enhanced Badge Components */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        letter-spacing: 0.025em;
    }

    .badge-primary {
        @apply badge bg-brand-100 text-brand-800;
    }

    .badge-success {
        @apply badge bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply badge bg-yellow-100 text-yellow-800;
    }

    .badge-error {
        @apply badge bg-red-100 text-red-800;
    }

    .badge-neutral {
        @apply badge bg-gray-100 text-gray-800;
    }

    /* Enhanced Navigation Components */
    .nav-link {
        @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    .nav-link:hover {
        @apply bg-gray-100 text-gray-900;
    }

    .nav-link.active {
        @apply bg-brand-500 text-white;
        box-shadow: 0 4px 14px rgba(25, 104, 179, 0.25);
    }

    /* Enhanced Table Components */
    .table {
        @apply w-full text-sm text-left text-gray-500;
    }

    .table th {
        @apply px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        letter-spacing: 0.05em;
    }

    .table td {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    .table tbody tr {
        @apply border-b border-gray-200 hover:bg-gray-50 transition-colors duration-150;
    }
}

@layer utilities {
    /* Enhanced Gradient Utilities */
    .gradient-brand {
        background: linear-gradient(135deg, rgb(25, 104, 179) 0%, rgb(41, 128, 209) 100%);
    }

    .gradient-brand-soft {
        background: linear-gradient(135deg, rgba(25, 104, 179, 0.1) 0%, rgba(41, 128, 209, 0.1) 100%);
    }

    .gradient-text-brand {
        background: linear-gradient(135deg, rgb(25, 104, 179) 0%, rgb(41, 128, 209) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .gradient-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-sunset {
        background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
    }

    .gradient-ocean {
        background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
    }

    /* Glass Morphism Effects */
    .glass {
        background: rgba(255, 255, 255, 0.25);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .glass-dark {
        background: rgba(0, 0, 0, 0.25);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .glass-strong {
        background: rgba(255, 255, 255, 0.4);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Enhanced Shadow Utilities */
    .shadow-brand {
        box-shadow: 0 10px 25px rgba(25, 104, 179, 0.15);
    }

    .shadow-brand-lg {
        box-shadow: 0 20px 40px rgba(25, 104, 179, 0.2);
    }

    .shadow-colored {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(25, 104, 179, 0.05);
    }

    .shadow-inner-brand {
        box-shadow: inset 0 2px 4px rgba(25, 104, 179, 0.1);
    }

    /* Animation Utilities */
    .animate-float {
        animation: float 6s ease-in-out infinite;
    }

    .animate-pulse-slow {
        animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-gradient {
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
    }

    .animate-shimmer {
        position: relative;
        overflow: hidden;
    }

    .animate-shimmer::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        animation: shimmer 2s linear infinite;
    }

    /* Interactive Utilities */
    .hover-lift {
        transition: transform var(--transition-normal);
    }

    .hover-lift:hover {
        transform: translateY(-4px);
    }

    .hover-scale {
        transition: transform var(--transition-normal);
    }

    .hover-scale:hover {
        transform: scale(1.05);
    }

    .hover-glow {
        transition: all var(--transition-normal);
    }

    .hover-glow:hover {
        box-shadow: 0 0 20px rgba(25, 104, 179, 0.4);
    }

    /* Focus Utilities */
    .focus-brand {
        @apply focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50;
    }

    .focus-visible-brand {
        @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-500 focus-visible:ring-opacity-50;
    }

    /* Text Utilities */
    .text-balance {
        /* Fallback for older browsers */
        word-wrap: break-word;
        -webkit-hyphens: auto;
        hyphens: auto;
    }

    .text-pretty {
        /* Enhanced text rendering */
        word-wrap: break-word;
        -webkit-hyphens: auto;
        hyphens: auto;
        text-rendering: optimizeLegibility;
    }

    /* Layout Utilities */
    .container-fluid {
        @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
    }

    .container-narrow {
        @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .container-wide {
        @apply max-w-8xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Aspect Ratio Utilities */
    .aspect-golden {
        aspect-ratio: 1.618 / 1;
    }

    .aspect-video {
        aspect-ratio: 16 / 9;
    }

    .aspect-square {
        aspect-ratio: 1 / 1;
    }

    /* Modern Border Utilities */
    .border-gradient {
        border: 1px solid transparent;
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(135deg, rgb(25, 104, 179), rgb(41, 128, 209)) border-box;
    }

    .border-gradient-soft {
        border: 1px solid transparent;
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(135deg, rgba(25, 104, 179, 0.3), rgba(41, 128, 209, 0.3)) border-box;
    }
}
