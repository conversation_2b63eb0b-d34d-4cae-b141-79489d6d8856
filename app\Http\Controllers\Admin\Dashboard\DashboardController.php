<?php

namespace App\Http\Controllers\Admin\Dashboard;

use App\Models\User;
use App\Models\PressRelease;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class DashboardController extends Controller
{
    public function dashboard(Request $request)
    {
        $totalUsers = User::where('role', 'user')->count();
        if (auth()->check() && auth()->user()->role == 'admin') {
            $totalPR = PressRelease::where('user_id', Auth::id())->count();
        } else {
            $totalPR = PressRelease::count();
        }



        if (auth()->check() && auth()->user()->role == 'user') {
            $totalPR = PressRelease::where('user_id', Auth::id())->count();
        } else {
            $totalPR = PressRelease::count();
        }

        if (auth()->check() && auth()->user()->role == 'user') {
            $pendingPR = PressRelease::where('user_id', Auth::id())
                ->where('status', 'pending')
                ->count();
        } else {
            $pendingPR = PressRelease::where('status', 'pending')->count();
        }

        if (auth()->check() && auth()->user()->role == 'user') {
            $approvedPR = PressRelease::where('user_id', Auth::id())
                ->where('status', 'approved')
                ->count();
        } else {
            $approvedPR = PressRelease::where('status', 'approved')->count();
        }


        if (auth()->check() && auth()->user()->role == 'user') {
            $rejectedPR = PressRelease::where('user_id', Auth::id())
                ->where('status', 'rejected')
                ->count();
        } else {
            $rejectedPR = PressRelease::where('status', 'rejected')->count();
        }

        $latestPR = PressRelease::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $allPR = PressRelease::take(5)->get();

        $pendingPRAdmin = PressRelease::where('status', 'pending')->take(5)->get();
        $approvedPRAdmin = PressRelease::where('status', 'approved')->take(5)->get();
        $rejectedPRAdmin = PressRelease::where('status', 'rejected')->take(5)->get();
        return view('admin.pages.dashboard', compact('totalPR', 'pendingPR', 'approvedPR', 'rejectedPR', 'latestPR', 'allPR', 'pendingPRAdmin', 'approvedPRAdmin', 'rejectedPRAdmin', 'totalUsers'));
    }
    // terms and condition //
    public function terms()
    {
        return view('admin.pages.terms.index');
    }
    // contact us
    public function contact()
    {
        return view('admin.pages.contact.index');
    }

    public function changePassword()
    {
        return view('profile.partials.update-password-form');
    }

    public function updatePassword(Request $request)
    {
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'old_password' => 'required|string|min:8',
            'new_password' => 'required|string|confirmed|min:8',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->with('error-toast', 'Password update failed!')
                ->withInput();
        }

        $user = Auth::user(); // Logged-in user

        if (!Hash::check($request->old_password, $user->password)) {
            return back()
                ->with('error-toast', 'Old password is incorrect!')
                ->withInput();
        }

        $user->password = Hash::make($request->new_password);
        $user->save();

        Session::flash('success-toast', 'Password updated successfully!');
        return redirect()->back();
    }
}
