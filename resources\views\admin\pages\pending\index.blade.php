@extends('admin.layouts.master')
@section('title', 'Pending')
@section('main-section')


    <div>

        <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3">
            <span class="font-bold ltr:mr-3 rtl:ml-3">Press Releases</span>

            {{-- <a href="{{ route('press-releases.create') }}" class="ifl btn ml-auto">Add</a> --}}
        </div>

        <div class="panel mt-6">
            <div class="mb-5">
                <div>

                </div>

                <div class="flex-1 pt-5 text-sm">
                    <!-- Press Releases Table - displayed only when 'press' tab is active -->
                    <div x-data="exportTable">
                        <table id="myTable">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    {{-- <th>PR Agency</th> --}}
                                    {{-- <th>Telephone</th> --}}
                                    <th>Company Name</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($pressReleases as $press)
                                    <tr>
                                        <td>
                                            @if (!empty($press->image) && file_exists(public_path('storage/' . $press->image)))
                                                <img src="{{ asset('storage/' . $press->image) }}"
                                                    class="rounded-full object-contain w-10 h-10" alt="Press Release Image">
                                            @else
                                                <img src="{{ asset('admin/assets/images/product/product-1.jpg') }}"
                                                    class="rounded-full object-cover w-10 h-10" alt="Dummy Image">
                                            @endif
                                        </td>

                                        <td>{{ \Illuminate\Support\Str::limit($press->name, 25) }}</td>
                                        {{-- <td>{{ \Illuminate\Support\Str::limit($press->heading, 25) }}</td> --}}
                                        <td>{{ $press->email }}
                                        </td>
                                        {{-- <td>{{ $press->pr_agency }}</td> --}}
                                        {{-- <td>{{ $press->telephone }}</td> --}}
                                        <td>{{ Illuminate\Support\Str::limit($press->company_name, 25) ?? $press->company_name }}
                                        </td>
                                        <td>
                                            @if ($press->category)
                                                {{ Illuminate\Support\Str::limit($press->category, 15) ?? $press->category }}
                                            @else
                                                <span class="text-gray-500">No Category</span>
                                            @endif
                                        </td>
                                        <td>
                                                            @if ($press->status == 'pending')
                                                                <span class="badge bg-warning text-dark">Pending</span>
                                                            @elseif ($press->status == 'approved')
                                                                <span class="badge bg-success">Approved</span>
                                                            @elseif ($press->status == 'rejected')
                                                                <span class="badge bg-danger">Rejected</span>
                                                            @endif
                                                        </td>

                                        <td class="align-middle px-4 py-2">
                                            <div class="flex justify-center items-center">
                                                <!-- Edit button -->
                                                @if(auth()->check() && auth()->user()->role == 'user')
                                                <a href="{{ route('press.editPending', $press->id) }}"
                                                    class="text-blue-500 hover:text-blue-700 inline-flex items-center justify-center p-2 rounded-lg transition duration-150 ease-in-out"
                                                    aria-label="Edit">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="inline-block"
                                                        width="20" height="20" viewBox="0 0 24 24">
                                                        <path fill="#1968b3"
                                                            d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04a.996.996 0 0 0 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83l3.75 3.75z" />
                                                    </svg>
                                                </a>

                                                <!-- Delete button -->
                                                <form action="{{ route('press.deletePending', $press->id) }}"
                                                    method="POST" class="delete-form">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="button" class="hover:text-red-700 delete-btn"
                                                        style="color: #ff4d4d;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="inline-block"
                                                            width="20px" height="20px" viewBox="0 0 24 24">
                                                            <path fill="#fb2109"
                                                                d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z" />
                                                        </svg>
                                                    </button>
                                                </form>
                                                @endif
                                                <!-- View button -->
                                                <a href="{{ route('press.showPending', $press->id) }}"
                                                    class="text-blue-500 hover:text-blue-700 inline-flex items-center justify-center p-2 rounded-lg transition duration-150 ease-in-out"
                                                    aria-label="View">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="inline-block"
                                                        width="20" height="20" viewBox="0 0 256 256">
                                                        <path fill="#1968b3"
                                                            d="M247.31 124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57 61.26 162.88 48 128 48S61.43 61.26 36.34 86.35C17.51 105.18 9 124 8.69 124.76a8 8 0 0 0 0 6.5c.35.79 8.82 19.57 27.65 38.4C61.43 194.74 93.12 208 128 208s66.57-13.26 91.66-38.34c18.83-18.83 27.3-37.61 27.65-38.4a8 8 0 0 0 0-6.5M128 168a40 40 0 1 1 40-40a40 40 0 0 1-40 40" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>


@endsection

@section('extra-script')
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('exportTable', () => ({
                datatable: null,
                init() {
                    this.datatable = new simpleDatatables.DataTable('#myTable', {
                        data: {
                            headings: ['Image', 'Name', 'Email', 'Company Name',
                                'Category','Status','Action'
                            ],
                            data: []
                        },
                        perPage: 10,
                        perPageSelect: [10, 20, 30, 50, 100],
                        columns: [{
                                select: 0,
                                sort: false
                            },
                            {
                                select: 1,
                                sort: false
                            },
                            {
                                select: 2,
                                sort: false
                            },
                            {
                                select: 3,
                                sort: false
                            },
                            {
                                select: 4,
                                sort: false
                            },
                            {
                                select: 5,
                                sort: false
                            },
                            {
                                select: 6,
                                sort: false
                            },


                        ],
                        firstLast: true,
                    });
                }
            }));

            // Initialize DataTables for Global News



        });
    </script>



    {{-- // sweet alert for delete --}}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.body.addEventListener('click', function(e) {
                if (e.target.closest('.delete-btn')) {
                    e.preventDefault();
                    const button = e.target.closest('.delete-btn');
                    const form = button.closest('.delete-form');

                    Swal.fire({
                        title: "Do you want to delete this press release?",
                        showDenyButton: true,
                        showCancelButton: false,
                        confirmButtonText: "Yes",
                        confirmButtonColor: '#1968b3',
                        denyButtonColor: '#ff4d4d',
                        denyButtonText: `Cancel`
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                }
            });
        });
    </script>



@endsection
