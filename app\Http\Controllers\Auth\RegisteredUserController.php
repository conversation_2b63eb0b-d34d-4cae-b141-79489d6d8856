<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Notification;
use Illuminate\Auth\Notifications\VerifyEmail;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // dd($request->all());
        // Validate the request data
        $request->validate([
            // 'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
            // Enforce strong password: min 8 chars, upper, lower, number, symbol
            'password' => [
                'required',
                'confirmed',
                'min:8',
                'regex:/[a-z]/',      // at least one lowercase letter
                'regex:/[A-Z]/',      // at least one uppercase letter
                'regex:/[0-9]/',      // at least one digit
                'regex:/[@$!%*#?&]/', // at least one special character
            ],
            [
                'password.confirmed' => 'The password confirmation does not match.',
                'email.unique' => 'This email address is already registered.',
                'password.regex' => 'The password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character.',
                'password.min' => 'The password must be at least 8 characters long.',
                'name.required' => 'The name field is required.',
                'email.required' => 'The email field is required.',
                'email.email' => 'The email must be a valid email address.',
                'email.lowercase' => 'The email must be in lowercase.',
                'email.max' => 'The email may not be greater than 255 characters.',
                'name.max' => 'The name may not be greater than 255 characters.',
            ]
        ]);
          // ✅ Verify CAPTCHA with Cloudflare
    $captchaResponse = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
        'secret' => env('TURNSTILE_SECRET_KEY'),
        'response' => $request->input('cf-turnstile-response'),
        'remoteip' => $request->ip(),
    ]);

    if (!$captchaResponse->json('success')) {
        return back()->withErrors(['captcha' => 'Captcha verification failed. Please try again.'])->withInput();
    }

        $user = User::create([
            // 'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Send email verification notification
        // $user->sendEmailVerificationNotification();
        $verificationLink = URL::temporarySignedRoute(
            'custom.verify.email',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
        );

        // now send this link via mail
        Mail::send('emails.verify_email', ['user' => $user, 'verificationLink' => $verificationLink], function ($message) use ($user) {
            $message->to($user->email)
                ->subject('Verify Your Email Address');
        });


        return redirect()->route('login')->with('success-toast', 'Registration successful! Please check your email to verify your account.');
    }




    public function verify(Request $request, $id, $hash)
    {
        $user = User::findOrFail($id);

        if (!hash_equals($hash, sha1($user->getEmailForVerification()))) {
            abort(403, 'Invalid verification link');
        }

        if ($user->hasVerifiedEmail()) {
            return redirect()->route('login')->with('success-toast', 'Email already verified!');
        }

        $user->email_verified_at = now();
        $user->save();

        return redirect()->route('login')->with('success-toast', 'Your email has been successfully verified!');
    }
}
